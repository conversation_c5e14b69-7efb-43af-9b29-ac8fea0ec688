// ===== Cross-Browser Compatibility Fixes =====

class CrossBrowserCompatibility {
    constructor() {
        this.browserInfo = this.detectBrowser();
        this.init();
    }

    init() {
        console.log(`🌐 تطبيق إصلاحات التوافق للمتصفح: ${this.browserInfo.name} ${this.browserInfo.version}`);
        
        this.addPolyfills();
        this.fixCSSCompatibility();
        this.fixJavaScriptCompatibility();
        this.addBrowserSpecificFixes();
        
        console.log('✅ تم تطبيق إصلاحات التوافق بنجاح');
    }

    detectBrowser() {
        const ua = navigator.userAgent;
        const browser = {
            name: 'Unknown',
            version: 'Unknown',
            engine: 'Unknown',
            isOld: false
        };

        if (ua.includes('Chrome')) {
            browser.name = 'Chrome';
            browser.version = parseInt(ua.match(/Chrome\/(\d+)/)?.[1] || '0');
            browser.engine = 'Blink';
            browser.isOld = browser.version < 80;
        } else if (ua.includes('Firefox')) {
            browser.name = 'Firefox';
            browser.version = parseInt(ua.match(/Firefox\/(\d+)/)?.[1] || '0');
            browser.engine = 'Gecko';
            browser.isOld = browser.version < 75;
        } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
            browser.name = 'Safari';
            browser.version = parseInt(ua.match(/Version\/(\d+)/)?.[1] || '0');
            browser.engine = 'WebKit';
            browser.isOld = browser.version < 13;
        } else if (ua.includes('Edge')) {
            browser.name = 'Edge';
            browser.version = parseInt(ua.match(/Edge\/(\d+)/)?.[1] || '0');
            browser.engine = 'Blink';
            browser.isOld = browser.version < 80;
        }

        return browser;
    }

    addPolyfills() {
        // CustomEvent polyfill for older browsers
        if (typeof CustomEvent !== 'function') {
            function CustomEvent(event, params) {
                params = params || { bubbles: false, cancelable: false, detail: null };
                const evt = document.createEvent('CustomEvent');
                evt.initCustomEvent(event, params.bubbles, params.cancelable, params.detail);
                return evt;
            }
            window.CustomEvent = CustomEvent;
            console.log('✅ CustomEvent polyfill added');
        }

        // Promise polyfill check
        if (typeof Promise === 'undefined') {
            console.warn('⚠️ Promise not supported. Loading polyfill...');
            this.loadPolyfill('https://cdn.jsdelivr.net/npm/es6-promise@4/dist/es6-promise.auto.min.js');
        }

        // Fetch polyfill for older browsers
        if (typeof fetch === 'undefined') {
            console.warn('⚠️ Fetch API not supported. Loading polyfill...');
            this.loadPolyfill('https://cdn.jsdelivr.net/npm/whatwg-fetch@3.6.2/dist/fetch.umd.js');
        }

        // Object.assign polyfill
        if (typeof Object.assign !== 'function') {
            Object.assign = function(target) {
                if (target == null) {
                    throw new TypeError('Cannot convert undefined or null to object');
                }
                const to = Object(target);
                for (let index = 1; index < arguments.length; index++) {
                    const nextSource = arguments[index];
                    if (nextSource != null) {
                        for (const nextKey in nextSource) {
                            if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                                to[nextKey] = nextSource[nextKey];
                            }
                        }
                    }
                }
                return to;
            };
            console.log('✅ Object.assign polyfill added');
        }

        // Array.from polyfill
        if (!Array.from) {
            Array.from = function(arrayLike) {
                const result = [];
                for (let i = 0; i < arrayLike.length; i++) {
                    result.push(arrayLike[i]);
                }
                return result;
            };
            console.log('✅ Array.from polyfill added');
        }

        // String.includes polyfill
        if (!String.prototype.includes) {
            String.prototype.includes = function(search, start) {
                if (typeof start !== 'number') {
                    start = 0;
                }
                if (start + search.length > this.length) {
                    return false;
                } else {
                    return this.indexOf(search, start) !== -1;
                }
            };
            console.log('✅ String.includes polyfill added');
        }
    }

    loadPolyfill(url) {
        const script = document.createElement('script');
        script.src = url;
        script.async = true;
        document.head.appendChild(script);
    }

    fixCSSCompatibility() {
        const style = document.createElement('style');
        let css = '';

        // CSS Grid fallback for older browsers
        if (this.browserInfo.isOld) {
            css += `
                /* Flexbox fallback for CSS Grid */
                .cv-builder-container {
                    display: flex !important;
                    flex-direction: row !important;
                }
                
                .cv-sidebar {
                    flex: 0 0 300px !important;
                }
                
                .cv-editor {
                    flex: 1 !important;
                }
                
                .cv-preview-panel {
                    flex: 0 0 350px !important;
                }
            `;
        }

        // Safari-specific fixes
        if (this.browserInfo.name === 'Safari') {
            css += `
                /* Safari backdrop-filter fix */
                .preview-modal-overlay {
                    -webkit-backdrop-filter: blur(5px);
                }
                
                /* Safari transform fix */
                .cv-preview-document {
                    -webkit-transform-origin: top center;
                }
                
                /* Safari appearance fix */
                input, textarea, select {
                    -webkit-appearance: none;
                    appearance: none;
                }
            `;
        }

        // Firefox-specific fixes
        if (this.browserInfo.name === 'Firefox') {
            css += `
                /* Firefox scrollbar styling */
                * {
                    scrollbar-width: thin;
                    scrollbar-color: #cbd5e1 #f1f5f9;
                }
                
                /* Firefox input styling */
                input[type="number"] {
                    -moz-appearance: textfield;
                }
            `;
        }

        // Edge-specific fixes
        if (this.browserInfo.name === 'Edge') {
            css += `
                /* Edge grid fix */
                .form-grid {
                    display: -ms-grid;
                    -ms-grid-columns: 1fr 1fr;
                    -ms-grid-rows: auto;
                }
            `;
        }

        // General compatibility fixes
        css += `
            /* Cross-browser box-sizing */
            *, *::before, *::after {
                box-sizing: border-box;
            }
            
            /* Cross-browser user-select */
            .no-select {
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
            }
            
            /* Cross-browser transition */
            .transition {
                -webkit-transition: all 0.3s ease;
                -moz-transition: all 0.3s ease;
                -ms-transition: all 0.3s ease;
                -o-transition: all 0.3s ease;
                transition: all 0.3s ease;
            }
            
            /* Cross-browser transform */
            .transform {
                -webkit-transform: translateZ(0);
                -moz-transform: translateZ(0);
                -ms-transform: translateZ(0);
                -o-transform: translateZ(0);
                transform: translateZ(0);
            }
        `;

        style.textContent = css;
        document.head.appendChild(style);
        
        console.log('✅ تم تطبيق إصلاحات CSS للتوافق');
    }

    fixJavaScriptCompatibility() {
        // Fix addEventListener for older IE
        if (!Element.prototype.addEventListener) {
            Element.prototype.addEventListener = function(type, listener) {
                this.attachEvent('on' + type, listener);
            };
        }

        // Fix querySelector for older browsers
        if (!document.querySelector) {
            document.querySelector = function(selector) {
                return document.querySelectorAll(selector)[0] || null;
            };
        }

        // Fix classList for older browsers
        if (!Element.prototype.classList) {
            Element.prototype.classList = {
                add: function(className) {
                    if (!this.contains(className)) {
                        this.className += ' ' + className;
                    }
                },
                remove: function(className) {
                    this.className = this.className.replace(new RegExp('\\b' + className + '\\b', 'g'), '');
                },
                contains: function(className) {
                    return this.className.indexOf(className) !== -1;
                },
                toggle: function(className) {
                    if (this.contains(className)) {
                        this.remove(className);
                    } else {
                        this.add(className);
                    }
                }
            };
        }

        // Fix matches method
        if (!Element.prototype.matches) {
            Element.prototype.matches = 
                Element.prototype.matchesSelector || 
                Element.prototype.mozMatchesSelector ||
                Element.prototype.msMatchesSelector || 
                Element.prototype.oMatchesSelector || 
                Element.prototype.webkitMatchesSelector ||
                function(s) {
                    const matches = (this.document || this.ownerDocument).querySelectorAll(s);
                    let i = matches.length;
                    while (--i >= 0 && matches.item(i) !== this) {}
                    return i > -1;
                };
        }

        // Fix closest method
        if (!Element.prototype.closest) {
            Element.prototype.closest = function(s) {
                let el = this;
                do {
                    if (el.matches(s)) return el;
                    el = el.parentElement || el.parentNode;
                } while (el !== null && el.nodeType === 1);
                return null;
            };
        }

        console.log('✅ تم تطبيق إصلاحات JavaScript للتوافق');
    }

    addBrowserSpecificFixes() {
        // Chrome-specific fixes
        if (this.browserInfo.name === 'Chrome') {
            // Fix for Chrome's aggressive caching
            window.addEventListener('beforeunload', () => {
                if (window.cvBuilder) {
                    window.cvBuilder.saveToLocalStorage();
                }
            });
        }

        // Safari-specific fixes
        if (this.browserInfo.name === 'Safari') {
            // Fix for Safari's date input issues
            document.addEventListener('DOMContentLoaded', () => {
                const dateInputs = document.querySelectorAll('input[type="date"]');
                dateInputs.forEach(input => {
                    if (!input.value) {
                        input.type = 'text';
                        input.placeholder = 'YYYY-MM-DD';
                    }
                });
            });

            // Fix for Safari's file upload issues
            document.addEventListener('change', (e) => {
                if (e.target.type === 'file') {
                    // Force Safari to recognize file changes
                    setTimeout(() => {
                        e.target.dispatchEvent(new Event('input', { bubbles: true }));
                    }, 100);
                }
            });
        }

        // Firefox-specific fixes
        if (this.browserInfo.name === 'Firefox') {
            // Fix for Firefox's PDF generation issues
            window.firefoxPDFWorkaround = true;
        }

        // Edge-specific fixes
        if (this.browserInfo.name === 'Edge') {
            // Fix for Edge's localStorage issues
            try {
                localStorage.setItem('edge_test', 'test');
                localStorage.removeItem('edge_test');
            } catch (e) {
                console.warn('⚠️ localStorage issues detected in Edge');
                // Implement fallback storage
                window.fallbackStorage = {};
            }
        }

        console.log('✅ تم تطبيق الإصلاحات الخاصة بالمتصفح');
    }

    // Method to check if a feature is supported
    isFeatureSupported(feature) {
        const features = {
            'css-grid': () => CSS.supports('display', 'grid'),
            'css-flexbox': () => CSS.supports('display', 'flex'),
            'css-backdrop-filter': () => CSS.supports('backdrop-filter', 'blur(5px)'),
            'js-promises': () => typeof Promise !== 'undefined',
            'js-async-await': () => {
                try {
                    eval('(async () => {})');
                    return true;
                } catch (e) {
                    return false;
                }
            },
            'js-modules': () => 'noModule' in HTMLScriptElement.prototype,
            'file-api': () => typeof FileReader !== 'undefined',
            'canvas': () => {
                const canvas = document.createElement('canvas');
                return !!(canvas.getContext && canvas.getContext('2d'));
            },
            'local-storage': () => {
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    return true;
                } catch (e) {
                    return false;
                }
            }
        };

        return features[feature] ? features[feature]() : false;
    }

    // Method to get browser capabilities report
    getBrowserCapabilities() {
        const capabilities = {
            browser: this.browserInfo,
            features: {
                'CSS Grid': this.isFeatureSupported('css-grid'),
                'CSS Flexbox': this.isFeatureSupported('css-flexbox'),
                'CSS Backdrop Filter': this.isFeatureSupported('css-backdrop-filter'),
                'JavaScript Promises': this.isFeatureSupported('js-promises'),
                'JavaScript Async/Await': this.isFeatureSupported('js-async-await'),
                'JavaScript Modules': this.isFeatureSupported('js-modules'),
                'File API': this.isFeatureSupported('file-api'),
                'Canvas API': this.isFeatureSupported('canvas'),
                'Local Storage': this.isFeatureSupported('local-storage')
            }
        };

        return capabilities;
    }
}

// Initialize cross-browser compatibility
window.CrossBrowserCompatibility = CrossBrowserCompatibility;

// Auto-initialize when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.browserCompatibility = new CrossBrowserCompatibility();
    });
} else {
    window.browserCompatibility = new CrossBrowserCompatibility();
}

console.log('🌐 نظام التوافق عبر المتصفحات جاهز');
