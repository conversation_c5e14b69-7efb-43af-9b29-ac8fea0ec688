// ===== CV Builder - Comprehensive Fixed Version =====

class CVBuilderFixed {
    constructor() {
        this.currentSection = 'personal';
        this.selectedTemplate = 1;
        this.isInitialized = false;
        this.saveTimeout = null;
        this.validationRules = {};
        this.cvData = {
            personal: {
                fullName: '',
                jobTitle: '',
                email: '',
                phone: '',
                city: '',
                website: '',
                linkedin: '',
                github: '',
                photo: null
            },
            summary: '',
            experience: [],
            education: [],
            skills: {
                technical: [],
                soft: []
            },
            languages: []
        };

        this.init();
    }

    async init() {
        try {
            console.log('🚀 تهيئة منشئ السيرة الذاتية المحسن...');

            // Wait for DOM and libraries
            await this.waitForDOMAndLibraries();

            // Initialize components in order
            await this.initializeComponents();

            console.log('✅ تم تهيئة منشئ السيرة الذاتية بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تهيئة منشئ السيرة الذاتية:', error);
            this.showError('حدث خطأ في تحميل التطبيق. يرجى إعادة تحميل الصفحة.');
        }
    }

    async waitForDOMAndLibraries() {
        // Wait for DOM
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }

        // Wait for external libraries
        await this.waitForLibraries();
    }

    async waitForLibraries() {
        const maxWait = 10000; // 10 seconds
        const startTime = Date.now();

        while (Date.now() - startTime < maxWait) {
            if (this.areLibrariesLoaded()) {
                console.log('✅ جميع المكتبات محملة');
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.warn('⚠️ بعض المكتبات قد لا تكون محملة بالكامل');
    }

    areLibrariesLoaded() {
        return (
            (typeof jsPDF !== 'undefined' || typeof window.jsPDF !== 'undefined') &&
            typeof html2canvas !== 'undefined' &&
            typeof QRCode !== 'undefined'
        );
    }

    async initializeComponents() {
        try {
            // Load saved template selection
            this.loadSelectedTemplate();

            // Setup event listeners
            this.setupEventListeners();

            // Load saved data
            this.loadSavedData();

            // Create form sections
            this.createAllSections();

            // Initialize photo upload
            this.initializePhotoUpload();

            // Setup auto-save
            this.setupAutoSave();

            // Setup validation
            this.setupValidation();

            // Update preview and progress
            this.updatePreview();
            this.updateProgress();

            this.isInitialized = true;

            // Show welcome message
            setTimeout(() => {
                this.showNotification('مرحباً! ابدأ في إنشاء سيرتك الذاتية المميزة', 'success');
            }, 1000);

        } catch (error) {
            console.error('❌ خطأ في تهيئة المكونات:', error);
            throw error;
        }
    }

    setupEventListeners() {
        try {
            // Form input listeners
            document.addEventListener('input', (e) => {
                if (e.target.closest('.cv-editor')) {
                    this.handleInputChange(e);
                }
            });

            // Form change listeners
            document.addEventListener('change', (e) => {
                if (e.target.closest('.cv-editor')) {
                    this.handleInputChange(e);
                }
            });

            // Template selection listeners
            document.addEventListener('click', (e) => {
                if (e.target.matches('[data-template]')) {
                    const templateId = parseInt(e.target.dataset.template);
                    this.switchTemplate(templateId);
                }
            });

            // Preview control listeners
            document.addEventListener('click', (e) => {
                if (e.target.matches('[data-action="zoom-in"]')) {
                    this.zoomIn();
                } else if (e.target.matches('[data-action="zoom-out"]')) {
                    this.zoomOut();
                } else if (e.target.matches('[data-action="zoom-reset"]')) {
                    this.resetZoom();
                }
            });

            // Section navigation
            document.addEventListener('click', (e) => {
                if (e.target.matches('.section-nav-btn')) {
                    const section = e.target.dataset.section;
                    this.switchSection(section);
                }
            });

            // Add/remove item buttons
            document.addEventListener('click', (e) => {
                if (e.target.matches('.add-item-btn')) {
                    const section = e.target.dataset.section;
                    this.addItem(section);
                } else if (e.target.matches('.remove-item-btn')) {
                    const section = e.target.dataset.section;
                    const index = parseInt(e.target.dataset.index);
                    this.removeItem(section, index);
                }
            });

            console.log('✅ تم إعداد مستمعي الأحداث');
        } catch (error) {
            console.error('❌ خطأ في إعداد مستمعي الأحداث:', error);
            throw error;
        }
    }

    handleInputChange(e) {
        try {
            // Validate input
            this.validateInput(e.target);

            // Update data
            this.updateDataFromForm();

            // Update preview with debouncing
            clearTimeout(this.updateTimeout);
            this.updateTimeout = setTimeout(() => {
                this.updatePreview();
                this.updateProgress();
            }, 300);

            // Auto-save with debouncing
            clearTimeout(this.saveTimeout);
            this.saveTimeout = setTimeout(() => {
                this.saveToLocalStorage();
            }, 1000);

        } catch (error) {
            console.error('❌ خطأ في معالجة تغيير الإدخال:', error);
        }
    }

    validateInput(input) {
        try {
            const value = input.value.trim();
            const type = input.type;
            const name = input.name;

            // Remove previous validation classes
            input.classList.remove('valid', 'invalid');

            // Clear previous error messages
            const errorElement = input.parentNode.querySelector('.error-message');
            if (errorElement) {
                errorElement.remove();
            }

            let isValid = true;
            let errorMessage = '';

            // Required field validation
            if (input.hasAttribute('required') && !value) {
                isValid = false;
                errorMessage = 'هذا الحقل مطلوب';
            }

            // Email validation
            if (type === 'email' && value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'يرجى إدخال بريد إلكتروني صحيح';
                }
            }

            // Phone validation
            if (name === 'phone' && value) {
                const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
                if (!phoneRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'يرجى إدخال رقم هاتف صحيح';
                }
            }

            // URL validation
            if (type === 'url' && value) {
                try {
                    new URL(value);
                } catch {
                    isValid = false;
                    errorMessage = 'يرجى إدخال رابط صحيح';
                }
            }

            // Apply validation result
            if (isValid) {
                input.classList.add('valid');
            } else {
                input.classList.add('invalid');
                this.showFieldError(input, errorMessage);
            }

            return isValid;
        } catch (error) {
            console.error('❌ خطأ في التحقق من صحة الإدخال:', error);
            return false;
        }
    }

    showFieldError(input, message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        errorElement.textContent = message;
        errorElement.style.cssText = `
            color: #ef4444;
            font-size: 0.8rem;
            margin-top: 4px;
            display: block;
        `;

        input.parentNode.appendChild(errorElement);
    }

    updateDataFromForm() {
        try {
            // Personal information
            const personalInputs = document.querySelectorAll('[data-section="personal"] input, [data-section="personal"] textarea');
            personalInputs.forEach(input => {
                const field = input.name;
                if (field && this.cvData.personal.hasOwnProperty(field)) {
                    this.cvData.personal[field] = input.value.trim();
                }
            });

            // Summary
            const summaryTextarea = document.querySelector('[name="summary"]');
            if (summaryTextarea) {
                this.cvData.summary = summaryTextarea.value.trim();
            }

            // Experience
            this.updateArrayDataFromForm('experience');

            // Education
            this.updateArrayDataFromForm('education');

            // Skills
            this.updateSkillsFromForm();

            // Languages
            this.updateArrayDataFromForm('languages');

        } catch (error) {
            console.error('❌ خطأ في تحديث البيانات من النموذج:', error);
        }
    }

    updateArrayDataFromForm(section) {
        try {
            const items = document.querySelectorAll(`[data-section="${section}"] .item-form`);
            this.cvData[section] = [];

            items.forEach((item, index) => {
                const itemData = {};
                const inputs = item.querySelectorAll('input, textarea, select');

                inputs.forEach(input => {
                    const field = input.name;
                    if (field) {
                        itemData[field] = input.value.trim();
                    }
                });

                // Only add if has meaningful data
                if (this.hasValidData(itemData)) {
                    this.cvData[section].push(itemData);
                }
            });
        } catch (error) {
            console.error(`❌ خطأ في تحديث بيانات ${section}:`, error);
        }
    }

    updateSkillsFromForm() {
        try {
            const technicalInput = document.querySelector('[name="technical-skills"]');
            const softInput = document.querySelector('[name="soft-skills"]');

            if (technicalInput) {
                this.cvData.skills.technical = technicalInput.value
                    .split(',')
                    .map(skill => skill.trim())
                    .filter(skill => skill.length > 0);
            }

            if (softInput) {
                this.cvData.skills.soft = softInput.value
                    .split(',')
                    .map(skill => skill.trim())
                    .filter(skill => skill.length > 0);
            }
        } catch (error) {
            console.error('❌ خطأ في تحديث المهارات:', error);
        }
    }

    hasValidData(obj) {
        return Object.values(obj).some(value =>
            value && typeof value === 'string' && value.trim().length > 0
        );
    }

    updatePreview() {
        try {
            // Dispatch event for preview system
            const event = new CustomEvent('cvDataUpdated', {
                detail: { ...this.cvData },
                bubbles: true
            });
            document.dispatchEvent(event);

            // Also update local preview if exists
            this.updateLocalPreview();

        } catch (error) {
            console.error('❌ خطأ في تحديث المعاينة:', error);
        }
    }

    updateLocalPreview() {
        try {
            const previewContainer = document.querySelector('.cv-preview-document .cv-page');
            if (!previewContainer) return;

            // Use the preview system if available
            if (window.cvPreview && typeof window.cvPreview.renderFullPreview === 'function') {
                window.cvPreview.cvData = { ...this.cvData };
                window.cvPreview.renderFullPreview();
            } else {
                // Fallback to basic preview
                this.renderBasicPreview(previewContainer);
            }
        } catch (error) {
            console.error('❌ خطأ في تحديث المعاينة المحلية:', error);
        }
    }

    renderBasicPreview(container) {
        const data = this.cvData;

        container.innerHTML = `
            <div class="cv-header">
                <div class="cv-photo">
                    ${data.personal.photo ?
                        `<img src="${data.personal.photo}" alt="Profile Photo">` :
                        '<i class="fas fa-user"></i>'
                    }
                </div>
                <div class="cv-basic-info">
                    <h1 class="cv-name">${data.personal.fullName || 'اسمك هنا'}</h1>
                    <h2 class="cv-title">${data.personal.jobTitle || 'المسمى الوظيفي'}</h2>
                    <div class="cv-contact">
                        ${data.personal.email ? `<div class="contact-item"><i class="fas fa-envelope"></i> ${data.personal.email}</div>` : ''}
                        ${data.personal.phone ? `<div class="contact-item"><i class="fas fa-phone"></i> ${data.personal.phone}</div>` : ''}
                        ${data.personal.city ? `<div class="contact-item"><i class="fas fa-map-marker-alt"></i> ${data.personal.city}</div>` : ''}
                    </div>
                </div>
            </div>

            <div class="cv-content">
                ${data.summary ? `
                    <div class="cv-section">
                        <h3>الملخص المهني</h3>
                        <p>${data.summary}</p>
                    </div>
                ` : ''}

                ${data.experience.length > 0 ? `
                    <div class="cv-section">
                        <h3>الخبرات العملية</h3>
                        ${data.experience.map(exp => `
                            <div class="cv-experience-item">
                                <h4>${exp.jobTitle || 'المسمى الوظيفي'}</h4>
                                <div class="company">${exp.company || 'اسم الشركة'}</div>
                                <div class="duration">${exp.startDate || 'تاريخ البداية'} - ${exp.endDate || 'حتى الآن'}</div>
                                ${exp.description ? `<p>${exp.description}</p>` : ''}
                            </div>
                        `).join('')}
                    </div>
                ` : ''}

                ${data.education.length > 0 ? `
                    <div class="cv-section">
                        <h3>التعليم</h3>
                        ${data.education.map(edu => `
                            <div class="cv-education-item">
                                <h4>${edu.degree || 'الدرجة العلمية'}</h4>
                                <div class="institution">${edu.institution || 'اسم المؤسسة'}</div>
                                <div class="year">${edu.year || 'السنة'}</div>
                                ${edu.gpa ? `<div class="gpa">المعدل: ${edu.gpa}</div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                ` : ''}

                ${(data.skills.technical.length > 0 || data.skills.soft.length > 0) ? `
                    <div class="cv-section">
                        <h3>المهارات</h3>
                        ${data.skills.technical.length > 0 ? `
                            <div class="skills-category">
                                <h4>المهارات التقنية</h4>
                                <div class="skills-list">
                                    ${data.skills.technical.map(skill => `<span class="skill-item">${skill}</span>`).join('')}
                                </div>
                            </div>
                        ` : ''}
                        ${data.skills.soft.length > 0 ? `
                            <div class="skills-category">
                                <h4>المهارات الشخصية</h4>
                                <div class="skills-list">
                                    ${data.skills.soft.map(skill => `<span class="skill-item soft">${skill}</span>`).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                ` : ''}

                ${data.languages.length > 0 ? `
                    <div class="cv-section">
                        <h3>اللغات</h3>
                        ${data.languages.map(lang => `
                            <div class="cv-language-item">
                                <span class="language-name">${lang.name || 'اسم اللغة'}</span>
                                <span class="language-level">${lang.level || 'المستوى'}</span>
                            </div>
                        `).join('')}
                    </div>
                ` : ''}
            </div>
        `;
    }

    switchTemplate(templateId) {
        try {
            this.selectedTemplate = templateId;

            // Update active template button
            document.querySelectorAll('[data-template]').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-template="${templateId}"]`)?.classList.add('active');

            // Update preview with new template
            if (window.cvPreview && typeof window.cvPreview.switchTemplate === 'function') {
                window.cvPreview.switchTemplate(templateId);
            }

            // Save template selection
            localStorage.setItem('selected_template_id', templateId.toString());

            this.showNotification(`تم تغيير القالب إلى القالب رقم ${templateId}`, 'success');
        } catch (error) {
            console.error('❌ خطأ في تبديل القالب:', error);
            this.showError('حدث خطأ في تغيير القالب');
        }
    }

    loadSelectedTemplate() {
        try {
            const savedTemplate = localStorage.getItem('selected_template_id');
            if (savedTemplate) {
                this.selectedTemplate = parseInt(savedTemplate);
            }

            // Set active template button
            setTimeout(() => {
                document.querySelectorAll('[data-template]').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`[data-template="${this.selectedTemplate}"]`)?.classList.add('active');
            }, 100);
        } catch (error) {
            console.error('❌ خطأ في تحميل القالب المحفوظ:', error);
        }
    }

    saveToLocalStorage() {
        try {
            const dataToSave = {
                ...this.cvData,
                selectedTemplate: this.selectedTemplate,
                lastSaved: new Date().toISOString(),
                version: '2.0.0'
            };

            localStorage.setItem('elashrafy_cv_data', JSON.stringify(dataToSave));
            console.log('✅ تم حفظ البيانات بنجاح');
        } catch (error) {
            console.error('❌ خطأ في حفظ البيانات:', error);
            this.showError('حدث خطأ في حفظ البيانات');
        }
    }

    loadSavedData() {
        try {
            const savedData = localStorage.getItem('elashrafy_cv_data');
            if (savedData) {
                const parsedData = JSON.parse(savedData);

                // Merge with default structure to ensure all fields exist
                this.cvData = {
                    ...this.cvData,
                    ...parsedData,
                    personal: { ...this.cvData.personal, ...parsedData.personal },
                    skills: { ...this.cvData.skills, ...parsedData.skills }
                };

                if (parsedData.selectedTemplate) {
                    this.selectedTemplate = parsedData.selectedTemplate;
                }

                console.log('✅ تم تحميل البيانات المحفوظة');
                this.populateFormFromData();
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات المحفوظة:', error);
        }
    }

    populateFormFromData() {
        try {
            // Populate personal information
            Object.keys(this.cvData.personal).forEach(field => {
                const input = document.querySelector(`[name="${field}"]`);
                if (input && this.cvData.personal[field]) {
                    input.value = this.cvData.personal[field];
                }
            });

            // Populate summary
            const summaryTextarea = document.querySelector('[name="summary"]');
            if (summaryTextarea && this.cvData.summary) {
                summaryTextarea.value = this.cvData.summary;
            }

            // Populate skills
            const technicalInput = document.querySelector('[name="technical-skills"]');
            if (technicalInput && this.cvData.skills.technical.length > 0) {
                technicalInput.value = this.cvData.skills.technical.join(', ');
            }

            const softInput = document.querySelector('[name="soft-skills"]');
            if (softInput && this.cvData.skills.soft.length > 0) {
                softInput.value = this.cvData.skills.soft.join(', ');
            }

            // Populate array sections
            this.populateArraySection('experience');
            this.populateArraySection('education');
            this.populateArraySection('languages');

        } catch (error) {
            console.error('❌ خطأ في ملء النموذج من البيانات:', error);
        }
    }

    populateArraySection(section) {
        try {
            const container = document.querySelector(`[data-section="${section}"] .items-container`);
            if (!container || !this.cvData[section]) return;

            // Clear existing items
            container.innerHTML = '';

            // Add saved items
            this.cvData[section].forEach((item, index) => {
                this.addItemToForm(section, item, index);
            });

            // Add empty item if none exist
            if (this.cvData[section].length === 0) {
                this.addItemToForm(section, {}, 0);
            }
        } catch (error) {
            console.error(`❌ خطأ في ملء قسم ${section}:`, error);
        }
    }

    createAllSections() {
        try {
            this.createPersonalSection();
            this.createSummarySection();
            this.createExperienceSection();
            this.createEducationSection();
            this.createSkillsSection();
            this.createLanguagesSection();

            console.log('✅ تم إنشاء جميع الأقسام');
        } catch (error) {
            console.error('❌ خطأ في إنشاء الأقسام:', error);
            throw error;
        }
    }

    createPersonalSection() {
        const container = document.querySelector('[data-section="personal"]');
        if (!container) return;

        container.innerHTML = `
            <div class="section-header">
                <h3><i class="fas fa-user"></i> المعلومات الشخصية</h3>
                <p>أدخل معلوماتك الشخصية الأساسية</p>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label for="fullName">الاسم الكامل *</label>
                    <input type="text" id="fullName" name="fullName" required
                           placeholder="أدخل اسمك الكامل">
                </div>

                <div class="form-group">
                    <label for="jobTitle">المسمى الوظيفي *</label>
                    <input type="text" id="jobTitle" name="jobTitle" required
                           placeholder="مثال: مطور ويب">
                </div>

                <div class="form-group">
                    <label for="email">البريد الإلكتروني *</label>
                    <input type="email" id="email" name="email" required
                           placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="phone">رقم الهاتف *</label>
                    <input type="tel" id="phone" name="phone" required
                           placeholder="+966 50 123 4567">
                </div>

                <div class="form-group">
                    <label for="city">المدينة</label>
                    <input type="text" id="city" name="city"
                           placeholder="الرياض، السعودية">
                </div>

                <div class="form-group">
                    <label for="website">الموقع الشخصي</label>
                    <input type="url" id="website" name="website"
                           placeholder="https://yourwebsite.com">
                </div>

                <div class="form-group">
                    <label for="linkedin">LinkedIn</label>
                    <input type="url" id="linkedin" name="linkedin"
                           placeholder="https://linkedin.com/in/yourprofile">
                </div>

                <div class="form-group">
                    <label for="github">GitHub</label>
                    <input type="url" id="github" name="github"
                           placeholder="https://github.com/yourusername">
                </div>
            </div>

            <div class="photo-upload-section">
                <label>الصورة الشخصية</label>
                <div class="photo-upload-area" id="photoUploadArea">
                    <div class="photo-preview" id="photoPreview">
                        <i class="fas fa-camera"></i>
                        <p>انقر لرفع صورة</p>
                    </div>
                    <input type="file" id="photoInput" accept="image/*" style="display: none;">
                </div>
            </div>
        `;
    }

    createSummarySection() {
        const container = document.querySelector('[data-section="summary"]');
        if (!container) return;

        container.innerHTML = `
            <div class="section-header">
                <h3><i class="fas fa-file-text"></i> الملخص المهني</h3>
                <p>اكتب ملخصاً مختصراً عن خبراتك ومهاراتك</p>
            </div>

            <div class="form-group">
                <label for="summary">الملخص المهني</label>
                <textarea id="summary" name="summary" rows="6"
                          placeholder="اكتب ملخصاً مهنياً يبرز خبراتك ومهاراتك الرئيسية..."></textarea>
                <div class="char-counter">
                    <span id="summaryCounter">0</span> / 500 حرف
                </div>
            </div>
        `;

        // Add character counter
        const textarea = container.querySelector('#summary');
        const counter = container.querySelector('#summaryCounter');

        textarea.addEventListener('input', () => {
            const length = textarea.value.length;
            counter.textContent = length;

            if (length > 500) {
                counter.style.color = '#ef4444';
            } else {
                counter.style.color = '#6b7280';
            }
        });
    }

    initializePhotoUpload() {
        try {
            const uploadArea = document.getElementById('photoUploadArea');
            const photoInput = document.getElementById('photoInput');
            const photoPreview = document.getElementById('photoPreview');

            if (!uploadArea || !photoInput || !photoPreview) return;

            // Click to upload
            uploadArea.addEventListener('click', () => {
                photoInput.click();
            });

            // Drag and drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('drag-over');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('drag-over');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handlePhotoUpload(files[0]);
                }
            });

            // File input change
            photoInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.handlePhotoUpload(e.target.files[0]);
                }
            });

            console.log('✅ تم تهيئة رفع الصور');
        } catch (error) {
            console.error('❌ خطأ في تهيئة رفع الصور:', error);
        }
    }

    handlePhotoUpload(file) {
        try {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                this.showError('يرجى اختيار ملف صورة صحيح');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                this.showError('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const imageData = e.target.result;
                this.cvData.personal.photo = imageData;

                // Update preview
                const photoPreview = document.getElementById('photoPreview');
                if (photoPreview) {
                    photoPreview.innerHTML = `
                        <img src="${imageData}" alt="Profile Photo" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">
                        <button type="button" class="remove-photo-btn" onclick="window.cvBuilder.removePhoto()">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                }

                // Update CV preview
                this.updatePreview();

                // Save data
                this.saveToLocalStorage();

                this.showNotification('تم رفع الصورة بنجاح', 'success');
            };

            reader.readAsDataURL(file);
        } catch (error) {
            console.error('❌ خطأ في رفع الصورة:', error);
            this.showError('حدث خطأ في رفع الصورة');
        }
    }

    removePhoto() {
        try {
            this.cvData.personal.photo = null;

            const photoPreview = document.getElementById('photoPreview');
            if (photoPreview) {
                photoPreview.innerHTML = `
                    <i class="fas fa-camera"></i>
                    <p>انقر لرفع صورة</p>
                `;
            }

            this.updatePreview();
            this.saveToLocalStorage();

            this.showNotification('تم حذف الصورة', 'info');
        } catch (error) {
            console.error('❌ خطأ في حذف الصورة:', error);
        }
    }

    showNotification(message, type = 'info') {
        try {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 10000;
                font-family: 'Cairo', sans-serif;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideInRight 0.3s ease-out;
                max-width: 300px;
                word-wrap: break-word;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, 4000);
        } catch (error) {
            console.error('❌ خطأ في عرض الإشعار:', error);
        }
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    // Additional methods for other sections will be added...
}
