/* ===== Advanced Animations ===== */

/* Hero Visual Animations */
.hero-visual {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cv-preview-container {
    position: relative;
    perspective: 1000px;
}

.cv-preview {
    width: 300px;
    height: 400px;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-lg);
    transform: rotateY(-15deg) rotateX(5deg);
    transition: var(--transition-slow);
    animation: cvFloat 4s ease-in-out infinite;
}

.cv-preview:hover {
    transform: rotateY(0deg) rotateX(0deg) scale(1.05);
}

.cv-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.cv-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    animation: avatarPulse 2s ease-in-out infinite;
}

.cv-info {
    flex: 1;
}

.cv-name {
    height: 16px;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
    animation: textShimmer 2s ease-in-out infinite;
}

.cv-title {
    height: 12px;
    width: 80%;
    background: var(--gradient-secondary);
    border-radius: var(--radius-sm);
    animation: textShimmer 2s ease-in-out infinite 0.5s;
}

.cv-sections {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.cv-section {
    height: 40px;
    background: linear-gradient(90deg, #e5e7eb 0%, #f3f4f6 50%, #e5e7eb 100%);
    border-radius: var(--radius-sm);
    animation: sectionLoad 2s ease-in-out infinite;
}

.cv-section:nth-child(1) { animation-delay: 0.2s; }
.cv-section:nth-child(2) { animation-delay: 0.4s; }
.cv-section:nth-child(3) { animation-delay: 0.6s; }

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-icon {
    position: absolute;
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: 1.125rem;
    animation: floatingIcon 3s ease-in-out infinite;
    animation-delay: var(--delay);
}

.floating-icon:nth-child(1) {
    top: 20%;
    right: -20px;
}

.floating-icon:nth-child(2) {
    top: 50%;
    left: -20px;
}

.floating-icon:nth-child(3) {
    bottom: 20%;
    right: -10px;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    animation: bounce 2s infinite;
    cursor: pointer;
    transition: var(--transition-normal);
}

.scroll-arrow:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: scale(1.1);
}

/* Features Section Animations */
.features {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-secondary);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
}

.section-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.feature-card {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: var(--transition-slow);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.feature-card:hover::before {
    left: 100%;
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--text-white);
    position: relative;
    animation: iconPulse 3s ease-in-out infinite;
}

.feature-icon::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0;
    animation: iconRing 3s ease-in-out infinite;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.feature-list {
    list-style: none;
    text-align: right;
}

.feature-list li {
    padding: var(--spacing-sm) 0;
    color: var(--text-secondary);
    position: relative;
    padding-right: var(--spacing-lg);
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--accent-color);
    font-weight: bold;
}

/* Keyframe Animations */
@keyframes cvFloat {
    0%, 100% {
        transform: rotateY(-15deg) rotateX(5deg) translateY(0px);
    }
    50% {
        transform: rotateY(-15deg) rotateX(5deg) translateY(-10px);
    }
}

@keyframes avatarPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes textShimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: 200px 0;
    }
}

@keyframes sectionLoad {
    0% {
        opacity: 0.3;
        transform: translateX(-20px);
    }
    50% {
        opacity: 0.7;
        transform: translateX(0px);
    }
    100% {
        opacity: 0.3;
        transform: translateX(-20px);
    }
}

@keyframes floatingIcon {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-15px) rotate(120deg);
    }
    66% {
        transform: translateY(-5px) rotate(240deg);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes iconRing {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(1.4);
        opacity: 0;
    }
}

/* Fade In Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Loading Animation for Dynamic Content */
@keyframes shimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

.shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 400% 100%;
    animation: shimmer 1.2s ease-in-out infinite;
}

/* Responsive Animations */
@media (max-width: 768px) {
    .cv-preview {
        width: 250px;
        height: 320px;
        transform: none;
    }
    
    .floating-icon {
        display: none;
    }
    
    .feature-card:hover {
        transform: none;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
