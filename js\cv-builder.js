// ===== CV Builder JavaScript - Fixed Version =====

class CVBuilder {
    constructor() {
        this.currentSection = 'personal';
        this.selectedTemplate = null;
        this.isInitialized = false;
        this.cvData = {
            personal: {
                fullName: '',
                jobTitle: '',
                email: '',
                phone: '',
                city: '',
                website: '',
                linkedin: '',
                github: '',
                photo: null
            },
            summary: '',
            experience: [],
            education: [],
            skills: {
                technical: [],
                soft: []
            },
            languages: []
        };

        this.init();
    }

    init() {
        try {
            console.log('🚀 تهيئة منشئ السيرة الذاتية...');

            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initializeComponents());
            } else {
                this.initializeComponents();
            }
        } catch (error) {
            console.error('❌ خطأ في تهيئة منشئ السيرة الذاتية:', error);
            this.showError('حدث خطأ في تحميل التطبيق. يرجى إعادة تحميل الصفحة.');
        }
    }

    initializeComponents() {
        try {
            this.loadSelectedTemplate();
            this.setupEventListeners();
            this.loadSavedData();
            this.createAllSections();
            this.updateProgress();
            this.initializePhotoUpload();
            this.setupAutoSave();
            this.updatePreview();
            this.updateProgress();
            this.isInitialized = true;

            console.log('✅ تم تهيئة منشئ السيرة الذاتية بنجاح');

            // Show welcome message after a short delay
            setTimeout(() => {
                this.showNotification('مرحباً! ابدأ في إنشاء سيرتك الذاتية المميزة', 'success');
            }, 1000);
        } catch (error) {
            console.error('❌ خطأ في تهيئة المكونات:', error);
            this.showError('حدث خطأ في تحميل المكونات. يرجى إعادة تحميل الصفحة.');
        }
    }

    setupEventListeners() {
        try {
            // Section navigation
            document.querySelectorAll('.section-item').forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const section = item.dataset.section;
                    if (section) {
                        this.switchSection(section);
                    }
                });
            });

            // Form inputs with debouncing
            let inputTimeout;
            document.addEventListener('input', (e) => {
                if (e.target.matches('input, textarea, select')) {
                    clearTimeout(inputTimeout);
                    inputTimeout = setTimeout(() => {
                        this.handleInputChange(e.target);
                    }, 300); // Debounce for 300ms
                }
            });

            // Form validation on blur
            document.addEventListener('blur', (e) => {
                if (e.target.matches('input[required], textarea[required]')) {
                    this.validateField(e.target);
                }
            }, true);

            // Photo upload
            const photoInput = document.getElementById('photoInput');
            if (photoInput) {
                photoInput.addEventListener('change', (e) => this.handlePhotoUpload(e));
            }

            // Navigation buttons
            document.addEventListener('click', (e) => {
                if (e.target.matches('[onclick*="nextSection"]') || e.target.closest('[onclick*="nextSection"]')) {
                    e.preventDefault();
                    this.nextSection();
                }
                if (e.target.matches('[onclick*="prevSection"]') || e.target.closest('[onclick*="prevSection"]')) {
                    e.preventDefault();
                    this.prevSection();
                }
            });

            // Auto-save setup
            this.setupAutoSave();

            console.log('✅ تم تهيئة مستمعي الأحداث بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تهيئة مستمعي الأحداث:', error);
        }
    }

    setupAutoSave() {
        // Auto-save every 30 seconds
        setInterval(() => {
            try {
                this.saveToLocalStorage();
            } catch (error) {
                console.error('خطأ في الحفظ التلقائي:', error);
            }
        }, 30000);
    }

    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.getAttribute('placeholder') || field.id;

        // Remove existing error
        const existingError = field.parentElement.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        field.classList.remove('error');

        // Check if required field is empty
        if (field.hasAttribute('required') && !value) {
            this.showFieldError(field, `${fieldName} مطلوب`);
            return false;
        }

        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                this.showFieldError(field, 'يرجى إدخال بريد إلكتروني صحيح');
                return false;
            }
        }

        // Phone validation
        if (field.type === 'tel' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
            if (!phoneRegex.test(value)) {
                this.showFieldError(field, 'يرجى إدخال رقم هاتف صحيح');
                return false;
            }
        }

        // URL validation
        if (field.type === 'url' && value) {
            try {
                new URL(value);
            } catch {
                this.showFieldError(field, 'يرجى إدخال رابط صحيح');
                return false;
            }
        }

        return true;
    }

    showFieldError(field, message) {
        field.classList.add('error');

        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;

        field.parentElement.appendChild(errorDiv);
    }

    createAllSections() {
        // Create all sections at once to avoid issues
        const sections = ['summary', 'experience', 'education', 'skills', 'languages'];

        sections.forEach(sectionName => {
            const existingSection = document.getElementById(`${sectionName}-section`);
            if (!existingSection) {
                this.createSectionContent(sectionName);
            }
        });
    }

    switchSection(sectionName) {
        try {
            // Validate section name
            const validSections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
            if (!validSections.includes(sectionName)) {
                console.error('قسم غير صحيح:', sectionName);
                return;
            }

            // Update sidebar
            document.querySelectorAll('.section-item').forEach(item => {
                item.classList.remove('active');
            });

            const sidebarItem = document.querySelector(`[data-section="${sectionName}"]`);
            if (sidebarItem) {
                sidebarItem.classList.add('active');
            }

            // Update editor content
            document.querySelectorAll('.editor-section').forEach(section => {
                section.style.display = 'none';
            });

            const targetSection = document.getElementById(`${sectionName}-section`);
            if (targetSection) {
                targetSection.style.display = 'block';

                // Re-populate fields when switching sections
                this.populateCurrentSectionFields(sectionName);
            } else {
                this.createSectionContent(sectionName);
            }

            this.currentSection = sectionName;
            this.updateProgress();

            // Scroll to top of editor
            const editor = document.querySelector('.cv-editor');
            if (editor) {
                editor.scrollTop = 0;
            }

            console.log(`✅ تم التبديل إلى قسم: ${sectionName}`);
        } catch (error) {
            console.error('خطأ في تبديل القسم:', error);
            this.showError('حدث خطأ في التنقل بين الأقسام');
        }
    }

    nextSection() {
        const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
        const currentIndex = sections.indexOf(this.currentSection);

        if (currentIndex < sections.length - 1) {
            this.switchSection(sections[currentIndex + 1]);
        } else {
            this.showNotification('لقد وصلت إلى آخر قسم', 'info');
        }
    }

    prevSection() {
        const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
        const currentIndex = sections.indexOf(this.currentSection);

        if (currentIndex > 0) {
            this.switchSection(sections[currentIndex - 1]);
        } else {
            this.showNotification('أنت في أول قسم', 'info');
        }
    }

    showNotification(message, type = 'info') {
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, type);
        } else {
            // Fallback notification
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 10000;
                font-family: 'Cairo', sans-serif;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    // Missing methods that need to be implemented
    loadSelectedTemplate() {
        // Load template from URL or localStorage
        const urlParams = new URLSearchParams(window.location.search);
        const templateId = urlParams.get('template');

        if (templateId) {
            this.selectedTemplate = {
                id: parseInt(templateId),
                loadedAt: Date.now()
            };
            console.log(`✅ تم تحميل القالب رقم ${templateId}`);
        }
    }

    loadSavedData() {
        try {
            const savedData = localStorage.getItem('elashrafy_cv_data');
            if (savedData) {
                const parsedData = JSON.parse(savedData);

                // Merge with default structure to ensure all fields exist
                this.cvData = {
                    personal: { ...this.cvData.personal, ...parsedData.personal },
                    summary: parsedData.summary || '',
                    experience: parsedData.experience || [],
                    education: parsedData.education || [],
                    skills: {
                        technical: parsedData.skills?.technical || [],
                        soft: parsedData.skills?.soft || []
                    },
                    languages: parsedData.languages || []
                };

                console.log('✅ تم تحميل البيانات المحفوظة');
                this.populateFormFields();
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
        }
    }

    populateFormFields() {
        // Populate personal information
        Object.keys(this.cvData.personal).forEach(key => {
            const field = document.getElementById(key) || document.querySelector(`[data-field="${key}"]`);
            if (field && this.cvData.personal[key]) {
                field.value = this.cvData.personal[key];
            }
        });

        // Populate summary
        const summaryField = document.getElementById('professionalSummary');
        if (summaryField && this.cvData.summary) {
            summaryField.value = this.cvData.summary;
        }

        // Initialize photo if exists
        if (this.cvData.personal.photo) {
            const photoPreview = document.getElementById('photoPreview');
            if (photoPreview) {
                photoPreview.innerHTML = `<img src="${this.cvData.personal.photo}" alt="Profile Photo">`;
            }
        }
    }

    createSectionContent(sectionName) {
        const editor = document.querySelector('.cv-editor');
        if (!editor) return;

        let sectionHTML = '';

        switch (sectionName) {
            case 'summary':
                sectionHTML = this.createSummarySection();
                break;
            case 'experience':
                sectionHTML = this.createExperienceSection();
                break;
            case 'education':
                sectionHTML = this.createEducationSection();
                break;
            case 'skills':
                sectionHTML = this.createSkillsSection();
                break;
            case 'languages':
                sectionHTML = this.createLanguagesSection();
                break;
        }

        if (sectionHTML) {
            const sectionDiv = document.createElement('div');
            sectionDiv.className = 'editor-section';
            sectionDiv.id = `${sectionName}-section`;
            sectionDiv.innerHTML = sectionHTML;
            editor.appendChild(sectionDiv);

            // Show the newly created section
            sectionDiv.style.display = 'block';

            // Populate with existing data
            this.populateCurrentSectionFields(sectionName);
        }
    }

    createSummarySection() {
        return `
            <div class="section-header">
                <h2>الملخص المهني</h2>
                <p>اكتب نبذة مختصرة ومؤثرة عن خبراتك وأهدافك المهنية</p>
            </div>

            <div class="form-group">
                <label>الملخص المهني *</label>
                <textarea id="professionalSummary" rows="6"
                    placeholder="مثال: مطور ويب متخصص بخبرة 5 سنوات في تطوير التطبيقات الحديثة باستخدام React و Node.js..."
                    data-field="summary"></textarea>
                <div class="character-count">
                    <span id="summaryCount">0</span> / 500 حرف
                </div>
            </div>

            <div class="section-actions">
                <button class="btn btn-outline" onclick="prevSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: الخبرات العملية
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }

    createExperienceSection() {
        return `
            <div class="section-header">
                <h2>الخبرات العملية</h2>
                <p>أضف خبراتك العملية والوظائف السابقة</p>
            </div>

            <div class="section-content">
                <div id="experienceList" class="items-list">
                    <!-- Experience items will be added here -->
                </div>

                <button class="btn btn-outline btn-add" onclick="addExperience()">
                    <i class="fas fa-plus"></i>
                    إضافة خبرة عملية
                </button>
            </div>

            <div class="section-actions">
                <button class="btn btn-outline" onclick="prevSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: التعليم
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }

    createEducationSection() {
        return `
            <div class="section-header">
                <h2>التعليم</h2>
                <p>أضف مؤهلاتك الأكاديمية والشهادات</p>
            </div>

            <div class="section-content">
                <div id="educationList" class="items-list">
                    <!-- Education items will be added here -->
                </div>

                <button class="btn btn-outline btn-add" onclick="addEducation()">
                    <i class="fas fa-plus"></i>
                    إضافة مؤهل أكاديمي
                </button>
            </div>

            <div class="section-actions">
                <button class="btn btn-outline" onclick="prevSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: المهارات
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }

    createSkillsSection() {
        return `
            <div class="section-header">
                <h2>المهارات</h2>
                <p>أضف مهاراتك التقنية والشخصية</p>
            </div>

            <div class="section-content">
                <div class="skills-container">
                    <div class="skill-category">
                        <h3>المهارات التقنية</h3>
                        <div class="skill-input-group">
                            <input type="text" id="technicalSkillInput" placeholder="مثال: JavaScript, React, Node.js">
                            <button class="btn btn-primary" onclick="addSkill('technical')">
                                <i class="fas fa-plus"></i>
                                إضافة
                            </button>
                        </div>
                        <div id="technicalSkillsList" class="skills-list">
                            <!-- Technical skills will be added here -->
                        </div>
                    </div>

                    <div class="skill-category">
                        <h3>المهارات الشخصية</h3>
                        <div class="skill-input-group">
                            <input type="text" id="softSkillInput" placeholder="مثال: القيادة, التواصل, العمل الجماعي">
                            <button class="btn btn-primary" onclick="addSkill('soft')">
                                <i class="fas fa-plus"></i>
                                إضافة
                            </button>
                        </div>
                        <div id="softSkillsList" class="skills-list">
                            <!-- Soft skills will be added here -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="section-actions">
                <button class="btn btn-outline" onclick="prevSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: اللغات
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }

    createLanguagesSection() {
        return `
            <div class="section-header">
                <h2>اللغات</h2>
                <p>أضف اللغات التي تتقنها ومستوى إتقانك لكل منها</p>
            </div>

            <div class="section-content">
                <div id="languagesList" class="items-list">
                    <!-- Language items will be added here -->
                </div>

                <button class="btn btn-outline btn-add" onclick="addLanguage()">
                    <i class="fas fa-plus"></i>
                    إضافة لغة
                </button>
            </div>

            <div class="section-actions">
                <button class="btn btn-outline" onclick="prevSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-success" onclick="previewCV()">
                    <i class="fas fa-eye"></i>
                    معاينة السيرة الذاتية
                </button>
            </div>
        `;
    }

    // Data handling methods
    handleInputChange(input) {
        const field = input.dataset.field || input.id;
        const value = input.value;

        // Update personal information
        if (this.cvData.personal.hasOwnProperty(field)) {
            this.cvData.personal[field] = value;
        }

        // Update summary
        if (field === 'summary' || input.id === 'professionalSummary') {
            this.cvData.summary = value;

            // Update character count
            const countElement = document.getElementById('summaryCount');
            if (countElement) {
                countElement.textContent = value.length;

                // Change color based on length
                if (value.length < 50) {
                    countElement.style.color = 'var(--danger-color)';
                } else if (value.length > 500) {
                    countElement.style.color = 'var(--warning-color)';
                } else {
                    countElement.style.color = 'var(--success-color)';
                }
            }
        }

        // Update preview and save
        this.updatePreview();
        this.updateProgress();
        this.markSectionCompleted(this.currentSection);

        // Debounced save
        clearTimeout(this.saveTimeout);
        this.saveTimeout = setTimeout(() => {
            this.saveToLocalStorage();
        }, 1000);
    }

    saveToLocalStorage() {
        try {
            const dataToSave = {
                ...this.cvData,
                lastSaved: new Date().toISOString(),
                version: '1.0.0'
            };

            localStorage.setItem('elashrafy_cv_data', JSON.stringify(dataToSave));
            console.log('✅ تم حفظ البيانات بنجاح');
        } catch (error) {
            console.error('❌ خطأ في حفظ البيانات:', error);
            this.showError('حدث خطأ في حفظ البيانات');
        }
    }

    updatePreview() {
        try {
            // Dispatch event for real-time preview updates
            const event = new CustomEvent('cvDataUpdated', {
                detail: this.cvData
            });
            document.dispatchEvent(event);

            const previewContainer = document.querySelector('.cv-preview-document');
            if (!previewContainer) return;

            const data = this.cvData;

            // Generate CV HTML
            const cvHTML = `
                <div class="cv-page">
                    <div class="cv-header">
                        <div class="cv-photo">
                            ${data.personal.photo ?
                                `<img src="${data.personal.photo}" alt="Profile Photo">` :
                                '<i class="fas fa-user"></i>'
                            }
                        </div>
                        <div class="cv-basic-info">
                            <h1 class="cv-name">${data.personal.fullName || 'اسمك هنا'}</h1>
                            <h2 class="cv-title">${data.personal.jobTitle || 'المسمى الوظيفي'}</h2>
                            <div class="cv-contact">
                                ${data.personal.email ? `
                                    <div class="contact-item">
                                        <i class="fas fa-envelope"></i>
                                        <span>${data.personal.email}</span>
                                    </div>
                                ` : ''}
                                ${data.personal.phone ? `
                                    <div class="contact-item">
                                        <i class="fas fa-phone"></i>
                                        <span>${data.personal.phone}</span>
                                    </div>
                                ` : ''}
                                ${data.personal.city ? `
                                    <div class="contact-item">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>${data.personal.city}</span>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>

                    <div class="cv-content">
                        ${data.summary ? `
                            <div class="cv-section">
                                <h3 class="section-title">الملخص المهني</h3>
                                <p class="summary-text">${data.summary}</p>
                            </div>
                        ` : ''}

                        ${data.experience && data.experience.length > 0 ? `
                            <div class="cv-section">
                                <h3 class="section-title">الخبرات العملية</h3>
                                <div class="experience-list">
                                    ${data.experience.map(exp => `
                                        <div class="cv-experience-item">
                                            <h4>${exp.jobTitle || 'المسمى الوظيفي'}</h4>
                                            <div class="company">${exp.company || 'اسم الشركة'}</div>
                                            <div class="duration">
                                                ${exp.startDate || 'تاريخ البداية'} -
                                                ${exp.isCurrent ? 'حتى الآن' : (exp.endDate || 'تاريخ النهاية')}
                                            </div>
                                            ${exp.description ? `<div class="description">${exp.description}</div>` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}

                        ${data.education && data.education.length > 0 ? `
                            <div class="cv-section">
                                <h3 class="section-title">التعليم</h3>
                                <div class="education-list">
                                    ${data.education.map(edu => `
                                        <div class="cv-education-item">
                                            <h4>${edu.degree || 'الدرجة العلمية'}</h4>
                                            <div class="institution">${edu.institution || 'اسم الجامعة'}</div>
                                            <div class="duration">${edu.year || 'سنة التخرج'}</div>
                                            ${edu.gpa ? `<div class="gpa">المعدل: ${edu.gpa}</div>` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}

                        ${(data.skills && (data.skills.technical?.length > 0 || data.skills.soft?.length > 0)) ? `
                            <div class="cv-section">
                                <h3 class="section-title">المهارات</h3>
                                <div class="cv-skills-grid">
                                    ${data.skills.technical ? data.skills.technical.map(skill =>
                                        `<div class="cv-skill-item">${skill}</div>`
                                    ).join('') : ''}
                                    ${data.skills.soft ? data.skills.soft.map(skill =>
                                        `<div class="cv-skill-item soft-skill">${skill}</div>`
                                    ).join('') : ''}
                                </div>
                            </div>
                        ` : ''}

                        ${data.languages && data.languages.length > 0 ? `
                            <div class="cv-section">
                                <h3 class="section-title">اللغات</h3>
                                <div class="languages-list">
                                    ${data.languages.map(lang => `
                                        <div class="cv-language-item">
                                            <span class="language-name">${lang.name || 'اسم اللغة'}</span>
                                            <span class="language-level">${lang.level || 'المستوى'}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

            previewContainer.innerHTML = cvHTML;

        } catch (error) {
            console.error('❌ خطأ في تحديث المعاينة:', error);
        }
    }

    updateProgress() {
        try {
            const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
            let completedSections = 0;

            // Check personal section
            if (this.cvData.personal.fullName && this.cvData.personal.email) {
                completedSections++;
            }

            // Check summary section
            if (this.cvData.summary && this.cvData.summary.length >= 50) {
                completedSections++;
            }

            // Check experience section
            if (this.cvData.experience && this.cvData.experience.length > 0) {
                completedSections++;
            }

            // Check education section
            if (this.cvData.education && this.cvData.education.length > 0) {
                completedSections++;
            }

            // Check skills section
            if (this.cvData.skills && (this.cvData.skills.technical?.length > 0 || this.cvData.skills.soft?.length > 0)) {
                completedSections++;
            }

            // Check languages section
            if (this.cvData.languages && this.cvData.languages.length > 0) {
                completedSections++;
            }

            const progressPercentage = Math.round((completedSections / sections.length) * 100);

            // Update progress bar
            const progressBar = document.querySelector('.progress-fill');
            const progressText = document.querySelector('.progress-text');

            if (progressBar) {
                progressBar.style.width = `${progressPercentage}%`;
            }

            if (progressText) {
                progressText.textContent = `${progressPercentage}% مكتمل`;
            }

            // Update section indicators
            this.updateSectionIndicators(completedSections, sections);

        } catch (error) {
            console.error('خطأ في تحديث التقدم:', error);
        }
    }

    populateCurrentSectionFields(sectionName) {
        try {
            if (sectionName === 'personal') {
                // Populate personal fields
                Object.keys(this.cvData.personal).forEach(key => {
                    const field = document.getElementById(key);
                    if (field && this.cvData.personal[key]) {
                        field.value = this.cvData.personal[key];
                    }
                });
            } else if (sectionName === 'summary') {
                // Populate summary field
                const summaryField = document.getElementById('professionalSummary');
                if (summaryField && this.cvData.summary) {
                    summaryField.value = this.cvData.summary;

                    // Update character count
                    const countElement = document.getElementById('summaryCount');
                    if (countElement) {
                        countElement.textContent = this.cvData.summary.length;

                        if (this.cvData.summary.length < 50) {
                            countElement.style.color = 'var(--danger-color)';
                        } else if (this.cvData.summary.length > 500) {
                            countElement.style.color = 'var(--warning-color)';
                        } else {
                            countElement.style.color = 'var(--success-color)';
                        }
                    }
                }
            } else if (sectionName === 'skills') {
                // Re-render skills lists
                this.renderSkillsList();

                // Setup skill input listeners
                setTimeout(() => {
                    const technicalInput = document.getElementById('technicalSkillInput');
                    const softInput = document.getElementById('softSkillInput');

                    if (technicalInput && !technicalInput.hasAttribute('data-listener')) {
                        technicalInput.setAttribute('data-listener', 'true');
                        technicalInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                this.addSkill('technical');
                            }
                        });
                    }

                    if (softInput && !softInput.hasAttribute('data-listener')) {
                        softInput.setAttribute('data-listener', 'true');
                        softInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                this.addSkill('soft');
                            }
                        });
                    }
                }, 100);
            }
        } catch (error) {
            console.error('خطأ في تحديث حقول القسم:', error);
        }
    }

    initializePhotoUpload() {
        const photoInput = document.getElementById('photoInput');
        const photoPreview = document.getElementById('photoPreview');

        if (photoInput && !photoInput.hasAttribute('data-initialized')) {
            photoInput.setAttribute('data-initialized', 'true');
            photoInput.addEventListener('change', (e) => this.handlePhotoUpload(e));
        }

        // Show existing photo if available
        if (this.cvData.personal.photo && photoPreview) {
            photoPreview.innerHTML = `<img src="${this.cvData.personal.photo}" alt="Profile Photo">`;
        }
    }

    handlePhotoUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.showNotification('يرجى اختيار ملف صورة صحيح', 'error');
            return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            this.showNotification('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.cvData.personal.photo = e.target.result;

            // Update photo preview
            const photoPreview = document.getElementById('photoPreview');
            if (photoPreview) {
                photoPreview.innerHTML = `<img src="${e.target.result}" alt="Profile Photo">`;
            }

            this.updatePreview();
            this.saveToLocalStorage();
            this.showNotification('تم رفع الصورة بنجاح', 'success');
        };

        reader.readAsDataURL(file);
    }

    // Skills management
    renderSkillsList() {
        const technicalList = document.getElementById('technicalSkillsList');
        const softList = document.getElementById('softSkillsList');

        if (technicalList && this.cvData.skills.technical) {
            technicalList.innerHTML = this.cvData.skills.technical.map((skill, index) => `
                <div class="skill-tag">
                    <span>${skill}</span>
                    <button onclick="removeSkill('technical', ${index})" class="remove-skill">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        }

        if (softList && this.cvData.skills.soft) {
            softList.innerHTML = this.cvData.skills.soft.map((skill, index) => `
                <div class="skill-tag">
                    <span>${skill}</span>
                    <button onclick="removeSkill('soft', ${index})" class="remove-skill">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        }
    }

    addSkill(type) {
        const input = document.getElementById(`${type}SkillInput`);
        if (!input) return;

        const skill = input.value.trim();
        if (!skill) {
            this.showNotification('يرجى إدخال اسم المهارة', 'warning');
            return;
        }

        // Check if skill already exists
        if (this.cvData.skills[type].includes(skill)) {
            this.showNotification('هذه المهارة موجودة بالفعل', 'warning');
            return;
        }

        this.cvData.skills[type].push(skill);
        input.value = '';

        this.renderSkillsList();
        this.updatePreview();
        this.updateProgress();
        this.saveToLocalStorage();

        this.showNotification('تم إضافة المهارة بنجاح', 'success');
    }

    removeSkill(type, index) {
        if (confirm('هل أنت متأكد من حذف هذه المهارة؟')) {
            this.cvData.skills[type].splice(index, 1);
            this.renderSkillsList();
            this.updatePreview();
            this.updateProgress();
            this.saveToLocalStorage();
            this.showNotification('تم حذف المهارة بنجاح', 'success');
        }
    }
}

// Global functions for HTML onclick handlers
function nextSection() {
    if (window.cvBuilder) {
        window.cvBuilder.nextSection();
    }
}

function prevSection() {
    if (window.cvBuilder) {
        window.cvBuilder.prevSection();
    }
}

function addSkill(type) {
    if (window.cvBuilder) {
        window.cvBuilder.addSkill(type);
    }
}

function removeSkill(type, index) {
    if (window.cvBuilder) {
        window.cvBuilder.removeSkill(type, index);
    }
}

function addExperience() {
    if (window.cvBuilder) {
        const experienceId = Date.now();
        const experienceItem = {
            id: experienceId,
            jobTitle: '',
            company: '',
            startDate: '',
            endDate: '',
            isCurrent: false,
            description: ''
        };

        window.cvBuilder.cvData.experience.push(experienceItem);
        renderExperienceItem(experienceItem);
        window.cvBuilder.updatePreview();
        window.cvBuilder.updateProgress();
        window.cvBuilder.saveToLocalStorage();
    }
}

function addEducation() {
    if (window.cvBuilder) {
        const educationId = Date.now();
        const educationItem = {
            id: educationId,
            degree: '',
            institution: '',
            year: '',
            gpa: '',
            details: ''
        };

        window.cvBuilder.cvData.education.push(educationItem);
        renderEducationItem(educationItem);
        window.cvBuilder.updatePreview();
        window.cvBuilder.updateProgress();
        window.cvBuilder.saveToLocalStorage();
    }
}

function addLanguage() {
    if (window.cvBuilder) {
        const languageId = Date.now();
        const languageItem = {
            id: languageId,
            name: '',
            level: 'مبتدئ'
        };

        window.cvBuilder.cvData.languages.push(languageItem);
        renderLanguageItem(languageItem);
        window.cvBuilder.updatePreview();
        window.cvBuilder.updateProgress();
        window.cvBuilder.saveToLocalStorage();
    }
}

function renderExperienceItem(experience) {
    const experienceList = document.getElementById('experienceList');
    if (!experienceList) return;

    const experienceDiv = document.createElement('div');
    experienceDiv.className = 'experience-item';
    experienceDiv.dataset.id = experience.id;

    experienceDiv.innerHTML = `
        <div class="item-header">
            <h4>خبرة عملية ${window.cvBuilder.cvData.experience.length}</h4>
            <button class="btn-remove" onclick="removeExperience(${experience.id})">
                <i class="fas fa-trash"></i>
            </button>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label>المسمى الوظيفي *</label>
                <input type="text" value="${experience.jobTitle}"
                       onchange="updateExperience(${experience.id}, 'jobTitle', this.value)"
                       placeholder="مثال: مطور ويب أول" required>
            </div>
            <div class="form-group">
                <label>اسم الشركة *</label>
                <input type="text" value="${experience.company}"
                       onchange="updateExperience(${experience.id}, 'company', this.value)"
                       placeholder="مثال: شركة التقنية المتقدمة" required>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label>تاريخ البداية *</label>
                <input type="month" value="${experience.startDate}"
                       onchange="updateExperience(${experience.id}, 'startDate', this.value)" required>
            </div>
            <div class="form-group">
                <label>تاريخ النهاية</label>
                <input type="month" value="${experience.endDate}"
                       onchange="updateExperience(${experience.id}, 'endDate', this.value)"
                       ${experience.isCurrent ? 'disabled' : ''}>
            </div>
        </div>

        <div class="form-group">
            <label class="checkbox-label">
                <input type="checkbox" ${experience.isCurrent ? 'checked' : ''}
                       onchange="updateExperience(${experience.id}, 'isCurrent', this.checked)">
                <span class="checkmark"></span>
                أعمل حالياً في هذه الوظيفة
            </label>
        </div>

        <div class="form-group">
            <label>وصف المهام والإنجازات</label>
            <textarea rows="4" onchange="updateExperience(${experience.id}, 'description', this.value)"
                      placeholder="اكتب وصفاً مختصراً لمهامك وإنجازاتك في هذه الوظيفة...">${experience.description}</textarea>
        </div>
    `;

    experienceList.appendChild(experienceDiv);
}

function renderEducationItem(education) {
    const educationList = document.getElementById('educationList');
    if (!educationList) return;

    const educationDiv = document.createElement('div');
    educationDiv.className = 'education-item';
    educationDiv.dataset.id = education.id;

    educationDiv.innerHTML = `
        <div class="item-header">
            <h4>مؤهل أكاديمي ${window.cvBuilder.cvData.education.length}</h4>
            <button class="btn-remove" onclick="removeEducation(${education.id})">
                <i class="fas fa-trash"></i>
            </button>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label>الدرجة العلمية *</label>
                <input type="text" value="${education.degree}"
                       onchange="updateEducation(${education.id}, 'degree', this.value)"
                       placeholder="مثال: بكالوريوس علوم الحاسب" required>
            </div>
            <div class="form-group">
                <label>اسم الجامعة/المؤسسة *</label>
                <input type="text" value="${education.institution}"
                       onchange="updateEducation(${education.id}, 'institution', this.value)"
                       placeholder="مثال: جامعة الملك سعود" required>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label>سنة التخرج *</label>
                <input type="number" value="${education.year}"
                       onchange="updateEducation(${education.id}, 'year', this.value)"
                       placeholder="2023" min="1950" max="2030" required>
            </div>
            <div class="form-group">
                <label>المعدل التراكمي (اختياري)</label>
                <input type="text" value="${education.gpa}"
                       onchange="updateEducation(${education.id}, 'gpa', this.value)"
                       placeholder="مثال: 3.8 من 4.0">
            </div>
        </div>

        <div class="form-group">
            <label>تفاصيل إضافية (اختياري)</label>
            <textarea rows="3" onchange="updateEducation(${education.id}, 'details', this.value)"
                      placeholder="مثال: تخصص فرعي، مشاريع التخرج، الأنشطة الطلابية...">${education.details}</textarea>
        </div>
    `;

    educationList.appendChild(educationDiv);
}

function renderLanguageItem(language) {
    const languagesList = document.getElementById('languagesList');
    if (!languagesList) return;

    const languageDiv = document.createElement('div');
    languageDiv.className = 'language-item';
    languageDiv.dataset.id = language.id;

    languageDiv.innerHTML = `
        <div class="item-header">
            <h4>لغة ${window.cvBuilder.cvData.languages.length}</h4>
            <button class="btn-remove" onclick="removeLanguage(${language.id})">
                <i class="fas fa-trash"></i>
            </button>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label>اسم اللغة *</label>
                <input type="text" value="${language.name}"
                       onchange="updateLanguage(${language.id}, 'name', this.value)"
                       placeholder="مثال: الإنجليزية" required>
            </div>
            <div class="form-group">
                <label>مستوى الإتقان *</label>
                <select onchange="updateLanguage(${language.id}, 'level', this.value)">
                    <option value="مبتدئ" ${language.level === 'مبتدئ' ? 'selected' : ''}>مبتدئ</option>
                    <option value="متوسط" ${language.level === 'متوسط' ? 'selected' : ''}>متوسط</option>
                    <option value="متقدم" ${language.level === 'متقدم' ? 'selected' : ''}>متقدم</option>
                    <option value="طليق" ${language.level === 'طليق' ? 'selected' : ''}>طليق</option>
                    <option value="لغة أم" ${language.level === 'لغة أم' ? 'selected' : ''}>لغة أم</option>
                </select>
            </div>
        </div>
    `;

    languagesList.appendChild(languageDiv);
}

function updateExperience(experienceId, field, value) {
    if (!window.cvBuilder) return;

    const experience = window.cvBuilder.cvData.experience.find(exp => exp.id === experienceId);
    if (experience) {
        experience[field] = value;

        // Handle current job checkbox
        if (field === 'isCurrent') {
            const endDateInput = document.querySelector(`[data-id="${experienceId}"] input[type="month"]:last-of-type`);
            if (endDateInput) {
                endDateInput.disabled = value;
                if (value) {
                    endDateInput.value = '';
                    experience.endDate = '';
                }
            }
        }

        window.cvBuilder.updatePreview();
        window.cvBuilder.updateProgress();
        window.cvBuilder.saveToLocalStorage();
    }
}

function updateEducation(educationId, field, value) {
    if (!window.cvBuilder) return;

    const education = window.cvBuilder.cvData.education.find(edu => edu.id === educationId);
    if (education) {
        education[field] = value;
        window.cvBuilder.updatePreview();
        window.cvBuilder.updateProgress();
        window.cvBuilder.saveToLocalStorage();
    }
}

function updateLanguage(languageId, field, value) {
    if (!window.cvBuilder) return;

    const language = window.cvBuilder.cvData.languages.find(lang => lang.id === languageId);
    if (language) {
        language[field] = value;
        window.cvBuilder.updatePreview();
        window.cvBuilder.updateProgress();
        window.cvBuilder.saveToLocalStorage();
    }
}

function removeExperience(experienceId) {
    if (!window.cvBuilder) return;

    if (confirm('هل أنت متأكد من حذف هذه الخبرة؟')) {
        window.cvBuilder.cvData.experience = window.cvBuilder.cvData.experience.filter(exp => exp.id !== experienceId);

        const experienceElement = document.querySelector(`[data-id="${experienceId}"]`);
        if (experienceElement) {
            experienceElement.remove();
        }

        window.cvBuilder.updatePreview();
        window.cvBuilder.updateProgress();
        window.cvBuilder.saveToLocalStorage();
        window.cvBuilder.showNotification('تم حذف الخبرة بنجاح', 'success');
    }
}

function removeEducation(educationId) {
    if (!window.cvBuilder) return;

    if (confirm('هل أنت متأكد من حذف هذا المؤهل؟')) {
        window.cvBuilder.cvData.education = window.cvBuilder.cvData.education.filter(edu => edu.id !== educationId);

        const educationElement = document.querySelector(`[data-id="${educationId}"]`);
        if (educationElement) {
            educationElement.remove();
        }

        window.cvBuilder.updatePreview();
        window.cvBuilder.updateProgress();
        window.cvBuilder.saveToLocalStorage();
        window.cvBuilder.showNotification('تم حذف المؤهل بنجاح', 'success');
    }
}

function removeLanguage(languageId) {
    if (!window.cvBuilder) return;

    if (confirm('هل أنت متأكد من حذف هذه اللغة؟')) {
        window.cvBuilder.cvData.languages = window.cvBuilder.cvData.languages.filter(lang => lang.id !== languageId);

        const languageElement = document.querySelector(`[data-id="${languageId}"]`);
        if (languageElement) {
            languageElement.remove();
        }

        window.cvBuilder.updatePreview();
        window.cvBuilder.updateProgress();
        window.cvBuilder.saveToLocalStorage();
        window.cvBuilder.showNotification('تم حذف اللغة بنجاح', 'success');
    }
}

function removePhoto() {
    if (!window.cvBuilder) return;

    const photoPreview = document.getElementById('photoPreview');
    if (photoPreview) {
        photoPreview.innerHTML = '<i class="fas fa-user"></i>';
    }

    window.cvBuilder.cvData.personal.photo = null;
    window.cvBuilder.updatePreview();
    window.cvBuilder.saveToLocalStorage();
    window.cvBuilder.showNotification('تم حذف الصورة بنجاح', 'success');
}

function saveCV() {
    if (!window.cvBuilder) return;

    window.cvBuilder.saveToLocalStorage();
    window.cvBuilder.showNotification('تم حفظ السيرة الذاتية بنجاح', 'success');
}

function previewCV() {
    if (!window.cvBuilder) return;

    // Check if CV has content
    if (!window.cvBuilder.cvData.personal.fullName) {
        window.cvBuilder.showNotification('لا توجد بيانات كافية للمعاينة. يرجى إدخال معلوماتك الشخصية على الأقل.', 'warning');
        return;
    }

    // Update preview immediately
    window.cvBuilder.updatePreview();

    // Show preview modal for better viewing
    showPreviewModal();
}

// Show full-screen preview modal
function showPreviewModal() {
    const modal = document.createElement('div');
    modal.className = 'preview-modal-overlay';
    modal.innerHTML = `
        <div class="preview-modal">
            <div class="preview-modal-header">
                <h3>معاينة السيرة الذاتية</h3>
                <div class="preview-controls">
                    <button class="btn btn-sm" onclick="zoomOut()">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <span class="zoom-level" id="modalZoomLevel">100%</span>
                    <button class="btn btn-sm" onclick="zoomIn()">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="btn btn-primary" onclick="exportCV()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                    <button class="close-btn" onclick="closePreviewModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="preview-modal-content">
                <div class="preview-container" id="modalPreviewContainer">
                    <!-- Preview content will be copied here -->
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Copy current preview content
    const originalPreview = document.querySelector('.cv-preview-document');
    const modalPreview = modal.querySelector('#modalPreviewContainer');

    if (originalPreview && modalPreview) {
        modalPreview.innerHTML = originalPreview.innerHTML;
    }

    // Add click outside to close
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closePreviewModal();
        }
    });
}

// Close preview modal
function closePreviewModal() {
    const modal = document.querySelector('.preview-modal-overlay');
    if (modal) {
        modal.remove();
    }
}

    // Create preview modal
    const modal = document.createElement('div');
    modal.className = 'modal preview-modal-overlay';
    modal.innerHTML = `
        <div class="modal-content preview-modal-content">
            <div class="preview-modal-header">
                <div class="preview-header-content">
                    <div class="preview-icon-main">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="preview-title-section">
                        <h3>معاينة السيرة الذاتية</h3>
                        <p>معاينة كاملة لسيرتك الذاتية قبل التصدير</p>
                    </div>
                </div>
                <button class="modal-close" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="preview-modal-body">
                <div class="cv-preview-document">
                    ${document.querySelector('.cv-preview-document').innerHTML}
                </div>
            </div>

            <div class="preview-modal-footer">
                <button class="btn btn-outline" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
                <button class="btn btn-primary" onclick="downloadCV()">
                    <i class="fas fa-download"></i>
                    تحميل PDF
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add click outside to close
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function downloadCV() {
    if (!window.cvBuilder) return;

    window.cvBuilder.showNotification('جاري تحضير ملف PDF...', 'info');

    // This would integrate with a PDF generation service
    // For now, we'll show a placeholder message
    setTimeout(() => {
        window.cvBuilder.showNotification('ميزة تحميل PDF ستكون متاحة قريباً', 'info');
    }, 1000);
}

// Export CV function - main export handler
function exportCV() {
    if (!window.cvBuilder) {
        console.error('CV Builder not initialized');
        return;
    }

    // Check if CV has sufficient data
    if (!window.cvBuilder.cvData.personal.fullName) {
        window.cvBuilder.showNotification('لا توجد بيانات كافية للتصدير. يرجى إدخال معلوماتك الشخصية على الأقل.', 'warning');
        return;
    }

    // Show export options modal
    showExportModal();
}

// Show export options modal
function showExportModal() {
    const modal = document.createElement('div');
    modal.className = 'export-modal-overlay';
    modal.innerHTML = `
        <div class="export-modal">
            <div class="export-modal-header">
                <h3>تصدير السيرة الذاتية</h3>
                <button class="close-btn" onclick="closeExportModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="export-modal-content">
                <div class="export-options">
                    <div class="export-option" onclick="exportToPDF()">
                        <div class="export-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="export-info">
                            <h4>ملف PDF</h4>
                            <p>تصدير عالي الجودة للطباعة والمشاركة</p>
                        </div>
                    </div>
                    <div class="export-option" onclick="exportToImage('png')">
                        <div class="export-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="export-info">
                            <h4>صورة PNG</h4>
                            <p>صورة عالية الدقة مناسبة للويب</p>
                        </div>
                    </div>
                    <div class="export-option" onclick="exportToImage('jpg')">
                        <div class="export-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="export-info">
                            <h4>صورة JPG</h4>
                            <p>صورة مضغوطة بحجم أصغر</p>
                        </div>
                    </div>
                    <div class="export-option" onclick="generateQRCode()">
                        <div class="export-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <div class="export-info">
                            <h4>رمز QR</h4>
                            <p>رمز للمشاركة السريعة لمعلومات الاتصال</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add click outside to close
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeExportModal();
        }
    });
}

// Close export modal
function closeExportModal() {
    const modal = document.querySelector('.export-modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// Initialize CV Builder when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة منشئ السيرة الذاتية...');

    try {
        // Initialize immediately without delay for better performance
        window.cvBuilder = new CVBuilder();
        console.log('✅ تم تهيئة منشئ السيرة الذاتية بنجاح');

        // Initialize CV Preview if available
        if (typeof CVPreview !== 'undefined') {
            window.cvPreview = new CVPreview();
        }

    } catch (error) {
        console.error('❌ خطأ في تهيئة منشئ السيرة الذاتية:', error);
    }
});

console.log('📄 تم تحميل ملف CV Builder بنجاح');
