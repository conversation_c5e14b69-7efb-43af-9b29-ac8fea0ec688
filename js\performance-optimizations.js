// ===== Performance Optimizations for Elashrafy CV =====

class PerformanceOptimizer {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupLazyLoading();
        this.setupImageOptimization();
        this.setupDebouncing();
        this.setupCaching();
        this.setupPreloading();
        this.monitorPerformance();
    }
    
    // Lazy loading for images and heavy content
    setupLazyLoading() {
        // Intersection Observer for lazy loading
        const lazyObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    
                    // Load images
                    if (element.dataset.src) {
                        element.src = element.dataset.src;
                        element.removeAttribute('data-src');
                    }
                    
                    // Load background images
                    if (element.dataset.bgSrc) {
                        element.style.backgroundImage = `url(${element.dataset.bgSrc})`;
                        element.removeAttribute('data-bg-src');
                    }
                    
                    // Load heavy content
                    if (element.dataset.loadContent) {
                        this.loadHeavyContent(element);
                    }
                    
                    lazyObserver.unobserve(element);
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.1
        });
        
        // Observe lazy elements
        document.querySelectorAll('[data-src], [data-bg-src], [data-load-content]').forEach(el => {
            lazyObserver.observe(el);
        });
    }
    
    // Image optimization
    setupImageOptimization() {
        // Compress uploaded images
        window.compressImage = (file, maxWidth = 800, quality = 0.8) => {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                img.onload = () => {
                    // Calculate new dimensions
                    let { width, height } = img;
                    if (width > maxWidth) {
                        height = (height * maxWidth) / width;
                        width = maxWidth;
                    }
                    
                    canvas.width = width;
                    canvas.height = height;
                    
                    // Draw and compress
                    ctx.drawImage(img, 0, 0, width, height);
                    canvas.toBlob(resolve, 'image/jpeg', quality);
                };
                
                img.src = URL.createObjectURL(file);
            });
        };
        
        // Auto-compress profile photos
        const photoInput = document.getElementById('photoInput');
        if (photoInput) {
            photoInput.addEventListener('change', async (e) => {
                const file = e.target.files[0];
                if (file && file.type.startsWith('image/')) {
                    const compressedFile = await window.compressImage(file);
                    const reader = new FileReader();
                    reader.onload = (event) => {
                        const photoPreview = document.getElementById('photoPreview');
                        if (photoPreview) {
                            photoPreview.innerHTML = `<img src="${event.target.result}" alt="Profile Photo">`;
                        }
                        
                        // Update CV data
                        if (window.cvBuilder) {
                            window.cvBuilder.cvData.personal.photo = event.target.result;
                            window.cvBuilder.updatePreview();
                        }
                    };
                    reader.readAsDataURL(compressedFile);
                }
            });
        }
    }
    
    // Debouncing for input events
    setupDebouncing() {
        // Debounce function
        const debounce = (func, wait) => {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        };
        
        // Debounce search inputs
        const searchInputs = document.querySelectorAll('input[type="search"], input[placeholder*="بحث"]');
        searchInputs.forEach(input => {
            const originalHandler = input.oninput;
            input.oninput = debounce((e) => {
                if (originalHandler) originalHandler(e);
            }, 300);
        });
        
        // Debounce auto-save
        if (window.cvBuilder) {
            const originalSave = window.cvBuilder.saveToLocalStorage.bind(window.cvBuilder);
            window.cvBuilder.saveToLocalStorage = debounce(originalSave, 1000);
        }
    }
    
    // Caching system
    setupCaching() {
        // Cache templates
        window.templateCache = new Map();
        
        // Cache API responses (if any)
        window.apiCache = new Map();
        
        // Cache computed styles
        window.styleCache = new Map();
        
        // Helper function to get cached data
        window.getCachedData = (key, fetchFunction, expiry = 300000) => { // 5 minutes default
            const cached = window.apiCache.get(key);
            const now = Date.now();
            
            if (cached && (now - cached.timestamp) < expiry) {
                return Promise.resolve(cached.data);
            }
            
            return fetchFunction().then(data => {
                window.apiCache.set(key, {
                    data,
                    timestamp: now
                });
                return data;
            });
        };
    }
    
    // Preloading critical resources
    setupPreloading() {
        // Preload critical CSS
        const criticalCSS = ['css/main.css', 'css/animations.css'];
        criticalCSS.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = href;
            document.head.appendChild(link);
        });
        
        // Preload critical fonts
        const criticalFonts = [
            'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap'
        ];
        criticalFonts.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = href;
            document.head.appendChild(link);
        });
        
        // Preload next page resources
        this.preloadNextPageResources();
    }
    
    preloadNextPageResources() {
        // Preload CV builder resources when on homepage
        if (window.location.pathname === '/' || window.location.pathname.includes('index.html')) {
            setTimeout(() => {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = 'cv-builder.html';
                document.head.appendChild(link);
                
                // Preload CV builder CSS
                const cssLink = document.createElement('link');
                cssLink.rel = 'prefetch';
                cssLink.href = 'css/cv-builder.css';
                document.head.appendChild(cssLink);
            }, 2000);
        }
    }
    
    // Load heavy content when needed
    loadHeavyContent(element) {
        const contentType = element.dataset.loadContent;
        
        switch (contentType) {
            case 'templates':
                this.loadTemplateContent(element);
                break;
            case 'ai-chat':
                this.loadAIChatContent(element);
                break;
            case 'export-options':
                this.loadExportContent(element);
                break;
        }
    }
    
    loadTemplateContent(element) {
        // Load template previews dynamically
        if (window.templatesManager) {
            window.templatesManager.renderTemplates();
        }
    }
    
    loadAIChatContent(element) {
        // Load AI chat interface
        if (window.aiAssistant) {
            window.aiAssistant.createChatModal();
        }
    }
    
    loadExportContent(element) {
        // Load export libraries when needed
        if (!window.jsPDF) {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            document.head.appendChild(script);
        }
        
        if (!window.html2canvas) {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
            document.head.appendChild(script);
        }
    }
    
    // Performance monitoring
    monitorPerformance() {
        // Monitor page load time
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            console.log(`📊 وقت تحميل الصفحة: ${Math.round(loadTime)}ms`);
            
            // Log performance metrics
            if (performance.getEntriesByType) {
                const navigation = performance.getEntriesByType('navigation')[0];
                if (navigation) {
                    console.log(`📊 إحصائيات الأداء:
                        - DNS: ${Math.round(navigation.domainLookupEnd - navigation.domainLookupStart)}ms
                        - اتصال: ${Math.round(navigation.connectEnd - navigation.connectStart)}ms
                        - تحميل: ${Math.round(navigation.loadEventEnd - navigation.loadEventStart)}ms
                        - DOM: ${Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart)}ms
                    `);
                }
            }
        });
        
        // Monitor memory usage (if available)
        if (performance.memory) {
            setInterval(() => {
                const memory = performance.memory;
                const used = Math.round(memory.usedJSHeapSize / 1048576); // MB
                const total = Math.round(memory.totalJSHeapSize / 1048576); // MB
                
                if (used > 50) { // Alert if using more than 50MB
                    console.warn(`⚠️ استخدام ذاكرة عالي: ${used}MB من ${total}MB`);
                }
            }, 30000); // Check every 30 seconds
        }
        
        // Monitor long tasks
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    if (entry.duration > 50) { // Tasks longer than 50ms
                        console.warn(`⚠️ مهمة طويلة: ${entry.name} (${Math.round(entry.duration)}ms)`);
                    }
                });
            });
            
            try {
                observer.observe({ entryTypes: ['longtask'] });
            } catch (e) {
                // Long task API not supported
            }
        }
    }
    
    // Cleanup function
    cleanup() {
        // Clear caches
        if (window.templateCache) window.templateCache.clear();
        if (window.apiCache) window.apiCache.clear();
        if (window.styleCache) window.styleCache.clear();
        
        // Remove event listeners
        // This would be implemented based on specific needs
    }
}

// Service Worker registration for caching
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('✅ Service Worker مسجل بنجاح');
            })
            .catch(error => {
                console.log('❌ فشل تسجيل Service Worker:', error);
            });
    });
}

// Initialize performance optimizer
document.addEventListener('DOMContentLoaded', () => {
    window.performanceOptimizer = new PerformanceOptimizer();
});

// Export for use in other modules
window.PerformanceOptimizer = PerformanceOptimizer;
