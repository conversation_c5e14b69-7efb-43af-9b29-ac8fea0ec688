// ===== Service Worker for Elashrafy CV =====

const CACHE_NAME = 'elashrafy-cv-v1.0.0';
const STATIC_CACHE = 'elashrafy-static-v1';
const DYNAMIC_CACHE = 'elashrafy-dynamic-v1';

// Files to cache immediately
const STATIC_FILES = [
    '/',
    '/index.html',
    '/cv-builder.html',
    '/templates.html',
    '/css/main.css',
    '/css/cv-builder.css',
    '/css/templates.css',
    '/css/animations.css',
    '/js/app.js',
    '/js/cv-builder.js',
    '/js/cv-preview.js',
    '/js/ai-assistant.js',
    '/js/templates.js',
    '/js/navigation.js',
    '/js/animations.js',
    '/assets/favicon.svg',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Amiri:wght@400;700&display=swap',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('Service Worker: Static files cached successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: Error caching static files', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated successfully');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached files or fetch from network
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http requests
    if (!url.protocol.startsWith('http')) {
        return;
    }
    
    event.respondWith(
        caches.match(request)
            .then((cachedResponse) => {
                // Return cached version if available
                if (cachedResponse) {
                    console.log('Service Worker: Serving from cache', request.url);
                    return cachedResponse;
                }
                
                // Fetch from network
                return fetch(request)
                    .then((networkResponse) => {
                        // Don't cache if not a valid response
                        if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== 'basic') {
                            return networkResponse;
                        }
                        
                        // Clone the response
                        const responseToCache = networkResponse.clone();
                        
                        // Cache dynamic content
                        if (shouldCache(request.url)) {
                            caches.open(DYNAMIC_CACHE)
                                .then((cache) => {
                                    console.log('Service Worker: Caching dynamic content', request.url);
                                    cache.put(request, responseToCache);
                                });
                        }
                        
                        return networkResponse;
                    })
                    .catch((error) => {
                        console.error('Service Worker: Fetch failed', error);
                        
                        // Return offline fallback for HTML pages
                        if (request.headers.get('accept').includes('text/html')) {
                            return caches.match('/index.html');
                        }
                        
                        // Return empty response for other resources
                        return new Response('', {
                            status: 408,
                            statusText: 'Request Timeout'
                        });
                    });
            })
    );
});

// Helper function to determine if a resource should be cached
function shouldCache(url) {
    // Cache images
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
        return true;
    }
    
    // Cache fonts
    if (url.match(/\.(woff|woff2|ttf|eot)$/i)) {
        return true;
    }
    
    // Cache external libraries
    if (url.includes('cdnjs.cloudflare.com') || 
        url.includes('fonts.googleapis.com') ||
        url.includes('fonts.gstatic.com')) {
        return true;
    }
    
    // Don't cache API calls or dynamic content
    if (url.includes('/api/') || url.includes('?')) {
        return false;
    }
    
    return false;
}

// Background sync for offline data
self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync triggered', event.tag);
    
    if (event.tag === 'cv-data-sync') {
        event.waitUntil(syncCVData());
    }
});

// Sync CV data when back online
async function syncCVData() {
    try {
        // Get stored CV data from IndexedDB or localStorage
        const clients = await self.clients.matchAll();
        
        clients.forEach(client => {
            client.postMessage({
                type: 'SYNC_CV_DATA',
                message: 'Syncing CV data...'
            });
        });
        
        console.log('Service Worker: CV data synced successfully');
    } catch (error) {
        console.error('Service Worker: Error syncing CV data', error);
    }
}

// Push notifications (for future use)
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'إشعار جديد من Elashrafy CV',
        icon: '/assets/favicon.svg',
        badge: '/assets/favicon.svg',
        vibrate: [200, 100, 200],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'عرض التفاصيل',
                icon: '/assets/favicon.svg'
            },
            {
                action: 'close',
                title: 'إغلاق',
                icon: '/assets/favicon.svg'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('Elashrafy CV', options)
    );
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification clicked', event.action);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Handle messages from main thread
self.addEventListener('message', (event) => {
    console.log('Service Worker: Message received', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CACHE_CV_DATA') {
        // Cache CV data for offline use
        caches.open(DYNAMIC_CACHE)
            .then(cache => {
                const response = new Response(JSON.stringify(event.data.cvData));
                return cache.put('/cv-data.json', response);
            });
    }
});

// Periodic background sync (if supported)
self.addEventListener('periodicsync', (event) => {
    console.log('Service Worker: Periodic sync triggered', event.tag);
    
    if (event.tag === 'cv-backup') {
        event.waitUntil(backupCVData());
    }
});

async function backupCVData() {
    try {
        // Backup CV data periodically
        console.log('Service Worker: Backing up CV data');
        
        const clients = await self.clients.matchAll();
        clients.forEach(client => {
            client.postMessage({
                type: 'BACKUP_CV_DATA',
                message: 'Creating backup...'
            });
        });
    } catch (error) {
        console.error('Service Worker: Error backing up CV data', error);
    }
}

// Error handling
self.addEventListener('error', (event) => {
    console.error('Service Worker: Error occurred', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
    console.error('Service Worker: Unhandled promise rejection', event.reason);
});

console.log('Service Worker: Script loaded successfully');
