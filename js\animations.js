// ===== Advanced Animations Controller =====

class AnimationController {
    constructor() {
        this.observers = new Map();
        this.animationQueue = [];
        this.isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        
        this.init();
    }
    
    init() {
        this.setupIntersectionObservers();
        this.setupCounterAnimations();
        this.setupTypewriterAnimations();
        this.setupParticleEffects();
        this.setupHoverEffects();
    }
    
    // Setup intersection observers for scroll-triggered animations
    setupIntersectionObservers() {
        const fadeObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerFadeAnimation(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });
        
        const slideObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerSlideAnimation(entry.target);
                }
            });
        }, {
            threshold: 0.2,
            rootMargin: '0px 0px -30px 0px'
        });
        
        // Observe elements
        document.querySelectorAll('[data-animate="fade"]').forEach(el => {
            fadeObserver.observe(el);
        });
        
        document.querySelectorAll('[data-animate="slide"]').forEach(el => {
            slideObserver.observe(el);
        });
        
        this.observers.set('fade', fadeObserver);
        this.observers.set('slide', slideObserver);
    }
    
    // Trigger fade animations
    triggerFadeAnimation(element) {
        if (this.isReducedMotion) {
            element.style.opacity = '1';
            return;
        }
        
        const direction = element.dataset.direction || 'up';
        const delay = parseInt(element.dataset.delay) || 0;
        const duration = parseInt(element.dataset.duration) || 600;
        
        setTimeout(() => {
            element.classList.add('animate-fade-in');
            element.style.animationDuration = `${duration}ms`;
            
            switch (direction) {
                case 'up':
                    element.style.animation = `fadeInUp ${duration}ms ease-out forwards`;
                    break;
                case 'down':
                    element.style.animation = `fadeInDown ${duration}ms ease-out forwards`;
                    break;
                case 'left':
                    element.style.animation = `fadeInLeft ${duration}ms ease-out forwards`;
                    break;
                case 'right':
                    element.style.animation = `fadeInRight ${duration}ms ease-out forwards`;
                    break;
                default:
                    element.style.animation = `fadeIn ${duration}ms ease-out forwards`;
            }
        }, delay);
    }
    
    // Trigger slide animations
    triggerSlideAnimation(element) {
        if (this.isReducedMotion) {
            element.style.transform = 'translateX(0)';
            element.style.opacity = '1';
            return;
        }
        
        const direction = element.dataset.direction || 'right';
        const delay = parseInt(element.dataset.delay) || 0;
        const duration = parseInt(element.dataset.duration) || 800;
        
        setTimeout(() => {
            element.classList.add('animate-slide-in');
            element.style.animationDuration = `${duration}ms`;
            element.style.animation = `slideIn${direction.charAt(0).toUpperCase() + direction.slice(1)} ${duration}ms ease-out forwards`;
        }, delay);
    }
    
    // Setup counter animations
    setupCounterAnimations() {
        const counterElements = document.querySelectorAll('[data-counter]');
        
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    counterObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });
        
        counterElements.forEach(el => counterObserver.observe(el));
    }
    
    // Animate counter
    animateCounter(element) {
        const target = parseInt(element.dataset.counter);
        const duration = parseInt(element.dataset.duration) || 2000;
        const increment = target / (duration / 16);
        let current = 0;
        
        const updateCounter = () => {
            current += increment;
            if (current < target) {
                element.textContent = Math.floor(current).toLocaleString('ar-EG');
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target.toLocaleString('ar-EG');
            }
        };
        
        updateCounter();
    }
    
    // Setup typewriter animations
    setupTypewriterAnimations() {
        const typewriterElements = document.querySelectorAll('[data-typewriter]');
        
        typewriterElements.forEach(element => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.startTypewriter(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(element);
        });
    }
    
    // Start typewriter animation
    startTypewriter(element) {
        const text = element.dataset.typewriter;
        const speed = parseInt(element.dataset.speed) || 100;
        const delay = parseInt(element.dataset.delay) || 0;
        
        element.textContent = '';
        
        setTimeout(() => {
            let i = 0;
            const typeInterval = setInterval(() => {
                element.textContent += text.charAt(i);
                i++;
                
                if (i >= text.length) {
                    clearInterval(typeInterval);
                    element.classList.add('typewriter-complete');
                }
            }, speed);
        }, delay);
    }
    
    // Setup particle effects
    setupParticleEffects() {
        const particleContainers = document.querySelectorAll('[data-particles]');
        
        particleContainers.forEach(container => {
            this.createParticleEffect(container);
        });
    }
    
    // Create particle effect
    createParticleEffect(container) {
        const particleCount = parseInt(container.dataset.particles) || 50;
        const particleColor = container.dataset.particleColor || '#6366f1';
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: 2px;
                height: 2px;
                background: ${particleColor};
                border-radius: 50%;
                pointer-events: none;
                opacity: 0;
            `;
            
            container.appendChild(particle);
            this.animateParticle(particle, container);
        }
    }
    
    // Animate individual particle
    animateParticle(particle, container) {
        const containerRect = container.getBoundingClientRect();
        const startX = Math.random() * containerRect.width;
        const startY = Math.random() * containerRect.height;
        const endX = startX + (Math.random() - 0.5) * 200;
        const endY = startY - Math.random() * 100;
        const duration = 3000 + Math.random() * 2000;
        
        particle.style.left = startX + 'px';
        particle.style.top = startY + 'px';
        
        particle.animate([
            { 
                transform: `translate(0, 0)`,
                opacity: 0
            },
            {
                transform: `translate(${(endX - startX) / 2}px, ${(endY - startY) / 2}px)`,
                opacity: 0.8
            },
            {
                transform: `translate(${endX - startX}px, ${endY - startY}px)`,
                opacity: 0
            }
        ], {
            duration: duration,
            easing: 'ease-out',
            iterations: Infinity
        });
    }
    
    // Setup hover effects
    setupHoverEffects() {
        // Magnetic effect for buttons
        const magneticElements = document.querySelectorAll('[data-magnetic]');
        
        magneticElements.forEach(element => {
            element.addEventListener('mousemove', (e) => {
                this.applyMagneticEffect(e, element);
            });
            
            element.addEventListener('mouseleave', () => {
                this.resetMagneticEffect(element);
            });
        });
        
        // Ripple effect for buttons
        const rippleElements = document.querySelectorAll('[data-ripple]');
        
        rippleElements.forEach(element => {
            element.addEventListener('click', (e) => {
                this.createRippleEffect(e, element);
            });
        });
    }
    
    // Apply magnetic effect
    applyMagneticEffect(e, element) {
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        const deltaX = (e.clientX - centerX) * 0.1;
        const deltaY = (e.clientY - centerY) * 0.1;
        
        element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
    }
    
    // Reset magnetic effect
    resetMagneticEffect(element) {
        element.style.transform = 'translate(0, 0)';
    }
    
    // Create ripple effect
    createRippleEffect(e, element) {
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        const ripple = document.createElement('div');
        ripple.className = 'ripple-effect';
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            pointer-events: none;
            z-index: 1;
        `;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        ripple.animate([
            { transform: 'scale(0)', opacity: 1 },
            { transform: 'scale(1)', opacity: 0 }
        ], {
            duration: 600,
            easing: 'ease-out'
        }).onfinish = () => {
            ripple.remove();
        };
    }
    
    // Utility method to add custom animation
    addCustomAnimation(element, keyframes, options = {}) {
        if (this.isReducedMotion) return;
        
        const defaultOptions = {
            duration: 600,
            easing: 'ease-out',
            fill: 'forwards'
        };
        
        const animationOptions = { ...defaultOptions, ...options };
        
        return element.animate(keyframes, animationOptions);
    }
    
    // Cleanup method
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.animationQueue = [];
    }
}

// ===== Loading Animations =====
class LoadingAnimations {
    static showSkeletonLoader(container, type = 'card') {
        const skeleton = document.createElement('div');
        skeleton.className = `skeleton-loader skeleton-${type}`;
        
        switch (type) {
            case 'card':
                skeleton.innerHTML = `
                    <div class="skeleton-header"></div>
                    <div class="skeleton-content">
                        <div class="skeleton-line"></div>
                        <div class="skeleton-line"></div>
                        <div class="skeleton-line short"></div>
                    </div>
                `;
                break;
            case 'text':
                skeleton.innerHTML = `
                    <div class="skeleton-line"></div>
                    <div class="skeleton-line"></div>
                    <div class="skeleton-line short"></div>
                `;
                break;
            case 'avatar':
                skeleton.innerHTML = `<div class="skeleton-avatar"></div>`;
                break;
        }
        
        container.appendChild(skeleton);
        return skeleton;
    }
    
    static hideSkeletonLoader(skeleton) {
        if (skeleton && skeleton.parentElement) {
            skeleton.style.opacity = '0';
            setTimeout(() => {
                skeleton.remove();
            }, 300);
        }
    }
}

// Initialize animations when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.animationController = new AnimationController();
});

// Export for use in other modules
window.AnimationController = AnimationController;
window.LoadingAnimations = LoadingAnimations;
