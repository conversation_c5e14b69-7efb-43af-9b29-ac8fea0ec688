<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منشئ السيرة الذاتية - Elashrafy CV</title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/cv-builder.css">
    <link rel="stylesheet" href="css/cv-templates.css">
    <link rel="stylesheet" href="css/animations.css">

    <!-- Google Fonts for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- External Libraries will be loaded at the bottom -->
</head>
<body class="cv-builder-page">
    <!-- Navigation -->
    <nav class="navbar cv-builder-nav">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html">
                    <i class="fas fa-file-alt"></i>
                    <span>Elashrafy CV</span>
                </a>
            </div>

            <div class="nav-actions">
                <button class="btn btn-outline" onclick="saveCV()">
                    <i class="fas fa-save"></i>
                    حفظ
                </button>
                <button class="btn btn-primary" onclick="previewCV()">
                    <i class="fas fa-eye"></i>
                    معاينة
                </button>
                <button class="btn btn-secondary" onclick="exportCV()">
                    <i class="fas fa-download"></i>
                    تصدير
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="cv-builder-container">
        <!-- Sidebar -->
        <aside class="cv-sidebar">
            <div class="sidebar-header">
                <h3>إنشاء السيرة الذاتية</h3>
                <div class="progress-indicator">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 25%"></div>
                    </div>
                    <span class="progress-text">25% مكتمل</span>
                </div>
            </div>

            <div class="sidebar-sections">
                <div class="section-item active" data-section="personal">
                    <div class="section-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="section-info">
                        <h4>المعلومات الشخصية</h4>
                        <p>الاسم والتواصل</p>
                    </div>
                    <div class="section-status">
                        <div class="section-indicator">1</div>
                    </div>
                </div>

                <div class="section-item" data-section="summary">
                    <div class="section-icon">
                        <i class="fas fa-align-left"></i>
                    </div>
                    <div class="section-info">
                        <h4>الملخص المهني</h4>
                        <p>نبذة عن خبراتك</p>
                    </div>
                    <div class="section-status">
                        <div class="section-indicator">2</div>
                    </div>
                </div>

                <div class="section-item" data-section="experience">
                    <div class="section-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="section-info">
                        <h4>الخبرات العملية</h4>
                        <p>تاريخك المهني</p>
                    </div>
                    <div class="section-status">
                        <div class="section-indicator">3</div>
                    </div>
                </div>

                <div class="section-item" data-section="education">
                    <div class="section-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="section-info">
                        <h4>التعليم</h4>
                        <p>مؤهلاتك الأكاديمية</p>
                    </div>
                    <div class="section-status">
                        <div class="section-indicator">4</div>
                    </div>
                </div>

                <div class="section-item" data-section="skills">
                    <div class="section-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="section-info">
                        <h4>المهارات</h4>
                        <p>قدراتك وخبراتك</p>
                    </div>
                    <div class="section-status">
                        <div class="section-indicator">5</div>
                    </div>
                </div>

                <div class="section-item" data-section="languages">
                    <div class="section-icon">
                        <i class="fas fa-language"></i>
                    </div>
                    <div class="section-info">
                        <h4>اللغات</h4>
                        <p>اللغات التي تتقنها</p>
                    </div>
                    <div class="section-status">
                        <div class="section-indicator">6</div>
                    </div>
                </div>
            </div>

            <!-- AI Assistant -->
            <div class="ai-assistant">
                <div class="ai-header">
                    <div class="ai-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="ai-info">
                        <h4>المساعد الذكي</h4>
                        <p class="ai-status">متصل</p>
                    </div>
                </div>
                <div class="ai-suggestions">
                    <div class="suggestion-item">
                        <i class="fas fa-lightbulb"></i>
                        <span>اقتراح: أضف مهارات تقنية لتحسين ملفك الشخصي</span>
                    </div>
                </div>
                <button class="btn btn-ai" onclick="openAIChat()">
                    <i class="fas fa-comments"></i>
                    تحدث مع المساعد
                </button>
            </div>
        </aside>

        <!-- Main Editor -->
        <main class="cv-editor">
            <!-- Personal Information Section -->
            <div class="editor-section active" id="personal-section">
                <div class="section-header">
                    <h2>المعلومات الشخصية</h2>
                    <p>أدخل معلوماتك الأساسية للتواصل</p>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label>الاسم الكامل *</label>
                        <input type="text" id="fullName" placeholder="أدخل اسمك الكامل" required>
                    </div>

                    <div class="form-group">
                        <label>المسمى الوظيفي *</label>
                        <input type="text" id="jobTitle" placeholder="مثل: مطور ويب، مصمم جرافيك" required>
                    </div>

                    <div class="form-group">
                        <label>البريد الإلكتروني *</label>
                        <input type="email" id="email" placeholder="<EMAIL>" required>
                    </div>

                    <div class="form-group">
                        <label>رقم الهاتف *</label>
                        <input type="tel" id="phone" placeholder="+966 50 123 4567" required>
                    </div>

                    <div class="form-group">
                        <label>المدينة</label>
                        <input type="text" id="city" placeholder="الرياض، السعودية">
                    </div>

                    <div class="form-group">
                        <label>الموقع الإلكتروني</label>
                        <input type="url" id="website" placeholder="https://yourwebsite.com">
                    </div>

                    <div class="form-group">
                        <label>LinkedIn</label>
                        <input type="url" id="linkedin" placeholder="https://linkedin.com/in/yourprofile">
                    </div>

                    <div class="form-group">
                        <label>GitHub</label>
                        <input type="url" id="github" placeholder="https://github.com/yourusername">
                    </div>
                </div>

                <!-- Photo Upload -->
                <div class="photo-upload-section">
                    <h3>الصورة الشخصية</h3>
                    <div class="photo-upload">
                        <div class="photo-preview" id="photoPreview">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="upload-controls">
                            <input type="file" id="photoInput" accept="image/*" style="display: none;">
                            <button class="btn btn-outline" onclick="document.getElementById('photoInput').click()">
                                <i class="fas fa-upload"></i>
                                رفع صورة
                            </button>
                            <button class="btn btn-outline" onclick="removePhoto()">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                </div>

                <div class="section-actions">
                    <button class="btn btn-primary" onclick="nextSection()">
                        التالي: الملخص المهني
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </div>

            <!-- Summary Section -->
            <div class="editor-section" id="summary-section" style="display: none;">
                <div class="section-header">
                    <h2>الملخص المهني</h2>
                    <p>اكتب نبذة مختصرة ومؤثرة عن خبراتك وأهدافك المهنية</p>
                </div>

                <div class="form-group">
                    <label>الملخص المهني *</label>
                    <textarea id="professionalSummary" rows="6"
                        placeholder="مثال: مطور ويب متخصص بخبرة 5 سنوات في تطوير التطبيقات الحديثة باستخدام React و Node.js. نجحت في تطوير أكثر من 20 مشروع ويب وزيادة أداء التطبيقات بنسبة 40%. أسعى لتطوير حلول تقنية مبتكرة تساهم في نمو الشركة وتحسين تجربة المستخدم."
                        data-field="summary"></textarea>
                    <div class="character-count">
                        <span id="summaryCount">0</span> / 500 حرف
                    </div>
                </div>

                <div class="section-actions">
                    <button class="btn btn-outline" onclick="prevSection()">
                        <i class="fas fa-arrow-right"></i>
                        السابق
                    </button>
                    <button class="btn btn-primary" onclick="nextSection()">
                        التالي: الخبرات العملية
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </div>

            <!-- Experience Section -->
            <div class="editor-section" id="experience-section" style="display: none;">
                <div class="section-header">
                    <h2>الخبرات العملية</h2>
                    <p>أضف خبراتك العملية والوظائف السابقة</p>
                </div>

                <div class="section-content">
                    <div id="experienceList" class="items-list">
                        <!-- Experience items will be added here -->
                    </div>

                    <button class="btn btn-outline btn-add" onclick="addExperience()">
                        <i class="fas fa-plus"></i>
                        إضافة خبرة عملية
                    </button>
                </div>

                <div class="section-actions">
                    <button class="btn btn-outline" onclick="prevSection()">
                        <i class="fas fa-arrow-right"></i>
                        السابق
                    </button>
                    <button class="btn btn-primary" onclick="nextSection()">
                        التالي: التعليم
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </div>

            <!-- Education Section -->
            <div class="editor-section" id="education-section" style="display: none;">
                <div class="section-header">
                    <h2>التعليم</h2>
                    <p>أضف مؤهلاتك الأكاديمية والشهادات</p>
                </div>

                <div class="section-content">
                    <div id="educationList" class="items-list">
                        <!-- Education items will be added here -->
                    </div>

                    <button class="btn btn-outline btn-add" onclick="addEducation()">
                        <i class="fas fa-plus"></i>
                        إضافة مؤهل أكاديمي
                    </button>
                </div>

                <div class="section-actions">
                    <button class="btn btn-outline" onclick="prevSection()">
                        <i class="fas fa-arrow-right"></i>
                        السابق
                    </button>
                    <button class="btn btn-primary" onclick="nextSection()">
                        التالي: المهارات
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </div>

            <!-- Skills Section -->
            <div class="editor-section" id="skills-section" style="display: none;">
                <div class="section-header">
                    <h2>المهارات</h2>
                    <p>أضف مهاراتك التقنية والشخصية</p>
                </div>

                <div class="section-content">
                    <div class="skills-container">
                        <div class="skill-category">
                            <h3>المهارات التقنية</h3>
                            <div class="skill-input-group">
                                <input type="text" id="technicalSkillInput" placeholder="مثال: JavaScript, React, Node.js">
                                <button class="btn btn-primary" onclick="addSkill('technical')">
                                    <i class="fas fa-plus"></i>
                                    إضافة
                                </button>
                            </div>
                            <div id="technicalSkillsList" class="skills-list">
                                <!-- Technical skills will be added here -->
                            </div>
                        </div>

                        <div class="skill-category">
                            <h3>المهارات الشخصية</h3>
                            <div class="skill-input-group">
                                <input type="text" id="softSkillInput" placeholder="مثال: القيادة, التواصل, العمل الجماعي">
                                <button class="btn btn-primary" onclick="addSkill('soft')">
                                    <i class="fas fa-plus"></i>
                                    إضافة
                                </button>
                            </div>
                            <div id="softSkillsList" class="skills-list">
                                <!-- Soft skills will be added here -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-actions">
                    <button class="btn btn-outline" onclick="prevSection()">
                        <i class="fas fa-arrow-right"></i>
                        السابق
                    </button>
                    <button class="btn btn-primary" onclick="nextSection()">
                        التالي: اللغات
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </div>

            <!-- Languages Section -->
            <div class="editor-section" id="languages-section" style="display: none;">
                <div class="section-header">
                    <h2>اللغات</h2>
                    <p>أضف اللغات التي تتقنها ومستوى إتقانك لكل منها</p>
                </div>

                <div class="section-content">
                    <div id="languagesList" class="items-list">
                        <!-- Language items will be added here -->
                    </div>

                    <button class="btn btn-outline btn-add" onclick="addLanguage()">
                        <i class="fas fa-plus"></i>
                        إضافة لغة
                    </button>
                </div>

                <div class="section-actions">
                    <button class="btn btn-outline" onclick="prevSection()">
                        <i class="fas fa-arrow-right"></i>
                        السابق
                    </button>
                    <button class="btn btn-success" onclick="previewCV()">
                        <i class="fas fa-eye"></i>
                        معاينة السيرة الذاتية
                    </button>
                </div>
            </div>
        </main>

        <!-- CV Preview -->
        <aside class="cv-preview-panel">
            <div class="preview-header">
                <h3>معاينة السيرة الذاتية</h3>
                <div class="preview-controls">
                    <button class="btn btn-sm" data-action="zoom-out" title="تصغير">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <span class="zoom-level">100%</span>
                    <button class="btn btn-sm" data-action="zoom-in" title="تكبير">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="btn btn-sm" data-action="zoom-reset" title="إعادة تعيين">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="previewCV()" title="معاينة كاملة">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>

            <!-- Template Selection -->
            <div class="template-selection">
                <h4>اختر القالب</h4>
                <div class="template-options">
                    <button class="template-btn active" data-template="1" title="قالب عصري">
                        <i class="fas fa-file-alt"></i>
                        <span>عصري</span>
                    </button>
                    <button class="template-btn" data-template="2" title="قالب كلاسيكي">
                        <i class="fas fa-file-text"></i>
                        <span>كلاسيكي</span>
                    </button>
                    <button class="template-btn" data-template="3" title="قالب إبداعي">
                        <i class="fas fa-file-image"></i>
                        <span>إبداعي</span>
                    </button>
                </div>
            </div>

            <div class="preview-container">
                <div class="cv-preview-document" id="cvPreview">
                    <!-- CV preview will be generated here -->
                    <div class="cv-page">
                        <div class="cv-header">
                            <div class="cv-photo">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="cv-basic-info">
                                <h1 class="cv-name">اسمك هنا</h1>
                                <h2 class="cv-title">المسمى الوظيفي</h2>
                                <div class="cv-contact">
                                    <div class="contact-item">
                                        <i class="fas fa-envelope"></i>
                                        <span><EMAIL></span>
                                    </div>
                                    <div class="contact-item">
                                        <i class="fas fa-phone"></i>
                                        <span>+966 50 123 4567</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="cv-content">
                            <div class="cv-section">
                                <h3>الملخص المهني</h3>
                                <p>سيتم عرض الملخص المهني هنا...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
    </div>

    <!-- External Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js" defer></script>

    <!-- JavaScript Files -->
    <script src="js/app.js"></script>
    <script src="js/cv-builder.js"></script>
    <script src="js/cv-preview.js"></script>
    <script src="js/ai-assistant.js"></script>
    <script src="js/test-functions.js"></script>

    <!-- Ensure libraries are loaded -->
    <script>
        // Wait for all libraries to load
        function waitForLibraries() {
            return new Promise((resolve) => {
                const checkLibraries = () => {
                    if (typeof jsPDF !== 'undefined' &&
                        typeof html2canvas !== 'undefined' &&
                        typeof QRCode !== 'undefined') {
                        console.log('✅ جميع مكتبات التصدير جاهزة');
                        resolve();
                    } else {
                        setTimeout(checkLibraries, 100);
                    }
                };
                checkLibraries();
            });
        }

        // Initialize when libraries are ready
        document.addEventListener('DOMContentLoaded', () => {
            waitForLibraries().then(() => {
                if (window.cvPreview) {
                    window.cvPreview.setupExportFunctions();
                }
            });
        });
    </script>
</body>
</html>
