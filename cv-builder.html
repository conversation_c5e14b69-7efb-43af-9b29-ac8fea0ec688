<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منشئ السيرة الذاتية المتقدم - إلاشرافي</title>
    <meta name="description" content="أنشئ سيرتك الذاتية المهنية بسهولة مع أدوات متقدمة وقوالب احترافية">
    <meta name="keywords" content="سيرة ذاتية، CV، منشئ السيرة الذاتية، وظائف، مهن">

    <!-- خطوط عربية احترافية -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <!-- أيقونات Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- ملفات CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/cv-builder-new.css">
    <link rel="stylesheet" href="css/cv-templates.css">
    <link rel="stylesheet" href="css/cv-templates-advanced.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <i class="fas fa-file-alt"></i>
            </div>
            <h2>منشئ السيرة الذاتية المتقدم</h2>
            <div class="loading-spinner"></div>
            <p>جاري تحميل الأدوات المتقدمة...</p>
        </div>
    </div>

    <!-- الحاوي الرئيسي -->
    <div id="mainContainer" class="main-container hidden">
        <!-- شريط التنقل العلوي -->
        <header class="top-navbar">
            <div class="navbar-content">
                <div class="navbar-brand">
                    <i class="fas fa-file-alt"></i>
                    <span>منشئ السيرة الذاتية</span>
                </div>

                <div class="navbar-actions">
                    <button class="btn btn-outline" onclick="cvBuilder.saveProgress()">
                        <i class="fas fa-save"></i>
                        حفظ التقدم
                    </button>
                    <button class="btn btn-primary" onclick="cvBuilder.previewCV()">
                        <i class="fas fa-eye"></i>
                        معاينة
                    </button>
                    <button class="btn btn-success" onclick="cvBuilder.exportToPDF()">
                        <i class="fas fa-download"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
        </header>

        <!-- شريط التقدم -->
        <div class="progress-bar-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text">
                <span id="progressText">0% مكتمل</span>
                <span id="progressStep">الخطوة 1 من 6</span>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- الشريط الجانبي للتنقل -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3>خطوات إنشاء السيرة الذاتية</h3>
                </div>

                <nav class="steps-navigation">
                    <div class="step-item active" data-step="1">
                        <div class="step-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="step-content">
                            <h4>المعلومات الشخصية</h4>
                            <p>الاسم والتواصل والصورة</p>
                        </div>
                        <div class="step-status">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>

                    <div class="step-item" data-step="2">
                        <div class="step-icon">
                            <i class="fas fa-file-text"></i>
                        </div>
                        <div class="step-content">
                            <h4>الملخص المهني</h4>
                            <p>نبذة عن خبراتك ومهاراتك</p>
                        </div>
                        <div class="step-status">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>

                    <div class="step-item" data-step="3">
                        <div class="step-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="step-content">
                            <h4>الخبرات العملية</h4>
                            <p>تاريخك المهني والوظائف</p>
                        </div>
                        <div class="step-status">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>

                    <div class="step-item" data-step="4">
                        <div class="step-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="step-content">
                            <h4>التعليم والمؤهلات</h4>
                            <p>شهاداتك الأكاديمية</p>
                        </div>
                        <div class="step-status">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>

                    <div class="step-item" data-step="5">
                        <div class="step-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="step-content">
                            <h4>المهارات</h4>
                            <p>مهاراتك التقنية والشخصية</p>
                        </div>
                        <div class="step-status">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>

                    <div class="step-item" data-step="6">
                        <div class="step-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div class="step-content">
                            <h4>التصميم والتخصيص</h4>
                            <p>اختر القالب والألوان</p>
                        </div>
                        <div class="step-status">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                </nav>

                <!-- إحصائيات التقدم -->
                <div class="progress-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="completedFields">0</div>
                        <div class="stat-label">حقل مكتمل</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalFields">25</div>
                        <div class="stat-label">إجمالي الحقول</div>
                    </div>
                </div>

                <!-- أزرار الذكاء الاصطناعي -->
                <div class="ai-controls" style="margin-top: var(--spacing-lg);">
                    <button class="btn btn-primary btn-sm" onclick="cvAI && cvAI.analyzeOverallQuality()" style="width: 100%; margin-bottom: var(--spacing-sm);">
                        <i class="fas fa-robot"></i>
                        تحليل جودة السيرة الذاتية
                    </button>
                    <button class="btn btn-outline btn-sm" onclick="cvAI && cvAI.exportQualityReport()" style="width: 100%;">
                        <i class="fas fa-download"></i>
                        تصدير تقرير الجودة
                    </button>
                </div>
            </aside>

            <!-- منطقة المحتوى -->
            <section class="content-area">
                <!-- حاوي النماذج -->
                <div class="form-container">
                    <!-- الخطوة 1: المعلومات الشخصية -->
                    <div class="step-content active" id="step1">
                        <div class="step-header">
                            <h2>
                                <i class="fas fa-user"></i>
                                المعلومات الشخصية
                            </h2>
                            <p>أدخل معلوماتك الشخصية الأساسية التي ستظهر في أعلى سيرتك الذاتية</p>
                        </div>

                        <div class="form-section">
                            <!-- رفع الصورة الشخصية -->
                            <div class="photo-upload-section">
                                <label class="section-label">الصورة الشخصية</label>
                                <div class="photo-upload-area" id="photoUploadArea">
                                    <div class="photo-preview" id="photoPreview">
                                        <i class="fas fa-camera"></i>
                                        <h4>اسحب الصورة هنا أو انقر للاختيار</h4>
                                        <p>يُفضل صورة بحجم 300x300 بكسل</p>
                                        <small>الحد الأقصى: 5 ميجابايت (JPG, PNG)</small>
                                    </div>
                                    <input type="file" id="photoInput" accept="image/*" hidden>
                                </div>
                            </div>

                            <!-- المعلومات الأساسية -->
                            <div class="form-grid">
                                <div class="form-group full-width">
                                    <label for="fullName" class="required">الاسم الكامل</label>
                                    <input type="text" id="fullName" name="fullName" required
                                           placeholder="أدخل اسمك الكامل كما تريد أن يظهر في السيرة الذاتية"
                                           autocomplete="name">
                                    <div class="field-hint">مثال: أحمد محمد علي السعيد</div>
                                    <div class="error-message" id="fullNameError"></div>
                                </div>

                                <div class="form-group full-width">
                                    <label for="jobTitle" class="required">المسمى الوظيفي المطلوب</label>
                                    <input type="text" id="jobTitle" name="jobTitle" required
                                           placeholder="مثال: مطور تطبيقات الويب"
                                           autocomplete="organization-title">
                                    <div class="field-hint">المنصب الذي تسعى للحصول عليه</div>
                                    <div class="error-message" id="jobTitleError"></div>
                                </div>

                                <div class="form-group">
                                    <label for="email" class="required">البريد الإلكتروني</label>
                                    <input type="email" id="email" name="email" required
                                           placeholder="<EMAIL>"
                                           autocomplete="email">
                                    <div class="error-message" id="emailError"></div>
                                </div>

                                <div class="form-group">
                                    <label for="phone" class="required">رقم الهاتف</label>
                                    <input type="tel" id="phone" name="phone" required
                                           placeholder="+966 50 123 4567"
                                           autocomplete="tel">
                                    <div class="error-message" id="phoneError"></div>
                                </div>

                                <div class="form-group">
                                    <label for="city">المدينة والدولة</label>
                                    <input type="text" id="city" name="city"
                                           placeholder="الرياض، المملكة العربية السعودية"
                                           autocomplete="address-level1">
                                    <div class="error-message" id="cityError"></div>
                                </div>

                                <div class="form-group">
                                    <label for="website">الموقع الشخصي</label>
                                    <input type="url" id="website" name="website"
                                           placeholder="https://yourwebsite.com"
                                           autocomplete="url">
                                    <div class="error-message" id="websiteError"></div>
                                </div>

                                <div class="form-group">
                                    <label for="linkedin">LinkedIn</label>
                                    <input type="url" id="linkedin" name="linkedin"
                                           placeholder="https://linkedin.com/in/yourprofile"
                                           autocomplete="url">
                                    <div class="error-message" id="linkedinError"></div>
                                </div>

                                <div class="form-group">
                                    <label for="github">GitHub</label>
                                    <input type="url" id="github" name="github"
                                           placeholder="https://github.com/yourusername"
                                           autocomplete="url">
                                    <div class="error-message" id="githubError"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الخطوة 2: الملخص المهني -->
                    <div class="step-content" id="step2">
                        <div class="step-header">
                            <h2>
                                <i class="fas fa-file-text"></i>
                                الملخص المهني
                            </h2>
                            <p>اكتب ملخصاً مهنياً يبرز خبراتك ومهاراتك الرئيسية في 2-3 جمل</p>
                        </div>

                        <div class="form-section">
                            <div class="form-group">
                                <label for="professionalSummary" class="required">الملخص المهني</label>
                                <textarea id="professionalSummary" name="professionalSummary" rows="6" required
                                          placeholder="مثال: مطور ويب محترف مع أكثر من 5 سنوات من الخبرة في تطوير التطبيقات الحديثة باستخدام JavaScript وReact. متخصص في إنشاء واجهات مستخدم تفاعلية وحلول تقنية مبتكرة. شغوف بالتعلم المستمر ومواكبة أحدث التقنيات في مجال تطوير الويب."></textarea>
                                <div class="character-counter">
                                    <span id="summaryCounter">0</span> / 500 حرف
                                </div>
                                <div class="field-hint">اكتب ملخصاً يجذب انتباه صاحب العمل ويبرز نقاط قوتك</div>
                                <div class="error-message" id="professionalSummaryError"></div>
                            </div>

                            <!-- نصائح للملخص المهني -->
                            <div class="tips-section">
                                <h4><i class="fas fa-lightbulb"></i> نصائح لكتابة ملخص مهني مميز:</h4>
                                <ul>
                                    <li>ابدأ بذكر مسماك الوظيفي وسنوات الخبرة</li>
                                    <li>اذكر أهم مهاراتك التقنية أو المجالات التي تتخصص فيها</li>
                                    <li>أضف إنجازاً مهماً أو نقطة قوة تميزك</li>
                                    <li>اختتم بهدفك المهني أو ما تسعى لتحقيقه</li>
                                    <li>استخدم كلمات مفتاحية متعلقة بمجال عملك</li>
                                </ul>
                            </div>

                            <!-- أمثلة للملخص المهني -->
                            <div class="examples-section">
                                <h4><i class="fas fa-file-text"></i> أمثلة للملخص المهني:</h4>
                                <div class="example-cards">
                                    <div class="example-card" onclick="cvBuilder.fillExampleSummary('developer')">
                                        <h5>مطور برمجيات</h5>
                                        <p>مطور برمجيات محترف مع أكثر من 5 سنوات من الخبرة في تطوير التطبيقات الحديثة...</p>
                                    </div>
                                    <div class="example-card" onclick="cvBuilder.fillExampleSummary('designer')">
                                        <h5>مصمم جرافيك</h5>
                                        <p>مصمم جرافيك إبداعي متخصص في تصميم الهوية البصرية والتسويق الرقمي...</p>
                                    </div>
                                    <div class="example-card" onclick="cvBuilder.fillExampleSummary('manager')">
                                        <h5>مدير مشاريع</h5>
                                        <p>مدير مشاريع معتمد PMP مع خبرة 8 سنوات في إدارة المشاريع التقنية...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الخطوة 3: الخبرات العملية -->
                    <div class="step-content" id="step3">
                        <div class="step-header">
                            <h2>
                                <i class="fas fa-briefcase"></i>
                                الخبرات العملية
                            </h2>
                            <p>أضف خبراتك العملية مرتبة من الأحدث إلى الأقدم</p>
                        </div>

                        <div class="form-section">
                            <div class="experiences-container" id="experiencesContainer">
                                <!-- سيتم إضافة الخبرات هنا ديناميكياً -->
                            </div>

                            <button type="button" class="btn btn-outline add-item-btn" onclick="cvBuilder.addExperience()">
                                <i class="fas fa-plus"></i>
                                إضافة خبرة عملية
                            </button>
                        </div>
                    </div>

                    <!-- الخطوة 4: التعليم والمؤهلات -->
                    <div class="step-content" id="step4">
                        <div class="step-header">
                            <h2>
                                <i class="fas fa-graduation-cap"></i>
                                التعليم والمؤهلات
                            </h2>
                            <p>أضف مؤهلاتك الأكاديمية والشهادات المهنية</p>
                        </div>

                        <div class="form-section">
                            <div class="education-container" id="educationContainer">
                                <!-- سيتم إضافة المؤهلات هنا ديناميكياً -->
                            </div>

                            <button type="button" class="btn btn-outline add-item-btn" onclick="cvBuilder.addEducation()">
                                <i class="fas fa-plus"></i>
                                إضافة مؤهل أكاديمي
                            </button>
                        </div>
                    </div>

                    <!-- الخطوة 5: المهارات -->
                    <div class="step-content" id="step5">
                        <div class="step-header">
                            <h2>
                                <i class="fas fa-cogs"></i>
                                المهارات
                            </h2>
                            <p>أضف مهاراتك التقنية والشخصية واللغات</p>
                        </div>

                        <div class="form-section">
                            <!-- المهارات التقنية -->
                            <div class="skills-section">
                                <h3><i class="fas fa-code"></i> المهارات التقنية</h3>
                                <div class="form-group">
                                    <label for="technicalSkills">المهارات التقنية</label>
                                    <div class="skills-input-container">
                                        <input type="text" id="technicalSkillInput" placeholder="أدخل مهارة تقنية واضغط Enter">
                                        <button type="button" class="btn btn-sm btn-primary" onclick="cvBuilder.addTechnicalSkill()">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    <div class="skills-tags" id="technicalSkillsTags">
                                        <!-- ستظهر المهارات هنا -->
                                    </div>
                                    <div class="field-hint">مثال: JavaScript, React, Node.js, Python, SQL</div>
                                </div>
                            </div>

                            <!-- المهارات الشخصية -->
                            <div class="skills-section">
                                <h3><i class="fas fa-users"></i> المهارات الشخصية</h3>
                                <div class="form-group">
                                    <label for="softSkills">المهارات الشخصية</label>
                                    <div class="skills-input-container">
                                        <input type="text" id="softSkillInput" placeholder="أدخل مهارة شخصية واضغط Enter">
                                        <button type="button" class="btn btn-sm btn-primary" onclick="cvBuilder.addSoftSkill()">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    <div class="skills-tags" id="softSkillsTags">
                                        <!-- ستظهر المهارات هنا -->
                                    </div>
                                    <div class="field-hint">مثال: العمل الجماعي, القيادة, حل المشكلات, التواصل الفعال</div>
                                </div>
                            </div>

                            <!-- اللغات -->
                            <div class="skills-section">
                                <h3><i class="fas fa-language"></i> اللغات</h3>
                                <div class="languages-container" id="languagesContainer">
                                    <!-- سيتم إضافة اللغات هنا ديناميكياً -->
                                </div>

                                <button type="button" class="btn btn-outline add-item-btn" onclick="cvBuilder.addLanguage()">
                                    <i class="fas fa-plus"></i>
                                    إضافة لغة
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- الخطوة 6: التصميم والتخصيص -->
                    <div class="step-content" id="step6">
                        <div class="step-header">
                            <h2>
                                <i class="fas fa-palette"></i>
                                التصميم والتخصيص
                            </h2>
                            <p>اختر القالب والألوان التي تناسب شخصيتك المهنية</p>
                        </div>

                        <div class="form-section">
                            <!-- اختيار القالب -->
                            <div class="template-selection-section">
                                <h3><i class="fas fa-file-alt"></i> اختيار القالب</h3>
                                <div class="templates-grid">
                                    <div class="template-card active" data-template="modern">
                                        <div class="template-preview">
                                            <img src="assets/images/template-modern.png" alt="القالب العصري">
                                        </div>
                                        <div class="template-info">
                                            <h4>القالب العصري</h4>
                                            <p>تصميم حديث ومتطور</p>
                                        </div>
                                    </div>

                                    <div class="template-card" data-template="classic">
                                        <div class="template-preview">
                                            <img src="assets/images/template-classic.png" alt="القالب الكلاسيكي">
                                        </div>
                                        <div class="template-info">
                                            <h4>القالب الكلاسيكي</h4>
                                            <p>تصميم تقليدي ومهني</p>
                                        </div>
                                    </div>

                                    <div class="template-card" data-template="creative">
                                        <div class="template-preview">
                                            <img src="assets/images/template-creative.png" alt="القالب الإبداعي">
                                        </div>
                                        <div class="template-info">
                                            <h4>القالب الإبداعي</h4>
                                            <p>تصميم مبتكر وجذاب</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- اختيار الألوان -->
                            <div class="color-selection-section">
                                <h3><i class="fas fa-palette"></i> اختيار الألوان</h3>
                                <div class="color-schemes">
                                    <div class="color-scheme active" data-scheme="blue">
                                        <div class="color-preview">
                                            <div class="color-primary" style="background: #1e40af;"></div>
                                            <div class="color-secondary" style="background: #3b82f6;"></div>
                                        </div>
                                        <span>الأزرق المهني</span>
                                    </div>

                                    <div class="color-scheme" data-scheme="green">
                                        <div class="color-preview">
                                            <div class="color-primary" style="background: #059669;"></div>
                                            <div class="color-secondary" style="background: #10b981;"></div>
                                        </div>
                                        <span>الأخضر الطبيعي</span>
                                    </div>

                                    <div class="color-scheme" data-scheme="purple">
                                        <div class="color-preview">
                                            <div class="color-primary" style="background: #7c3aed;"></div>
                                            <div class="color-secondary" style="background: #8b5cf6;"></div>
                                        </div>
                                        <span>البنفسجي الإبداعي</span>
                                    </div>

                                    <div class="color-scheme" data-scheme="orange">
                                        <div class="color-preview">
                                            <div class="color-primary" style="background: #ea580c;"></div>
                                            <div class="color-secondary" style="background: #f97316;"></div>
                                        </div>
                                        <span>البرتقالي النشط</span>
                                    </div>
                                </div>
                            </div>

                            <!-- خيارات إضافية -->
                            <div class="additional-options">
                                <h3><i class="fas fa-cog"></i> خيارات إضافية</h3>
                                <div class="options-grid">
                                    <div class="option-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="includePhoto" checked>
                                            <span class="checkmark"></span>
                                            تضمين الصورة الشخصية
                                        </label>
                                    </div>

                                    <div class="option-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="includeQR">
                                            <span class="checkmark"></span>
                                            إضافة رمز QR للمعلومات
                                        </label>
                                    </div>

                                    <div class="option-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="twoColumns">
                                            <span class="checkmark"></span>
                                            تخطيط عمودين
                                        </label>
                                    </div>

                                    <div class="option-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="showProgress" checked>
                                            <span class="checkmark"></span>
                                            إظهار مستوى المهارات
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار التنقل -->
                <div class="navigation-buttons">
                    <button class="btn btn-outline" id="prevBtn" onclick="cvBuilder.previousStep()" disabled>
                        <i class="fas fa-arrow-right"></i>
                        السابق
                    </button>

                    <button class="btn btn-primary" id="nextBtn" onclick="cvBuilder.nextStep()">
                        التالي
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </section>

            <!-- لوحة المعاينة -->
            <aside class="preview-panel">
                <div class="preview-header">
                    <h3>
                        <i class="fas fa-eye"></i>
                        معاينة مباشرة
                    </h3>
                    <div class="preview-controls">
                        <button class="control-btn" onclick="cvBuilder.zoomOut()" title="تصغير">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <span class="zoom-level" id="zoomLevel">100%</span>
                        <button class="control-btn" onclick="cvBuilder.zoomIn()" title="تكبير">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="control-btn" onclick="cvBuilder.resetZoom()" title="إعادة تعيين">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button class="control-btn" onclick="cvBuilder.fullPreview()" title="معاينة كاملة">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>

                <!-- اختيار القالب -->
                <div class="template-selector">
                    <label>اختر القالب:</label>
                    <div class="template-options">
                        <button class="template-option active" data-template="modern" title="قالب عصري">
                            <i class="fas fa-file-alt"></i>
                            <span>عصري</span>
                        </button>
                        <button class="template-option" data-template="classic" title="قالب كلاسيكي">
                            <i class="fas fa-file-text"></i>
                            <span>كلاسيكي</span>
                        </button>
                        <button class="template-option" data-template="creative" title="قالب إبداعي">
                            <i class="fas fa-file-image"></i>
                            <span>إبداعي</span>
                        </button>
                    </div>
                </div>

                <!-- منطقة المعاينة -->
                <div class="preview-container">
                    <div class="cv-preview" id="cvPreview">
                        <div class="preview-placeholder">
                            <i class="fas fa-file-alt"></i>
                            <h4>ابدأ في ملء المعلومات</h4>
                            <p>ستظهر معاينة سيرتك الذاتية هنا</p>
                        </div>
                    </div>
                </div>
            </aside>
        </main>
    </div>

    <!-- نافذة المعاينة الكاملة -->
    <div id="fullPreviewModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>معاينة السيرة الذاتية</h3>
                <button class="close-btn" onclick="cvBuilder.closeFullPreview()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="full-preview-container" id="fullPreviewContainer">
                    <!-- محتوى المعاينة الكاملة -->
                </div>
            </div>
        </div>
    </div>

    <!-- نظام الإشعارات -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- مكتبات خارجية -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <!-- ملفات JavaScript -->
    <script src="js/cv-ai-config.js"></script>
    <script src="js/cv-builder-core.js"></script>
    <script src="js/cv-templates-engine.js"></script>
    <script src="js/cv-validation.js"></script>
    <script src="js/cv-export.js"></script>
    <script src="js/cv-ai-assistant.js"></script>

    <script>
        // تهيئة التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إخفاء شاشة التحميل وإظهار المحتوى الرئيسي
            setTimeout(() => {
                document.getElementById('loadingScreen').classList.add('fade-out');
                setTimeout(() => {
                    document.getElementById('loadingScreen').style.display = 'none';
                    document.getElementById('mainContainer').classList.remove('hidden');

                    // تهيئة منشئ السيرة الذاتية
                    window.cvBuilder = new CVBuilder();
                }, 500);
            }, 2000);
        });
    </script>
</body>
</html>
