// ===== Homepage AI Assistant =====

class HomepageAI {
    constructor() {
        this.isOpen = false;
        this.userField = null;
        this.chatHistory = [];
        this.responses = {
            'مطور ويب': {
                message: 'ممتاز! كمطور ويب، أنصحك بـ:',
                tips: [
                    'استخدم القالب العصري الأزرق - مثالي للمطورين',
                    'اذكر اللغات البرمجية: JavaScript, React, Node.js',
                    'أضف رابط GitHub و portfolio',
                    'ركز على المشاريع التي طورتها'
                ],
                template: 'modern',
                skills: ['JavaScript', 'React', 'Node.js', 'HTML/CSS', 'Git', 'MongoDB']
            },
            'مصمم جرافيك': {
                message: 'رائع! كمصمم جرافيك، أنصحك بـ:',
                tips: [
                    'استخدم القالب الإبداعي الملون',
                    'أضف رابط portfolio يعرض أعمالك',
                    'اذكر برامج التصميم: Photoshop, Illustrator',
                    'ركز على المشاريع المرئية'
                ],
                template: 'creative',
                skills: ['Photoshop', 'Illustrator', 'InDesign', 'Figma', 'Branding', 'UI/UX']
            },
            'مدير مشاريع': {
                message: 'ممتاز! كمدير مشاريع، أنصحك بـ:',
                tips: [
                    'استخدم القالب المهني الأنيق',
                    'اذكر عدد الفرق التي أدرتها',
                    'ركز على النتائج والإنجازات بالأرقام',
                    'أضف شهادات PMP إن وجدت'
                ],
                template: 'professional',
                skills: ['إدارة المشاريع', 'القيادة', 'Agile', 'Scrum', 'التخطيط الاستراتيجي']
            },
            'محاسب': {
                message: 'ممتاز! كمحاسب، أنصحك بـ:',
                tips: [
                    'استخدم القالب الكلاسيكي الأنيق',
                    'اذكر برامج المحاسبة: Excel, QuickBooks',
                    'ركز على الدقة والتفاصيل',
                    'أضف الشهادات المهنية'
                ],
                template: 'classic',
                skills: ['Excel', 'QuickBooks', 'SAP', 'التحليل المالي', 'إعداد التقارير']
            }
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Auto-show after 10 seconds if not interacted
        setTimeout(() => {
            if (!this.isOpen && !localStorage.getItem('ai_assistant_dismissed')) {
                this.showWelcomeMessage();
            }
        }, 10000);
    }
    
    showWelcomeMessage() {
        const toggleBtn = document.querySelector('.ai-toggle-btn');
        if (toggleBtn) {
            // Add attention animation
            toggleBtn.style.animation = 'bounce 1s infinite';
            
            // Show tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'ai-tooltip';
            tooltip.innerHTML = 'مرحباً! يمكنني مساعدتك في إنشاء سيرة ذاتية مميزة 👋';
            tooltip.style.cssText = `
                position: absolute;
                bottom: 70px;
                left: 0;
                background: var(--primary-color);
                color: white;
                padding: 10px 15px;
                border-radius: 8px;
                font-size: 0.875rem;
                white-space: nowrap;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: fadeInUp 0.3s ease-out;
            `;
            
            document.getElementById('aiFloatingAssistant').appendChild(tooltip);
            
            // Remove tooltip after 5 seconds
            setTimeout(() => {
                if (tooltip.parentElement) {
                    tooltip.remove();
                    toggleBtn.style.animation = '';
                }
            }, 5000);
        }
    }
    
    toggle() {
        const chatWindow = document.getElementById('aiChatWindow');
        const toggleBtn = document.querySelector('.ai-toggle-btn');
        
        if (this.isOpen) {
            chatWindow.style.display = 'none';
            this.isOpen = false;
            toggleBtn.style.animation = '';
        } else {
            chatWindow.style.display = 'flex';
            this.isOpen = true;
            toggleBtn.style.animation = '';
            
            // Remove any existing tooltips
            const tooltip = document.querySelector('.ai-tooltip');
            if (tooltip) tooltip.remove();
            
            // Focus on input
            setTimeout(() => {
                const input = document.getElementById('aiMessageInput');
                if (input) input.focus();
            }, 300);
        }
    }
    
    sendMessage(message) {
        if (!message.trim()) return;
        
        // Add user message
        this.addMessage(message, 'user');
        
        // Clear input
        const input = document.getElementById('aiMessageInput');
        if (input) input.value = '';
        
        // Generate AI response
        setTimeout(() => {
            const response = this.generateResponse(message);
            this.addMessage(response.message, 'ai');
            
            if (response.tips) {
                setTimeout(() => {
                    this.addTipsMessage(response.tips);
                }, 1000);
            }
            
            if (response.actions) {
                setTimeout(() => {
                    this.addActionsMessage(response.actions);
                }, 2000);
            }
        }, 1000);
    }
    
    sendQuickMessage(field) {
        this.userField = field;
        this.sendMessage(field);
    }
    
    addMessage(content, sender) {
        const messagesContainer = document.getElementById('aiChatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `${sender}-message`;
        
        if (sender === 'ai') {
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">${content}</div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="message-content user-msg">${content}</div>
                <div class="message-avatar user-avatar">
                    <i class="fas fa-user"></i>
                </div>
            `;
            messageDiv.style.flexDirection = 'row-reverse';
        }
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Add animation
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateY(10px)';
        setTimeout(() => {
            messageDiv.style.transition = 'all 0.3s ease';
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';
        }, 100);
    }
    
    addTipsMessage(tips) {
        const tipsHTML = tips.map(tip => `<li>${tip}</li>`).join('');
        const content = `<ul style="margin: 5px 0; padding-right: 20px;">${tipsHTML}</ul>`;
        this.addMessage(content, 'ai');
    }
    
    addActionsMessage(actions) {
        const actionsHTML = actions.map(action => 
            `<button class="action-btn" onclick="${action.onclick}" style="
                background: var(--primary-color);
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 6px;
                margin: 5px 5px 5px 0;
                cursor: pointer;
                font-size: 0.8rem;
            ">${action.text}</button>`
        ).join('');
        
        const content = `<div style="margin-top: 10px;">${actionsHTML}</div>`;
        this.addMessage(content, 'ai');
    }
    
    generateResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        // Check for specific fields
        for (const [field, data] of Object.entries(this.responses)) {
            if (lowerMessage.includes(field.toLowerCase())) {
                return {
                    message: data.message,
                    tips: data.tips,
                    actions: [
                        {
                            text: `عرض قوالب ${field}`,
                            onclick: `viewTemplatesForField('${data.template}')`
                        },
                        {
                            text: 'ابدأ إنشاء السيرة الذاتية',
                            onclick: 'startCVBuilder()'
                        }
                    ]
                };
            }
        }
        
        // General responses
        if (lowerMessage.includes('قالب') || lowerMessage.includes('تصميم')) {
            return {
                message: 'لدينا أكثر من 10 قوالب احترافية مختلفة:',
                tips: [
                    'القوالب العصرية - للمجالات التقنية',
                    'القوالب الكلاسيكية - للمجالات الرسمية',
                    'القوالب الإبداعية - للمصممين والفنانين',
                    'القوالب البسيطة - لجميع المجالات'
                ],
                actions: [
                    {
                        text: 'عرض جميع القوالب',
                        onclick: 'viewAllTemplates()'
                    }
                ]
            };
        }
        
        if (lowerMessage.includes('مهارات') || lowerMessage.includes('خبرة')) {
            return {
                message: 'يمكنني مساعدتك في اختيار المهارات المناسبة:',
                tips: [
                    'المهارات التقنية - حسب مجال عملك',
                    'المهارات الشخصية - مثل القيادة والتواصل',
                    'الشهادات والدورات التدريبية',
                    'اللغات ومستوى إتقانها'
                ]
            };
        }
        
        if (lowerMessage.includes('مساعدة') || lowerMessage.includes('كيف')) {
            return {
                message: 'يمكنني مساعدتك في عدة أمور:',
                tips: [
                    'اختيار القالب المناسب لمجالك',
                    'كتابة ملخص مهني مميز',
                    'تنظيم خبراتك العملية',
                    'اقتراح المهارات المطلوبة',
                    'تحسين محتوى سيرتك الذاتية'
                ],
                actions: [
                    {
                        text: 'ابدأ الآن',
                        onclick: 'startCVBuilder()'
                    }
                ]
            };
        }
        
        // Default response
        return {
            message: 'شكراً لك! ما هو مجالك المهني؟ يمكنني تقديم نصائح مخصصة لك.',
            actions: [
                {
                    text: 'عرض القوالب',
                    onclick: 'viewAllTemplates()'
                },
                {
                    text: 'ابدأ إنشاء السيرة الذاتية',
                    onclick: 'startCVBuilder()'
                }
            ]
        };
    }
}

// Global functions
function toggleAIAssistant() {
    if (!window.homepageAI) {
        window.homepageAI = new HomepageAI();
    }
    window.homepageAI.toggle();
}

function sendAIMessage() {
    const input = document.getElementById('aiMessageInput');
    if (input && window.homepageAI) {
        window.homepageAI.sendMessage(input.value);
    }
}

function handleAIKeyPress(event) {
    if (event.key === 'Enter') {
        sendAIMessage();
    }
}

function sendQuickMessage(field) {
    if (window.homepageAI) {
        window.homepageAI.sendQuickMessage(field);
    }
}

function viewTemplatesForField(templateType) {
    localStorage.setItem('preferred_template_type', templateType);
    window.location.href = 'templates.html';
}

function viewAllTemplates() {
    window.location.href = 'templates.html';
}

function startCVBuilder() {
    window.location.href = 'cv-builder.html';
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.homepageAI = new HomepageAI();
});
