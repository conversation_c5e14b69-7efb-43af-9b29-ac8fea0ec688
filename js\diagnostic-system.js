// ===== Comprehensive Diagnostic System =====

class CVDiagnosticSystem {
    constructor() {
        this.testResults = [];
        this.errors = [];
        this.warnings = [];
        this.browserInfo = this.getBrowserInfo();
        this.init();
    }

    init() {
        console.log('🔍 تهيئة نظام التشخيص الشامل...');
        this.runAllDiagnostics();
    }

    getBrowserInfo() {
        const ua = navigator.userAgent;
        const browser = {
            name: 'Unknown',
            version: 'Unknown',
            engine: 'Unknown'
        };

        if (ua.includes('Chrome')) {
            browser.name = 'Chrome';
            browser.version = ua.match(/Chrome\/(\d+)/)?.[1] || 'Unknown';
            browser.engine = 'Blink';
        } else if (ua.includes('Firefox')) {
            browser.name = 'Firefox';
            browser.version = ua.match(/Firefox\/(\d+)/)?.[1] || 'Unknown';
            browser.engine = 'Gecko';
        } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
            browser.name = 'Safari';
            browser.version = ua.match(/Version\/(\d+)/)?.[1] || 'Unknown';
            browser.engine = 'WebKit';
        } else if (ua.includes('Edge')) {
            browser.name = 'Edge';
            browser.version = ua.match(/Edge\/(\d+)/)?.[1] || 'Unknown';
            browser.engine = 'Blink';
        }

        return browser;
    }

    async runAllDiagnostics() {
        console.log(`🌐 تشغيل التشخيص على ${this.browserInfo.name} ${this.browserInfo.version}`);
        
        // Core functionality tests
        await this.testLibraryLoading();
        await this.testDOMElements();
        await this.testCVBuilderInitialization();
        await this.testPreviewSystem();
        await this.testTemplateSystem();
        await this.testFormValidation();
        await this.testDataPersistence();
        await this.testExportFunctionality();
        await this.testCrossBrowserCompatibility();
        await this.testResponsiveDesign();
        
        this.generateReport();
    }

    async testLibraryLoading() {
        const test = { name: 'Library Loading', status: 'running', details: [] };
        
        try {
            // Test jsPDF
            if (typeof jsPDF === 'undefined' && typeof window.jsPDF === 'undefined') {
                test.details.push('❌ jsPDF library not loaded');
                this.errors.push('jsPDF library missing');
            } else {
                test.details.push('✅ jsPDF library loaded');
            }

            // Test html2canvas
            if (typeof html2canvas === 'undefined') {
                test.details.push('❌ html2canvas library not loaded');
                this.errors.push('html2canvas library missing');
            } else {
                test.details.push('✅ html2canvas library loaded');
            }

            // Test QRCode
            if (typeof QRCode === 'undefined') {
                test.details.push('❌ QRCode library not loaded');
                this.errors.push('QRCode library missing');
            } else {
                test.details.push('✅ QRCode library loaded');
            }

            test.status = this.errors.length === 0 ? 'passed' : 'failed';
        } catch (error) {
            test.status = 'failed';
            test.details.push(`❌ Error: ${error.message}`);
            this.errors.push(`Library loading error: ${error.message}`);
        }

        this.testResults.push(test);
    }

    async testDOMElements() {
        const test = { name: 'DOM Elements', status: 'running', details: [] };
        
        try {
            const requiredElements = [
                '.cv-editor',
                '.cv-preview-panel',
                '.cv-page',
                '.cv-preview-document',
                '.preview-controls',
                '.template-selection'
            ];

            requiredElements.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    test.details.push(`✅ ${selector} found`);
                } else {
                    test.details.push(`❌ ${selector} missing`);
                    this.errors.push(`Missing DOM element: ${selector}`);
                }
            });

            test.status = this.errors.length === 0 ? 'passed' : 'failed';
        } catch (error) {
            test.status = 'failed';
            test.details.push(`❌ Error: ${error.message}`);
            this.errors.push(`DOM elements error: ${error.message}`);
        }

        this.testResults.push(test);
    }

    async testCVBuilderInitialization() {
        const test = { name: 'CV Builder Initialization', status: 'running', details: [] };
        
        try {
            if (typeof CVBuilder === 'undefined') {
                test.details.push('❌ CVBuilder class not defined');
                this.errors.push('CVBuilder class missing');
            } else {
                test.details.push('✅ CVBuilder class defined');
            }

            if (typeof window.cvBuilder === 'undefined') {
                test.details.push('❌ CVBuilder instance not created');
                this.errors.push('CVBuilder instance missing');
            } else {
                test.details.push('✅ CVBuilder instance created');
                
                if (window.cvBuilder.isInitialized) {
                    test.details.push('✅ CVBuilder initialized successfully');
                } else {
                    test.details.push('❌ CVBuilder not initialized');
                    this.errors.push('CVBuilder initialization failed');
                }
            }

            test.status = this.errors.length === 0 ? 'passed' : 'failed';
        } catch (error) {
            test.status = 'failed';
            test.details.push(`❌ Error: ${error.message}`);
            this.errors.push(`CV Builder initialization error: ${error.message}`);
        }

        this.testResults.push(test);
    }

    async testPreviewSystem() {
        const test = { name: 'Preview System', status: 'running', details: [] };
        
        try {
            if (typeof CVPreview === 'undefined') {
                test.details.push('❌ CVPreview class not defined');
                this.errors.push('CVPreview class missing');
            } else {
                test.details.push('✅ CVPreview class defined');
            }

            if (typeof window.cvPreview === 'undefined') {
                test.details.push('❌ CVPreview instance not created');
                this.errors.push('CVPreview instance missing');
            } else {
                test.details.push('✅ CVPreview instance created');
            }

            // Test real-time updates
            if (window.cvBuilder && window.cvPreview) {
                const testData = { personal: { fullName: 'Test User' } };
                const event = new CustomEvent('cvDataUpdated', { detail: testData });
                document.dispatchEvent(event);
                test.details.push('✅ Real-time update event dispatched');
            }

            test.status = 'passed';
        } catch (error) {
            test.status = 'failed';
            test.details.push(`❌ Error: ${error.message}`);
            this.errors.push(`Preview system error: ${error.message}`);
        }

        this.testResults.push(test);
    }

    async testTemplateSystem() {
        const test = { name: 'Template System', status: 'running', details: [] };
        
        try {
            const templateButtons = document.querySelectorAll('[data-template]');
            if (templateButtons.length === 0) {
                test.details.push('❌ No template buttons found');
                this.errors.push('Template buttons missing');
            } else {
                test.details.push(`✅ ${templateButtons.length} template buttons found`);
            }

            // Test template switching
            if (window.cvPreview && typeof window.cvPreview.switchTemplate === 'function') {
                test.details.push('✅ Template switching function available');
            } else {
                test.details.push('❌ Template switching function missing');
                this.errors.push('Template switching not available');
            }

            test.status = this.errors.length === 0 ? 'passed' : 'failed';
        } catch (error) {
            test.status = 'failed';
            test.details.push(`❌ Error: ${error.message}`);
            this.errors.push(`Template system error: ${error.message}`);
        }

        this.testResults.push(test);
    }

    async testFormValidation() {
        const test = { name: 'Form Validation', status: 'running', details: [] };
        
        try {
            const forms = document.querySelectorAll('form, .form-section');
            if (forms.length === 0) {
                test.details.push('❌ No forms found');
                this.warnings.push('No forms found for validation testing');
            } else {
                test.details.push(`✅ ${forms.length} forms found`);
            }

            // Test input validation
            const inputs = document.querySelectorAll('input[required], textarea[required]');
            test.details.push(`✅ ${inputs.length} required inputs found`);

            test.status = 'passed';
        } catch (error) {
            test.status = 'failed';
            test.details.push(`❌ Error: ${error.message}`);
            this.errors.push(`Form validation error: ${error.message}`);
        }

        this.testResults.push(test);
    }

    async testDataPersistence() {
        const test = { name: 'Data Persistence', status: 'running', details: [] };
        
        try {
            // Test localStorage availability
            if (typeof Storage !== 'undefined') {
                test.details.push('✅ localStorage available');
                
                // Test saving data
                const testData = { test: 'data', timestamp: Date.now() };
                localStorage.setItem('cv_diagnostic_test', JSON.stringify(testData));
                
                // Test loading data
                const loadedData = JSON.parse(localStorage.getItem('cv_diagnostic_test'));
                if (loadedData && loadedData.test === 'data') {
                    test.details.push('✅ Data persistence working');
                    localStorage.removeItem('cv_diagnostic_test');
                } else {
                    test.details.push('❌ Data persistence failed');
                    this.errors.push('Data persistence not working');
                }
            } else {
                test.details.push('❌ localStorage not available');
                this.errors.push('localStorage not supported');
            }

            test.status = this.errors.length === 0 ? 'passed' : 'failed';
        } catch (error) {
            test.status = 'failed';
            test.details.push(`❌ Error: ${error.message}`);
            this.errors.push(`Data persistence error: ${error.message}`);
        }

        this.testResults.push(test);
    }

    async testExportFunctionality() {
        const test = { name: 'Export Functionality', status: 'running', details: [] };
        
        try {
            // Test PDF export function
            if (typeof window.exportToPDF === 'function') {
                test.details.push('✅ PDF export function available');
            } else {
                test.details.push('❌ PDF export function missing');
                this.errors.push('PDF export function not available');
            }

            // Test image export function
            if (typeof window.exportToImage === 'function') {
                test.details.push('✅ Image export function available');
            } else {
                test.details.push('❌ Image export function missing');
                this.errors.push('Image export function not available');
            }

            // Test QR code generation
            if (typeof window.generateQRCode === 'function') {
                test.details.push('✅ QR code generation available');
            } else {
                test.details.push('❌ QR code generation missing');
                this.errors.push('QR code generation not available');
            }

            test.status = this.errors.length === 0 ? 'passed' : 'failed';
        } catch (error) {
            test.status = 'failed';
            test.details.push(`❌ Error: ${error.message}`);
            this.errors.push(`Export functionality error: ${error.message}`);
        }

        this.testResults.push(test);
    }

    async testCrossBrowserCompatibility() {
        const test = { name: 'Cross-Browser Compatibility', status: 'running', details: [] };
        
        try {
            // Test modern JavaScript features
            const features = [
                { name: 'Promises', test: () => typeof Promise !== 'undefined' },
                { name: 'Async/Await', test: () => typeof (async () => {}) === 'function' },
                { name: 'Arrow Functions', test: () => typeof (() => {}) === 'function' },
                { name: 'Template Literals', test: () => `test` === 'test' },
                { name: 'Destructuring', test: () => { const {a} = {a: 1}; return a === 1; } },
                { name: 'Spread Operator', test: () => [...[1, 2]].length === 2 },
                { name: 'CustomEvent', test: () => typeof CustomEvent !== 'undefined' },
                { name: 'Fetch API', test: () => typeof fetch !== 'undefined' }
            ];

            features.forEach(feature => {
                try {
                    if (feature.test()) {
                        test.details.push(`✅ ${feature.name} supported`);
                    } else {
                        test.details.push(`❌ ${feature.name} not supported`);
                        this.warnings.push(`${feature.name} not supported in ${this.browserInfo.name}`);
                    }
                } catch (error) {
                    test.details.push(`❌ ${feature.name} test failed`);
                    this.warnings.push(`${feature.name} test failed: ${error.message}`);
                }
            });

            test.status = 'passed';
        } catch (error) {
            test.status = 'failed';
            test.details.push(`❌ Error: ${error.message}`);
            this.errors.push(`Cross-browser compatibility error: ${error.message}`);
        }

        this.testResults.push(test);
    }

    async testResponsiveDesign() {
        const test = { name: 'Responsive Design', status: 'running', details: [] };
        
        try {
            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight
            };

            test.details.push(`📱 Viewport: ${viewport.width}x${viewport.height}`);

            // Test CSS media queries
            const mediaQueries = [
                { name: 'Mobile', query: '(max-width: 768px)' },
                { name: 'Tablet', query: '(min-width: 769px) and (max-width: 1024px)' },
                { name: 'Desktop', query: '(min-width: 1025px)' }
            ];

            mediaQueries.forEach(mq => {
                if (window.matchMedia(mq.query).matches) {
                    test.details.push(`✅ ${mq.name} layout active`);
                }
            });

            // Test touch support
            if ('ontouchstart' in window) {
                test.details.push('✅ Touch events supported');
            } else {
                test.details.push('ℹ️ Touch events not supported (desktop)');
            }

            test.status = 'passed';
        } catch (error) {
            test.status = 'failed';
            test.details.push(`❌ Error: ${error.message}`);
            this.errors.push(`Responsive design error: ${error.message}`);
        }

        this.testResults.push(test);
    }

    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            browser: this.browserInfo,
            summary: {
                total: this.testResults.length,
                passed: this.testResults.filter(t => t.status === 'passed').length,
                failed: this.testResults.filter(t => t.status === 'failed').length,
                errors: this.errors.length,
                warnings: this.warnings.length
            },
            tests: this.testResults,
            errors: this.errors,
            warnings: this.warnings
        };

        console.log('📊 تقرير التشخيص الشامل:');
        console.log(`🌐 المتصفح: ${report.browser.name} ${report.browser.version}`);
        console.log(`✅ نجح: ${report.summary.passed}/${report.summary.total}`);
        console.log(`❌ فشل: ${report.summary.failed}/${report.summary.total}`);
        console.log(`⚠️ تحذيرات: ${report.summary.warnings}`);
        
        if (report.summary.errors > 0) {
            console.log('🚨 الأخطاء:');
            report.errors.forEach(error => console.log(`  - ${error}`));
        }

        if (report.summary.warnings > 0) {
            console.log('⚠️ التحذيرات:');
            report.warnings.forEach(warning => console.log(`  - ${warning}`));
        }

        // Store report for external access
        window.diagnosticReport = report;
        
        return report;
    }
}

// Initialize diagnostic system
window.CVDiagnosticSystem = CVDiagnosticSystem;

// Auto-run diagnostics when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        window.diagnosticSystem = new CVDiagnosticSystem();
    }, 2000); // Wait for other systems to initialize
});

console.log('🔍 نظام التشخيص الشامل جاهز');
