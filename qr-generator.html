<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد رموز QR - Elashrafy CV</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Google Fonts for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- QR Code Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
    
    <style>
        .qr-generator-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl);
            min-height: 100vh;
            background: var(--bg-primary);
        }

        .qr-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .qr-header h1 {
            font-size: 2.5rem;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .qr-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
            align-items: start;
        }

        .qr-form-section {
            background: var(--bg-card);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
        }

        .qr-preview-section {
            background: var(--bg-card);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            text-align: center;
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .qr-type-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .qr-type-btn {
            padding: var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .qr-type-btn.active {
            border-color: var(--primary-color);
            background: var(--primary-color-light);
            color: var(--primary-color);
        }

        .qr-type-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .qr-type-btn i {
            font-size: 1.5rem;
            margin-bottom: var(--spacing-sm);
            display: block;
        }

        .qr-preview-container {
            margin-bottom: var(--spacing-lg);
        }

        #qrCodeCanvas {
            max-width: 100%;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
        }

        .qr-customization {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }

        .color-input {
            width: 60px !important;
            height: 40px;
            padding: 0;
            border: none;
            border-radius: var(--radius-sm);
            cursor: pointer;
        }

        .qr-actions {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
            margin-top: var(--spacing-lg);
        }

        .ai-suggestions-qr {
            background: var(--primary-color-light);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
        }

        .ai-suggestions-qr h4 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--primary-color);
            font-size: 1rem;
        }

        .ai-suggestions-qr p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .qr-content {
                grid-template-columns: 1fr;
            }
            
            .qr-customization {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html">
                    <i class="fas fa-file-alt"></i>
                    <span>Elashrafy CV</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="index.html" class="nav-link">الرئيسية</a>
                <a href="cv-builder.html" class="nav-link">منشئ السيرة الذاتية</a>
                <a href="qr-generator.html" class="nav-link active">مولد QR</a>
                <a href="nfc-generator.html" class="nav-link">مولد NFC</a>
            </div>
        </div>
    </nav>

    <div class="qr-generator-container">
        <div class="qr-header">
            <h1><i class="fas fa-qrcode"></i> مولد رموز QR المتقدم</h1>
            <p>أنشئ رموز QR مخصصة لمعلومات الاتصال، الروابط، والنصوص بتقنية متقدمة</p>
        </div>

        <div class="ai-suggestions-qr">
            <h4><i class="fas fa-robot"></i> نصيحة ذكية</h4>
            <p>استخدم رموز QR لمشاركة معلومات الاتصال بسرعة. يمكن للأشخاص مسح الرمز وحفظ معلوماتك مباشرة في هواتفهم.</p>
        </div>

        <div class="qr-content">
            <div class="qr-form-section">
                <h3><i class="fas fa-cog"></i> إعدادات رمز QR</h3>
                
                <div class="qr-type-selector">
                    <div class="qr-type-btn active" data-type="contact" onclick="selectQRType('contact')">
                        <i class="fas fa-address-card"></i>
                        <span>معلومات الاتصال</span>
                    </div>
                    <div class="qr-type-btn" data-type="url" onclick="selectQRType('url')">
                        <i class="fas fa-link"></i>
                        <span>رابط ويب</span>
                    </div>
                    <div class="qr-type-btn" data-type="text" onclick="selectQRType('text')">
                        <i class="fas fa-font"></i>
                        <span>نص</span>
                    </div>
                    <div class="qr-type-btn" data-type="wifi" onclick="selectQRType('wifi')">
                        <i class="fas fa-wifi"></i>
                        <span>WiFi</span>
                    </div>
                </div>

                <!-- Contact Form -->
                <div id="contactForm" class="qr-form">
                    <div class="form-group">
                        <label>الاسم الكامل *</label>
                        <input type="text" id="contactName" placeholder="أدخل اسمك الكامل" oninput="updateQRCode()">
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" id="contactPhone" placeholder="+966 50 123 4567" oninput="updateQRCode()">
                    </div>
                    <div class="form-group">
                        <label>البريد الإلكتروني</label>
                        <input type="email" id="contactEmail" placeholder="<EMAIL>" oninput="updateQRCode()">
                    </div>
                    <div class="form-group">
                        <label>المنظمة/الشركة</label>
                        <input type="text" id="contactOrg" placeholder="اسم الشركة" oninput="updateQRCode()">
                    </div>
                    <div class="form-group">
                        <label>المسمى الوظيفي</label>
                        <input type="text" id="contactTitle" placeholder="المسمى الوظيفي" oninput="updateQRCode()">
                    </div>
                    <div class="form-group">
                        <label>الموقع الإلكتروني</label>
                        <input type="url" id="contactWebsite" placeholder="https://yourwebsite.com" oninput="updateQRCode()">
                    </div>
                </div>

                <!-- URL Form -->
                <div id="urlForm" class="qr-form" style="display: none;">
                    <div class="form-group">
                        <label>رابط الويب *</label>
                        <input type="url" id="urlInput" placeholder="https://example.com" oninput="updateQRCode()">
                    </div>
                </div>

                <!-- Text Form -->
                <div id="textForm" class="qr-form" style="display: none;">
                    <div class="form-group">
                        <label>النص *</label>
                        <textarea id="textInput" rows="4" placeholder="أدخل النص المراد تحويله لرمز QR" oninput="updateQRCode()"></textarea>
                    </div>
                </div>

                <!-- WiFi Form -->
                <div id="wifiForm" class="qr-form" style="display: none;">
                    <div class="form-group">
                        <label>اسم الشبكة (SSID) *</label>
                        <input type="text" id="wifiSSID" placeholder="اسم شبكة WiFi" oninput="updateQRCode()">
                    </div>
                    <div class="form-group">
                        <label>كلمة المرور</label>
                        <input type="password" id="wifiPassword" placeholder="كلمة مرور الشبكة" oninput="updateQRCode()">
                    </div>
                    <div class="form-group">
                        <label>نوع الأمان</label>
                        <select id="wifiSecurity" onchange="updateQRCode()">
                            <option value="WPA">WPA/WPA2</option>
                            <option value="WEP">WEP</option>
                            <option value="nopass">بدون كلمة مرور</option>
                        </select>
                    </div>
                </div>

                <div class="qr-customization">
                    <div class="form-group">
                        <label>لون المقدمة</label>
                        <input type="color" id="foregroundColor" class="color-input" value="#000000" onchange="updateQRCode()">
                    </div>
                    <div class="form-group">
                        <label>لون الخلفية</label>
                        <input type="color" id="backgroundColor" class="color-input" value="#ffffff" onchange="updateQRCode()">
                    </div>
                    <div class="form-group">
                        <label>حجم الرمز</label>
                        <select id="qrSize" onchange="updateQRCode()">
                            <option value="200">صغير (200px)</option>
                            <option value="300" selected>متوسط (300px)</option>
                            <option value="400">كبير (400px)</option>
                            <option value="500">كبير جداً (500px)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>مستوى تصحيح الأخطاء</label>
                        <select id="errorCorrection" onchange="updateQRCode()">
                            <option value="L">منخفض (7%)</option>
                            <option value="M" selected>متوسط (15%)</option>
                            <option value="Q">عالي (25%)</option>
                            <option value="H">عالي جداً (30%)</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="qr-preview-section">
                <h3><i class="fas fa-eye"></i> معاينة رمز QR</h3>
                
                <div class="qr-preview-container">
                    <canvas id="qrCodeCanvas"></canvas>
                </div>

                <div class="qr-actions">
                    <button class="btn btn-primary" onclick="downloadQRCode()">
                        <i class="fas fa-download"></i>
                        تحميل PNG
                    </button>
                    <button class="btn btn-outline" onclick="downloadQRCodeSVG()">
                        <i class="fas fa-vector-square"></i>
                        تحميل SVG
                    </button>
                    <button class="btn btn-secondary" onclick="printQRCode()">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                </div>

                <div class="ai-suggestions-qr" style="margin-top: var(--spacing-lg);">
                    <h4><i class="fas fa-lightbulb"></i> نصائح للاستخدام</h4>
                    <ul style="margin: 0; padding-right: var(--spacing-md);">
                        <li>تأكد من وضوح الرمز عند الطباعة</li>
                        <li>اختبر الرمز على أجهزة مختلفة</li>
                        <li>استخدم ألوان متباينة للوضوح</li>
                        <li>احفظ نسخة احتياطية من البيانات</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/app.js"></script>
    <script>
        let currentQRType = 'contact';

        function selectQRType(type) {
            currentQRType = type;
            
            // Update active button
            document.querySelectorAll('.qr-type-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('active');
            
            // Show/hide forms
            document.querySelectorAll('.qr-form').forEach(form => {
                form.style.display = 'none';
            });
            document.getElementById(`${type}Form`).style.display = 'block';
            
            updateQRCode();
        }

        function updateQRCode() {
            const canvas = document.getElementById('qrCodeCanvas');
            const size = parseInt(document.getElementById('qrSize').value);
            const foregroundColor = document.getElementById('foregroundColor').value;
            const backgroundColor = document.getElementById('backgroundColor').value;
            const errorCorrectionLevel = document.getElementById('errorCorrection').value;
            
            let qrData = '';
            
            switch (currentQRType) {
                case 'contact':
                    qrData = generateVCard();
                    break;
                case 'url':
                    qrData = document.getElementById('urlInput').value;
                    break;
                case 'text':
                    qrData = document.getElementById('textInput').value;
                    break;
                case 'wifi':
                    qrData = generateWiFiString();
                    break;
            }
            
            if (qrData) {
                QRCode.toCanvas(canvas, qrData, {
                    width: size,
                    margin: 3,
                    color: {
                        dark: foregroundColor,
                        light: backgroundColor
                    },
                    errorCorrectionLevel: errorCorrectionLevel
                }, function (error) {
                    if (error) console.error(error);
                });
            }
        }

        function generateVCard() {
            const name = document.getElementById('contactName').value;
            const phone = document.getElementById('contactPhone').value;
            const email = document.getElementById('contactEmail').value;
            const org = document.getElementById('contactOrg').value;
            const title = document.getElementById('contactTitle').value;
            const website = document.getElementById('contactWebsite').value;
            
            if (!name) return '';
            
            let vcard = 'BEGIN:VCARD\nVERSION:3.0\n';
            vcard += `FN:${name}\n`;
            if (phone) vcard += `TEL:${phone}\n`;
            if (email) vcard += `EMAIL:${email}\n`;
            if (org) vcard += `ORG:${org}\n`;
            if (title) vcard += `TITLE:${title}\n`;
            if (website) vcard += `URL:${website}\n`;
            vcard += 'END:VCARD';
            
            return vcard;
        }

        function generateWiFiString() {
            const ssid = document.getElementById('wifiSSID').value;
            const password = document.getElementById('wifiPassword').value;
            const security = document.getElementById('wifiSecurity').value;
            
            if (!ssid) return '';
            
            return `WIFI:T:${security};S:${ssid};P:${password};;`;
        }

        function downloadQRCode() {
            const canvas = document.getElementById('qrCodeCanvas');
            const link = document.createElement('a');
            link.download = 'qr-code.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadQRCodeSVG() {
            // SVG download functionality would require additional library
            alert('ميزة تحميل SVG ستكون متاحة قريباً');
        }

        function printQRCode() {
            const canvas = document.getElementById('qrCodeCanvas');
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head><title>طباعة رمز QR</title></head>
                    <body style="text-align: center; padding: 20px;">
                        <h2>رمز QR</h2>
                        <img src="${canvas.toDataURL()}" style="max-width: 100%;">
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // Initialize with default contact QR
        document.addEventListener('DOMContentLoaded', function() {
            updateQRCode();
        });
    </script>
</body>
</html>
