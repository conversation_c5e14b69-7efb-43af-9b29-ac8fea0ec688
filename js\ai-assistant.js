// ===== AI Assistant for CV Builder =====

class AIAssistant {
    constructor() {
        this.isActive = false;
        this.chatHistory = [];
        this.suggestions = {
            personal: [
                'تأكد من إضافة رقم هاتف صحيح للتواصل',
                'أضف رابط LinkedIn لزيادة فرص التواصل',
                'استخدم بريد إلكتروني احترافي'
            ],
            summary: [
                'اذكر سنوات خبرتك في بداية الملخص',
                'أضف أهم إنجازاتك المهنية',
                'اذكر المجال الذي تتخصص فيه',
                'أضف هدفك المهني في نهاية الملخص'
            ],
            experience: [
                'استخدم أرقام وإحصائيات لوصف إنجازاتك',
                'ابدأ كل نقطة بفعل قوي (طورت، أدرت، حققت)',
                'اذكر التقنيات والأدوات التي استخدمتها',
                'ركز على النتائج والتأثير الذي حققته'
            ],
            skills: [
                'صنف مهاراتك إلى تقنية وشخصية',
                'أضف مستوى إتقانك لكل مهارة',
                'ركز على المهارات المطلوبة في مجالك',
                'أضف مهارات حديثة ومطلوبة في السوق'
            ]
        };
        
        this.templates = {
            summary: {
                developer: 'مطور {technology} بخبرة {years} سنوات في تطوير التطبيقات الحديثة. متخصص في {specialization} مع خبرة في {tools}. أسعى لتطوير حلول تقنية مبتكرة تساهم في نمو الشركة.',
                designer: 'مصمم {type} إبداعي بخبرة {years} سنوات في تصميم {projects}. متخصص في {tools} مع شغف بإنشاء تجارب مستخدم استثنائية.',
                manager: 'مدير {department} بخبرة {years} سنوات في قيادة الفرق وإدارة المشاريع. نجحت في {achievements} وأسعى لتحقيق المزيد من النمو والتطوير.'
            }
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.startPeriodicSuggestions();
    }
    
    setupEventListeners() {
        // Listen for form changes to provide contextual suggestions
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea')) {
                this.analyzeInput(e.target);
            }
        });
        
        // Listen for section changes
        document.addEventListener('sectionChanged', (e) => {
            this.updateSuggestionsForSection(e.detail.section);
        });
    }
    
    analyzeInput(input) {
        const value = input.value.trim();
        const fieldType = input.id || input.dataset.field;
        
        if (value.length > 10) {
            this.provideSuggestion(fieldType, value);
        }
    }
    
    provideSuggestion(fieldType, content) {
        let suggestion = '';
        
        switch (fieldType) {
            case 'fullName':
                if (!this.isArabicName(content)) {
                    suggestion = 'يُفضل كتابة الاسم باللغة العربية أو الإنجليزية بشكل واضح';
                }
                break;
                
            case 'email':
                if (!this.isValidEmail(content)) {
                    suggestion = 'تأكد من صحة عنوان البريد الإلكتروني';
                } else if (!this.isProfessionalEmail(content)) {
                    suggestion = 'يُفضل استخدام بريد إلكتروني احترافي (تجنب أسماء غير مناسبة)';
                }
                break;
                
            case 'jobTitle':
                if (content.length < 5) {
                    suggestion = 'أضف مسمى وظيفي أكثر تفصيلاً';
                }
                break;
                
            case 'professionalSummary':
                suggestion = this.analyzeSummary(content);
                break;
        }
        
        if (suggestion) {
            this.showSuggestion(suggestion, 'tip');
        }
    }
    
    analyzeSummary(summary) {
        const words = summary.split(' ').length;
        const hasExperience = /\d+\s*(سنة|سنوات|عام|أعوام)/.test(summary);
        const hasSkills = /(خبرة|متخصص|مطور|مصمم|مدير)/.test(summary);
        
        if (words < 20) {
            return 'الملخص قصير جداً، أضف المزيد من التفاصيل عن خبراتك';
        } else if (words > 100) {
            return 'الملخص طويل، حاول اختصاره للنقاط الأساسية';
        } else if (!hasExperience) {
            return 'أضف سنوات خبرتك في الملخص';
        } else if (!hasSkills) {
            return 'اذكر مجال تخصصك أو مهاراتك الرئيسية';
        }
        
        return '';
    }
    
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    isProfessionalEmail(email) {
        const unprofessionalPatterns = [
            /\d{4,}/,  // Too many numbers
            /(cool|hot|sexy|baby|love|heart)/i,
            /(123|456|789|000)/
        ];
        
        return !unprofessionalPatterns.some(pattern => pattern.test(email));
    }
    
    isArabicName(name) {
        const arabicRegex = /[\u0600-\u06FF]/;
        const englishRegex = /^[a-zA-Z\s]+$/;
        return arabicRegex.test(name) || englishRegex.test(name);
    }
    
    generateSummaryTemplate(jobTitle, experience) {
        const templates = this.templates.summary;
        let template = '';
        
        if (jobTitle.includes('مطور') || jobTitle.includes('developer')) {
            template = templates.developer;
        } else if (jobTitle.includes('مصمم') || jobTitle.includes('designer')) {
            template = templates.designer;
        } else if (jobTitle.includes('مدير') || jobTitle.includes('manager')) {
            template = templates.manager;
        } else {
            template = 'محترف في مجال {field} بخبرة {years} سنوات. متخصص في {specialization} مع شغف بتحقيق النتائج المتميزة.';
        }
        
        return template;
    }
    
    suggestSkills(jobTitle) {
        const skillSuggestions = {
            'مطور': ['JavaScript', 'React', 'Node.js', 'Python', 'Git', 'SQL', 'HTML/CSS', 'TypeScript'],
            'مطور ويب': ['React', 'Vue.js', 'Angular', 'JavaScript', 'CSS3', 'HTML5', 'Bootstrap', 'Sass'],
            'مطور تطبيقات': ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Java', 'Dart'],
            'مطور خلفي': ['Node.js', 'Python', 'Java', 'C#', 'PHP', 'MySQL', 'MongoDB', 'Redis'],
            'مصمم': ['Photoshop', 'Illustrator', 'Figma', 'UI/UX', 'Typography', 'Color Theory', 'Sketch', 'InDesign'],
            'مصمم جرافيك': ['Photoshop', 'Illustrator', 'InDesign', 'After Effects', 'Branding', 'Print Design'],
            'مصمم UI/UX': ['Figma', 'Sketch', 'Adobe XD', 'Prototyping', 'User Research', 'Wireframing'],
            'مدير': ['القيادة', 'إدارة المشاريع', 'التخطيط الاستراتيجي', 'التواصل', 'حل المشكلات', 'اتخاذ القرارات'],
            'مدير مشاريع': ['PMP', 'Agile', 'Scrum', 'Jira', 'Microsoft Project', 'إدارة المخاطر'],
            'محاسب': ['Excel', 'QuickBooks', 'SAP', 'التحليل المالي', 'إعداد التقارير', 'الضرائب'],
            'مسوق': ['Google Analytics', 'Social Media', 'SEO', 'Content Marketing', 'Email Marketing', 'PPC'],
            'مسوق رقمي': ['Google Ads', 'Facebook Ads', 'SEO', 'SEM', 'Analytics', 'Social Media Marketing'],
            'محلل بيانات': ['Python', 'R', 'SQL', 'Excel', 'Tableau', 'Power BI', 'Statistics'],
            'مهندس': ['AutoCAD', 'SolidWorks', 'MATLAB', 'Project Management', 'Problem Solving'],
            'طبيب': ['التشخيص', 'العلاج', 'التواصل مع المرضى', 'العمل تحت الضغط', 'البحث الطبي'],
            'معلم': ['التدريس', 'إدارة الصف', 'التقييم', 'التخطيط التعليمي', 'التواصل'],
            'محامي': ['البحث القانوني', 'كتابة المذكرات', 'المرافعة', 'التفاوض', 'التحليل القانوني']
        };

        // Check for exact matches first
        for (const [key, skills] of Object.entries(skillSuggestions)) {
            if (jobTitle.toLowerCase().includes(key.toLowerCase())) {
                return skills;
            }
        }

        // Default skills for any profession
        return ['التواصل', 'العمل الجماعي', 'حل المشكلات', 'إدارة الوقت', 'التعلم المستمر'];
    }
    
    showSuggestion(message, type = 'info') {
        const suggestionContainer = document.querySelector('.ai-suggestions');
        if (!suggestionContainer) return;
        
        const suggestionElement = document.createElement('div');
        suggestionElement.className = `suggestion-item ${type}`;
        suggestionElement.innerHTML = `
            <i class="fas fa-${this.getSuggestionIcon(type)}"></i>
            <span>${message}</span>
            <button class="suggestion-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        suggestionContainer.appendChild(suggestionElement);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (suggestionElement.parentElement) {
                suggestionElement.remove();
            }
        }, 10000);
    }
    
    getSuggestionIcon(type) {
        const icons = {
            tip: 'lightbulb',
            warning: 'exclamation-triangle',
            error: 'exclamation-circle',
            success: 'check-circle',
            info: 'info-circle'
        };
        return icons[type] || 'lightbulb';
    }
    
    updateSuggestionsForSection(section) {
        const suggestions = this.suggestions[section] || [];
        const suggestionContainer = document.querySelector('.ai-suggestions');
        
        if (suggestionContainer && suggestions.length > 0) {
            // Clear existing suggestions
            suggestionContainer.innerHTML = '';
            
            // Add random suggestion for the section
            const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
            this.showSuggestion(randomSuggestion, 'tip');
        }
    }
    
    startPeriodicSuggestions() {
        setInterval(() => {
            if (window.cvBuilder && window.cvBuilder.currentSection) {
                this.updateSuggestionsForSection(window.cvBuilder.currentSection);
            }
        }, 60000); // Show new suggestion every minute
    }
    
    openAIChat() {
        this.createChatModal();
    }
    
    createChatModal() {
        const modal = document.createElement('div');
        modal.className = 'ai-chat-modal';
        modal.innerHTML = `
            <div class="modal-content ai-chat-content">
                <div class="modal-header">
                    <h3><i class="fas fa-robot"></i> المساعد الذكي</h3>
                    <button class="modal-close" onclick="this.closest('.ai-chat-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="chat-container">
                    <div class="chat-messages" id="chatMessages">
                        <div class="ai-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                مرحباً! أنا هنا لمساعدتك في إنشاء سيرة ذاتية مميزة. كيف يمكنني مساعدتك؟
                            </div>
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <input type="text" id="chatInput" placeholder="اكتب سؤالك هنا..." onkeypress="handleChatKeyPress(event)">
                        <button class="btn btn-primary" onclick="sendChatMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
                <div class="quick-actions">
                    <h4>أسئلة شائعة:</h4>
                    <button class="quick-action-btn" onclick="askQuickQuestion('كيف أكتب ملخص مهني جيد؟')">
                        كيف أكتب ملخص مهني جيد؟
                    </button>
                    <button class="quick-action-btn" onclick="askQuickQuestion('ما هي المهارات المطلوبة في مجالي؟')">
                        ما هي المهارات المطلوبة في مجالي؟
                    </button>
                    <button class="quick-action-btn" onclick="askQuickQuestion('كيف أصف خبراتي العملية؟')">
                        كيف أصف خبراتي العملية؟
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        document.getElementById('chatInput').focus();
    }
    
    sendMessage(message) {
        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) return;
        
        // Add user message
        this.addMessageToChat(message, 'user');
        
        // Simulate AI response
        setTimeout(() => {
            const response = this.generateAIResponse(message);
            this.addMessageToChat(response, 'ai');
        }, 1000);
    }
    
    addMessageToChat(message, sender) {
        const chatMessages = document.getElementById('chatMessages');
        const messageElement = document.createElement('div');
        messageElement.className = `${sender}-message`;
        
        if (sender === 'ai') {
            messageElement.innerHTML = `
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">${message}</div>
            `;
        } else {
            messageElement.innerHTML = `
                <div class="message-content">${message}</div>
                <div class="message-avatar">
                    <i class="fas fa-user"></i>
                </div>
            `;
        }
        
        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    generateAIResponse(message) {
        const lowerMessage = message.toLowerCase();

        const responses = {
            'ملخص': 'لكتابة ملخص مهني جيد: ابدأ بذكر سنوات خبرتك، ثم اذكر مجال تخصصك، أضف أهم إنجازاتك، واختتم بهدفك المهني. يجب أن يكون الملخص بين 3-5 أسطر.',
            'مهارات': 'المهارات المطلوبة تعتمد على مجالك. بشكل عام، ركز على المهارات التقنية المطلوبة في وظيفتك والمهارات الشخصية مثل التواصل والعمل الجماعي.',
            'خبرة': 'عند وصف خبراتك العملية: استخدم أرقام وإحصائيات، ابدأ بأفعال قوية، ركز على النتائج والإنجازات، واذكر التقنيات المستخدمة.',
            'تعليم': 'في قسم التعليم: اذكر الدرجة العلمية، اسم الجامعة، سنة التخرج، والمعدل إذا كان جيداً. يمكنك إضافة المشاريع المميزة أو الأنشطة الطلابية.',
            'لغات': 'عند إضافة اللغات: كن صادقاً في تقييم مستواك، استخدم مقاييس واضحة (مبتدئ، متوسط، متقدم، ممتاز، لغة أم).',
            'قوالب': 'اختر القالب المناسب لمجالك: العصري للتقنية، الكلاسيكي للمجالات الرسمية، الإبداعي للفنون والتصميم.',
            'ألوان': 'استخدم ألوان متناسقة ومهنية. تجنب الألوان الزاهية جداً في المجالات الرسمية.',
            'طباعة': 'تأكد من أن السيرة الذاتية تبدو جيدة عند الطباعة. استخدم خطوط واضحة وحجم مناسب.',
            'pdf': 'عند التصدير لـ PDF: تأكد من جودة الصورة، واختبر الملف على أجهزة مختلفة قبل الإرسال.',
            'default': 'شكراً لسؤالك! يمكنني مساعدتك في تحسين سيرتك الذاتية. هل لديك سؤال محدد حول قسم معين؟'
        };

        // Check for keywords in the message
        for (const [key, response] of Object.entries(responses)) {
            if (lowerMessage.includes(key)) {
                return response;
            }
        }

        // Check for specific job titles
        if (lowerMessage.includes('مطور') || lowerMessage.includes('برمجة')) {
            return 'للمطورين: ركز على اللغات البرمجية، الأطر التقنية، المشاريع المكتملة، والمساهمات في المصادر المفتوحة. اذكر GitHub profile إذا كان متاحاً.';
        }

        if (lowerMessage.includes('مصمم') || lowerMessage.includes('تصميم')) {
            return 'للمصممين: أضف رابط portfolio، اذكر البرامج التي تتقنها، وركز على المشاريع المرئية. استخدم قالب إبداعي يعكس أسلوبك.';
        }

        if (lowerMessage.includes('مدير') || lowerMessage.includes('إدارة')) {
            return 'للمدراء: ركز على الإنجازات القيادية، حجم الفرق التي أدرتها، والنتائج المحققة. استخدم أرقام ونسب مئوية لإظهار التأثير.';
        }

        return responses.default;
    }
}

// Global functions for AI chat
function sendChatMessage() {
    const input = document.getElementById('chatInput');
    const message = input.value.trim();
    
    if (message) {
        window.aiAssistant.sendMessage(message);
        input.value = '';
    }
}

function handleChatKeyPress(event) {
    if (event.key === 'Enter') {
        sendChatMessage();
    }
}

function askQuickQuestion(question) {
    const input = document.getElementById('chatInput');
    input.value = question;
    sendChatMessage();
}

function applySuggestion(suggestionId) {
    // Apply specific suggestions based on ID
    console.log('Applying suggestion:', suggestionId);
}

function addSuggestedSkill(skill, type) {
    // Add suggested skill to the appropriate list
    console.log('Adding skill:', skill, 'Type:', type);
}

function openAIChat() {
    if (window.aiAssistant) {
        window.aiAssistant.openAIChat();
    }
}

// Initialize AI Assistant when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.aiAssistant = new AIAssistant();
});
