<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار منشئ السيرة الذاتية - إلاشرافي</title>

    <!-- خطوط عربية احترافية -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <!-- أيقونات Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- ملفات CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/cv-builder-new.css">
    <link rel="stylesheet" href="css/cv-templates.css">
    <link rel="stylesheet" href="css/cv-templates-advanced.css">

    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Cairo', sans-serif;
        }

        .test-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            border-radius: 12px;
        }

        .test-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .test-card {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .test-card:hover {
            border-color: #1e40af;
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }

        .test-button {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 15px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(30, 64, 175, 0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }

        .test-results {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }

        .demo-cv {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> اختبار منشئ السيرة الذاتية المتقدم</h1>
            <p>اختبار شامل لجميع مكونات ووظائف النظام</p>
        </div>

        <!-- اختبار تحميل المكونات -->
        <div class="test-section">
            <h2><i class="fas fa-cogs"></i> اختبار تحميل المكونات</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h3>محرك القوالب <span class="status-indicator" id="templatesStatus"></span></h3>
                    <p>اختبار تحميل وعمل محرك القوالب</p>
                    <button class="test-button" onclick="testTemplatesEngine()">اختبار المحرك</button>
                </div>

                <div class="test-card">
                    <h3>نظام التحقق <span class="status-indicator" id="validationStatus"></span></h3>
                    <p>اختبار نظام التحقق من صحة البيانات</p>
                    <button class="test-button" onclick="testValidationSystem()">اختبار التحقق</button>
                </div>

                <div class="test-card">
                    <h3>نظام التصدير <span class="status-indicator" id="exportStatus"></span></h3>
                    <p>اختبار نظام تصدير PDF والصور</p>
                    <button class="test-button" onclick="testExportSystem()">اختبار التصدير</button>
                </div>

                <div class="test-card">
                    <h3>منشئ السيرة الذاتية <span class="status-indicator" id="builderStatus"></span></h3>
                    <p>اختبار المنشئ الرئيسي</p>
                    <button class="test-button" onclick="testCVBuilder()">اختبار المنشئ</button>
                </div>

                <div class="test-card">
                    <h3>الذكاء الاصطناعي <span class="status-indicator" id="aiStatus"></span></h3>
                    <p>اختبار نظام الذكاء الاصطناعي</p>
                    <button class="test-button" onclick="testAISystem()">اختبار الذكاء الاصطناعي</button>
                </div>
            </div>
        </div>

        <!-- اختبار القوالب -->
        <div class="test-section">
            <h2><i class="fas fa-file-alt"></i> اختبار القوالب</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h3>القالب العصري</h3>
                    <p>اختبار عرض القالب العصري</p>
                    <button class="test-button" onclick="testTemplate('modern')">عرض القالب</button>
                </div>

                <div class="test-card">
                    <h3>القالب الكلاسيكي</h3>
                    <p>اختبار عرض القالب الكلاسيكي</p>
                    <button class="test-button" onclick="testTemplate('classic')">عرض القالب</button>
                </div>

                <div class="test-card">
                    <h3>القالب الإبداعي</h3>
                    <p>اختبار عرض القالب الإبداعي</p>
                    <button class="test-button" onclick="testTemplate('creative')">عرض القالب</button>
                </div>
            </div>

            <div class="demo-cv" id="templateDemo">
                <!-- سيتم عرض القالب هنا -->
            </div>
        </div>

        <!-- اختبار البيانات -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> اختبار البيانات</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h3>بيانات تجريبية</h3>
                    <p>تحميل بيانات تجريبية للاختبار</p>
                    <button class="test-button" onclick="loadSampleData()">تحميل البيانات</button>
                </div>

                <div class="test-card">
                    <h3>حفظ واستعادة</h3>
                    <p>اختبار حفظ واستعادة البيانات</p>
                    <button class="test-button" onclick="testSaveRestore()">اختبار الحفظ</button>
                </div>

                <div class="test-card">
                    <h3>التحقق من البيانات</h3>
                    <p>اختبار التحقق من صحة البيانات</p>
                    <button class="test-button" onclick="testDataValidation()">اختبار التحقق</button>
                </div>
            </div>
        </div>

        <!-- نتائج الاختبار -->
        <div class="test-section">
            <h2><i class="fas fa-chart-line"></i> نتائج الاختبار</h2>
            <div class="test-results" id="testResults">
                <div>جاهز لبدء الاختبارات...</div>
            </div>
        </div>
    </div>

    <!-- مكتبات خارجية -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <!-- ملفات JavaScript -->
    <script src="js/cv-ai-config.js"></script>
    <script src="js/cv-builder-core.js"></script>
    <script src="js/cv-templates-engine.js"></script>
    <script src="js/cv-validation.js"></script>
    <script src="js/cv-export.js"></script>
    <script src="js/cv-ai-assistant.js"></script>

    <script>
        // متغيرات الاختبار
        let testResults = [];
        let sampleData = {
            personal: {
                fullName: 'أحمد محمد علي السعيد',
                jobTitle: 'مطور تطبيقات الويب',
                email: '<EMAIL>',
                phone: '+966 50 123 4567',
                city: 'الرياض، المملكة العربية السعودية',
                website: 'https://ahmed-portfolio.com',
                linkedin: 'https://linkedin.com/in/ahmed-mohammed',
                github: 'https://github.com/ahmed-mohammed',
                photo: null
            },
            professionalSummary: 'مطور تطبيقات ويب محترف مع أكثر من 5 سنوات من الخبرة في تطوير التطبيقات الحديثة باستخدام JavaScript وReact وNode.js. متخصص في إنشاء واجهات مستخدم تفاعلية وحلول تقنية مبتكرة. شغوف بالتعلم المستمر ومواكبة أحدث التقنيات في مجال تطوير الويب.',
            experiences: [
                {
                    id: 1,
                    jobTitle: 'مطور ويب أول',
                    company: 'شركة التقنية المتقدمة',
                    startDate: '2020-01',
                    endDate: '',
                    current: true,
                    description: 'تطوير وصيانة تطبيقات الويب باستخدام React وNode.js. قيادة فريق من 3 مطورين وتحسين أداء التطبيقات بنسبة 40%.'
                },
                {
                    id: 2,
                    jobTitle: 'مطور ويب',
                    company: 'شركة الحلول الرقمية',
                    startDate: '2018-06',
                    endDate: '2019-12',
                    current: false,
                    description: 'تطوير مواقع ويب تفاعلية وتطبيقات الهاتف المحمول. العمل مع فرق متعددة التخصصات لتسليم مشاريع عالية الجودة.'
                }
            ],
            education: [
                {
                    id: 1,
                    degree: 'بكالوريوس علوم الحاسب',
                    institution: 'جامعة الملك سعود',
                    year: '2018',
                    gpa: '3.8 من 4.0',
                    description: 'تخصص في هندسة البرمجيات مع التركيز على تطوير الويب والذكاء الاصطناعي.'
                }
            ],
            technicalSkills: ['JavaScript', 'React', 'Node.js', 'Python', 'MySQL', 'MongoDB', 'Git', 'Docker'],
            softSkills: ['العمل الجماعي', 'القيادة', 'حل المشكلات', 'التواصل الفعال', 'إدارة الوقت'],
            languages: [
                { id: 1, name: 'العربية', level: 'اللغة الأم' },
                { id: 2, name: 'الإنجليزية', level: 'ممتاز' },
                { id: 3, name: 'الفرنسية', level: 'متوسط' }
            ],
            customization: {
                template: 'modern',
                colorScheme: 'blue',
                includePhoto: true,
                includeQR: false,
                twoColumns: false,
                showProgress: true
            }
        };

        // وظائف الاختبار
        function logTest(message, status = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const statusIcon = status === 'success' ? '✅' : status === 'error' ? '❌' : status === 'warning' ? '⚠️' : 'ℹ️';

            resultsDiv.innerHTML += `<div>[${timestamp}] ${statusIcon} ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;

            testResults.push({ timestamp, message, status });
        }

        function updateStatus(elementId, status) {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = `status-indicator status-${status}`;
            }
        }

        // اختبار محرك القوالب
        function testTemplatesEngine() {
            logTest('بدء اختبار محرك القوالب...');

            try {
                const engine = new CVTemplatesEngine();
                logTest('تم إنشاء محرك القوالب بنجاح', 'success');

                // اختبار القوالب المختلفة
                const templates = ['modern', 'classic', 'creative'];
                templates.forEach(template => {
                    if (engine.templates[template]) {
                        logTest(`القالب ${template} متوفر`, 'success');
                    } else {
                        logTest(`القالب ${template} غير متوفر`, 'error');
                    }
                });

                updateStatus('templatesStatus', 'success');
                logTest('اكتمل اختبار محرك القوالب بنجاح', 'success');

            } catch (error) {
                logTest(`خطأ في محرك القوالب: ${error.message}`, 'error');
                updateStatus('templatesStatus', 'error');
            }
        }

        // اختبار نظام التحقق
        function testValidationSystem() {
            logTest('بدء اختبار نظام التحقق...');

            try {
                const validation = new CVValidation();
                logTest('تم إنشاء نظام التحقق بنجاح', 'success');

                // اختبار التحقق من البيانات
                const testData = validation.validateAllData(sampleData);
                if (testData.isValid) {
                    logTest('البيانات التجريبية صحيحة', 'success');
                } else {
                    logTest(`أخطاء في البيانات: ${testData.errors.join(', ')}`, 'warning');
                }

                updateStatus('validationStatus', 'success');
                logTest('اكتمل اختبار نظام التحقق بنجاح', 'success');

            } catch (error) {
                logTest(`خطأ في نظام التحقق: ${error.message}`, 'error');
                updateStatus('validationStatus', 'error');
            }
        }

        // اختبار نظام التصدير
        function testExportSystem() {
            logTest('بدء اختبار نظام التصدير...');

            try {
                const exportSystem = new CVExport();
                logTest('تم إنشاء نظام التصدير بنجاح', 'success');

                // اختبار توليد اسم الملف
                const fileName = exportSystem.generateFileName(sampleData, 'pdf');
                logTest(`اسم الملف المولد: ${fileName}`, 'success');

                updateStatus('exportStatus', 'success');
                logTest('اكتمل اختبار نظام التصدير بنجاح', 'success');

            } catch (error) {
                logTest(`خطأ في نظام التصدير: ${error.message}`, 'error');
                updateStatus('exportStatus', 'error');
            }
        }

        // اختبار منشئ السيرة الذاتية
        function testCVBuilder() {
            logTest('بدء اختبار منشئ السيرة الذاتية...');

            try {
                // محاكاة إنشاء منشئ السيرة الذاتية
                const builder = {
                    cvData: sampleData,
                    currentStep: 1,
                    totalSteps: 6,
                    selectedTemplate: 'modern'
                };

                logTest('تم إنشاء منشئ السيرة الذاتية بنجاح', 'success');

                // اختبار حساب الإحصائيات
                const stats = {
                    completedFields: 15,
                    totalFields: 25,
                    completionPercentage: 60
                };

                logTest(`الإحصائيات: ${stats.completedFields}/${stats.totalFields} (${stats.completionPercentage}%)`, 'success');

                updateStatus('builderStatus', 'success');
                logTest('اكتمل اختبار منشئ السيرة الذاتية بنجاح', 'success');

            } catch (error) {
                logTest(`خطأ في منشئ السيرة الذاتية: ${error.message}`, 'error');
                updateStatus('builderStatus', 'error');
            }
        }

        // اختبار نظام الذكاء الاصطناعي
        function testAISystem() {
            logTest('بدء اختبار نظام الذكاء الاصطناعي...');

            try {
                // التحقق من وجود النظام
                if (typeof CVAIAssistant === 'undefined') {
                    throw new Error('فئة CVAIAssistant غير متوفرة');
                }

                // إنشاء مثيل من النظام
                const aiAssistant = new CVAIAssistant();
                logTest('تم إنشاء نظام الذكاء الاصطناعي بنجاح', 'success');

                // اختبار قاعدة بيانات المهن
                const professions = Object.keys(aiAssistant.professionsDatabase);
                logTest(`قاعدة بيانات المهن تحتوي على ${professions.length} مهنة`, 'success');

                // اختبار تحليل المسمى الوظيفي
                const testJobTitle = 'مطور ويب';
                const matchedProfession = aiAssistant.findMatchingProfession(testJobTitle);
                if (matchedProfession) {
                    logTest(`تم العثور على مطابقة للمسمى الوظيفي: ${matchedProfession.name}`, 'success');
                } else {
                    logTest('لم يتم العثور على مطابقة للمسمى الوظيفي', 'warning');
                }

                // اختبار تحليل الجودة
                aiAssistant.qualityScore = 85;
                const scoreDescription = aiAssistant.getScoreDescription(85);
                logTest(`وصف النتيجة (85%): ${scoreDescription}`, 'success');

                // اختبار تحسين النص
                const originalText = 'هذا نص تجريبي  مع مسافات  متعددة.';
                const improvedText = aiAssistant.fixCommonIssues(originalText);
                logTest(`تحسين النص: "${improvedText}"`, 'success');

                updateStatus('aiStatus', 'success');
                logTest('اكتمل اختبار نظام الذكاء الاصطناعي بنجاح', 'success');

            } catch (error) {
                logTest(`خطأ في نظام الذكاء الاصطناعي: ${error.message}`, 'error');
                updateStatus('aiStatus', 'error');
            }
        }

        // اختبار القوالب
        function testTemplate(templateName) {
            logTest(`بدء اختبار القالب ${templateName}...`);

            try {
                const engine = new CVTemplatesEngine();
                const templateDemo = document.getElementById('templateDemo');

                // عرض القالب
                const html = engine.templates[templateName].call(engine, sampleData, false);
                templateDemo.innerHTML = `
                    <div style="transform: scale(0.5); transform-origin: top right; width: 200%; height: 600px; overflow: hidden;">
                        ${html}
                    </div>
                `;

                logTest(`تم عرض القالب ${templateName} بنجاح`, 'success');

            } catch (error) {
                logTest(`خطأ في عرض القالب ${templateName}: ${error.message}`, 'error');
            }
        }

        // تحميل البيانات التجريبية
        function loadSampleData() {
            logTest('تحميل البيانات التجريبية...');

            try {
                // محاكاة تحميل البيانات
                localStorage.setItem('elashrafy_cv_test_data', JSON.stringify(sampleData));
                logTest('تم حفظ البيانات التجريبية في التخزين المحلي', 'success');

                // محاكاة استعادة البيانات
                const restored = JSON.parse(localStorage.getItem('elashrafy_cv_test_data'));
                if (restored && restored.personal.fullName === sampleData.personal.fullName) {
                    logTest('تم استعادة البيانات التجريبية بنجاح', 'success');
                } else {
                    logTest('فشل في استعادة البيانات التجريبية', 'error');
                }

            } catch (error) {
                logTest(`خطأ في تحميل البيانات: ${error.message}`, 'error');
            }
        }

        // اختبار الحفظ والاستعادة
        function testSaveRestore() {
            logTest('اختبار حفظ واستعادة البيانات...');

            try {
                // حفظ البيانات
                const testKey = 'elashrafy_cv_save_test';
                localStorage.setItem(testKey, JSON.stringify(sampleData));
                logTest('تم حفظ البيانات بنجاح', 'success');

                // استعادة البيانات
                const restored = JSON.parse(localStorage.getItem(testKey));
                if (JSON.stringify(restored) === JSON.stringify(sampleData)) {
                    logTest('تم استعادة البيانات بنجاح', 'success');
                } else {
                    logTest('البيانات المستعادة لا تطابق البيانات الأصلية', 'error');
                }

                // تنظيف
                localStorage.removeItem(testKey);
                logTest('تم تنظيف بيانات الاختبار', 'success');

            } catch (error) {
                logTest(`خطأ في اختبار الحفظ والاستعادة: ${error.message}`, 'error');
            }
        }

        // اختبار التحقق من البيانات
        function testDataValidation() {
            logTest('اختبار التحقق من صحة البيانات...');

            try {
                const validation = new CVValidation();

                // اختبار بيانات صحيحة
                const validResult = validation.validateAllData(sampleData);
                if (validResult.isValid) {
                    logTest('البيانات الصحيحة تم التحقق منها بنجاح', 'success');
                } else {
                    logTest(`أخطاء في البيانات الصحيحة: ${validResult.errors.join(', ')}`, 'warning');
                }

                // اختبار بيانات خاطئة
                const invalidData = { ...sampleData };
                invalidData.personal.email = 'invalid-email';
                invalidData.personal.fullName = '';

                const invalidResult = validation.validateAllData(invalidData);
                if (!invalidResult.isValid) {
                    logTest('تم اكتشاف الأخطاء في البيانات الخاطئة بنجاح', 'success');
                    logTest(`الأخطاء المكتشفة: ${invalidResult.errors.length}`, 'success');
                } else {
                    logTest('فشل في اكتشاف الأخطاء في البيانات الخاطئة', 'error');
                }

            } catch (error) {
                logTest(`خطأ في اختبار التحقق من البيانات: ${error.message}`, 'error');
            }
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            logTest('بدء تشغيل جميع الاختبارات...', 'info');

            setTimeout(() => testTemplatesEngine(), 500);
            setTimeout(() => testValidationSystem(), 1000);
            setTimeout(() => testExportSystem(), 1500);
            setTimeout(() => testCVBuilder(), 2000);
            setTimeout(() => testAISystem(), 2500);
            setTimeout(() => loadSampleData(), 3000);
            setTimeout(() => testSaveRestore(), 3500);
            setTimeout(() => testDataValidation(), 4000);

            setTimeout(() => {
                logTest('اكتملت جميع الاختبارات!', 'success');
                const successCount = testResults.filter(r => r.status === 'success').length;
                const errorCount = testResults.filter(r => r.status === 'error').length;
                const warningCount = testResults.filter(r => r.status === 'warning').length;

                logTest(`النتائج النهائية: ${successCount} نجح، ${errorCount} فشل، ${warningCount} تحذير`, 'info');
            }, 4500);
        }

        // تشغيل الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            logTest('تم تحميل صفحة الاختبار بنجاح', 'success');
            logTest('انقر على الأزرار لتشغيل الاختبارات المختلفة', 'info');

            // تشغيل اختبار سريع
            setTimeout(() => {
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
