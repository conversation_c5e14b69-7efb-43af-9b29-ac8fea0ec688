// ===== Navigation and Scroll Effects =====

class NavigationManager {
    constructor() {
        this.navbar = null;
        this.navToggle = null;
        this.navMenu = null;
        this.navLinks = [];
        this.sections = [];
        this.isScrolling = false;
        
        this.init();
    }
    
    init() {
        this.setupElements();
        this.setupEventListeners();
        this.setupScrollSpy();
    }
    
    setupElements() {
        this.navbar = document.getElementById('navbar');
        this.navToggle = document.getElementById('nav-toggle');
        this.navMenu = document.getElementById('nav-menu');
        this.navLinks = document.querySelectorAll('.nav-link');
        this.sections = document.querySelectorAll('section[id]');
    }
    
    setupEventListeners() {
        // Mobile menu toggle
        if (this.navToggle) {
            this.navToggle.addEventListener('click', () => this.toggleMobileMenu());
        }
        
        // Navigation links
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => this.handleNavClick(e, link));
        });
        
        // Scroll events
        window.addEventListener('scroll', () => this.handleScroll());
        
        // Resize events
        window.addEventListener('resize', () => this.handleResize());
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => this.handleOutsideClick(e));
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }
    
    setupScrollSpy() {
        // Create intersection observer for scroll spy
        const observerOptions = {
            rootMargin: '-20% 0px -80% 0px',
            threshold: 0
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.updateActiveLink(entry.target.id);
                }
            });
        }, observerOptions);
        
        this.sections.forEach(section => {
            observer.observe(section);
        });
    }
    
    toggleMobileMenu() {
        const isActive = this.navMenu.classList.contains('active');
        
        if (isActive) {
            this.closeMobileMenu();
        } else {
            this.openMobileMenu();
        }
    }
    
    openMobileMenu() {
        this.navMenu.classList.add('active');
        this.navToggle.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // Animate menu items
        const menuItems = this.navMenu.querySelectorAll('.nav-link');
        menuItems.forEach((item, index) => {
            item.style.animationDelay = `${index * 0.1}s`;
            item.classList.add('slide-in');
        });
    }
    
    closeMobileMenu() {
        this.navMenu.classList.remove('active');
        this.navToggle.classList.remove('active');
        document.body.style.overflow = '';
        
        // Remove animation classes
        const menuItems = this.navMenu.querySelectorAll('.nav-link');
        menuItems.forEach(item => {
            item.classList.remove('slide-in');
            item.style.animationDelay = '';
        });
    }
    
    handleNavClick(e, link) {
        e.preventDefault();
        
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
            this.scrollToSection(targetElement);
            this.closeMobileMenu();
        }
    }
    
    scrollToSection(element) {
        const offsetTop = element.offsetTop - this.getNavbarHeight();
        
        // Smooth scroll with easing
        this.smoothScrollTo(offsetTop, 800);
    }
    
    smoothScrollTo(targetPosition, duration) {
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        let startTime = null;
        
        const animation = (currentTime) => {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const run = this.easeInOutQuad(timeElapsed, startPosition, distance, duration);
            
            window.scrollTo(0, run);
            
            if (timeElapsed < duration) {
                requestAnimationFrame(animation);
            }
        };
        
        requestAnimationFrame(animation);
    }
    
    easeInOutQuad(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }
    
    getNavbarHeight() {
        return this.navbar ? this.navbar.offsetHeight : 80;
    }
    
    handleScroll() {
        if (this.isScrolling) return;
        
        this.isScrolling = true;
        requestAnimationFrame(() => {
            this.updateNavbarAppearance();
            this.isScrolling = false;
        });
    }
    
    updateNavbarAppearance() {
        const scrollTop = window.pageYOffset;
        
        if (scrollTop > 50) {
            this.navbar.classList.add('scrolled');
        } else {
            this.navbar.classList.remove('scrolled');
        }
        
        // Hide/show navbar on scroll
        if (scrollTop > this.lastScrollTop && scrollTop > 200) {
            this.navbar.classList.add('nav-hidden');
        } else {
            this.navbar.classList.remove('nav-hidden');
        }
        
        this.lastScrollTop = scrollTop;
    }
    
    updateActiveLink(sectionId) {
        this.navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${sectionId}`) {
                link.classList.add('active');
            }
        });
    }
    
    handleResize() {
        if (window.innerWidth > 768) {
            this.closeMobileMenu();
        }
    }
    
    handleOutsideClick(e) {
        if (this.navMenu.classList.contains('active') && 
            !this.navMenu.contains(e.target) && 
            !this.navToggle.contains(e.target)) {
            this.closeMobileMenu();
        }
    }
    
    handleKeyboard(e) {
        // Close mobile menu with Escape key
        if (e.key === 'Escape' && this.navMenu.classList.contains('active')) {
            this.closeMobileMenu();
        }
        
        // Navigate with arrow keys when menu is open
        if (this.navMenu.classList.contains('active')) {
            const focusedElement = document.activeElement;
            const menuItems = Array.from(this.navLinks);
            const currentIndex = menuItems.indexOf(focusedElement);
            
            if (e.key === 'ArrowDown' && currentIndex < menuItems.length - 1) {
                e.preventDefault();
                menuItems[currentIndex + 1].focus();
            } else if (e.key === 'ArrowUp' && currentIndex > 0) {
                e.preventDefault();
                menuItems[currentIndex - 1].focus();
            }
        }
    }
}

// ===== Scroll Animations =====
class ScrollAnimations {
    constructor() {
        this.animatedElements = [];
        this.init();
    }
    
    init() {
        this.setupIntersectionObserver();
        this.setupParallaxElements();
    }
    
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateElement(entry.target);
                }
            });
        }, observerOptions);
        
        // Observe elements with animation classes
        document.querySelectorAll('[data-aos]').forEach(el => {
            observer.observe(el);
        });
    }
    
    animateElement(element) {
        const animationType = element.dataset.aos;
        const delay = element.dataset.aosDelay || 0;
        
        setTimeout(() => {
            element.classList.add('aos-animate');
            
            switch (animationType) {
                case 'fade-up':
                    element.style.animation = 'fadeInUp 0.6s ease-out forwards';
                    break;
                case 'fade-down':
                    element.style.animation = 'fadeInDown 0.6s ease-out forwards';
                    break;
                case 'fade-left':
                    element.style.animation = 'fadeInLeft 0.6s ease-out forwards';
                    break;
                case 'fade-right':
                    element.style.animation = 'fadeInRight 0.6s ease-out forwards';
                    break;
                case 'zoom-in':
                    element.style.animation = 'zoomIn 0.6s ease-out forwards';
                    break;
                default:
                    element.style.animation = 'fadeInUp 0.6s ease-out forwards';
            }
        }, delay);
    }
    
    setupParallaxElements() {
        const parallaxElements = document.querySelectorAll('[data-parallax]');
        
        if (parallaxElements.length > 0) {
            window.addEventListener('scroll', () => {
                this.updateParallax(parallaxElements);
            });
        }
    }
    
    updateParallax(elements) {
        const scrollTop = window.pageYOffset;
        
        elements.forEach(element => {
            const speed = parseFloat(element.dataset.parallax) || 0.5;
            const yPos = -(scrollTop * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }
}

// ===== Page Transitions =====
class PageTransitions {
    constructor() {
        this.isTransitioning = false;
        this.init();
    }
    
    init() {
        this.setupPageTransitions();
    }
    
    setupPageTransitions() {
        // Intercept internal links for smooth transitions
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href]');
            if (link && this.isInternalLink(link.href)) {
                e.preventDefault();
                this.transitionToPage(link.href);
            }
        });
    }
    
    isInternalLink(href) {
        return href.startsWith(window.location.origin) || 
               href.startsWith('/') || 
               href.startsWith('./') ||
               href.startsWith('../');
    }
    
    async transitionToPage(url) {
        if (this.isTransitioning) return;
        
        this.isTransitioning = true;
        
        // Add transition overlay
        const overlay = this.createTransitionOverlay();
        document.body.appendChild(overlay);
        
        // Animate overlay in
        await this.animateOverlayIn(overlay);
        
        // Navigate to new page
        window.location.href = url;
    }
    
    createTransitionOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'page-transition-overlay';
        overlay.innerHTML = `
            <div class="transition-content">
                <div class="transition-logo">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="transition-text">جاري التحميل...</div>
            </div>
        `;
        return overlay;
    }
    
    animateOverlayIn(overlay) {
        return new Promise(resolve => {
            overlay.style.opacity = '0';
            overlay.style.transform = 'scale(0.8)';
            
            requestAnimationFrame(() => {
                overlay.style.transition = 'all 0.3s ease-out';
                overlay.style.opacity = '1';
                overlay.style.transform = 'scale(1)';
                
                setTimeout(resolve, 300);
            });
        });
    }
}

// Initialize navigation and animations when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new NavigationManager();
    new ScrollAnimations();
    new PageTransitions();
});
