// ===== Templates Page JavaScript =====

class TemplatesManager {
    constructor() {
        this.templates = [];
        this.filteredTemplates = [];
        this.currentFilter = 'all';
        this.currentSort = 'popular';
        this.searchQuery = '';
        this.currentPreviewTemplate = null;
        this.templatesPerPage = 12;
        this.currentPage = 1;
        
        this.init();
    }
    
    init() {
        this.loadTemplates();
        this.setupEventListeners();
        this.renderTemplates();
    }
    
    loadTemplates() {
        // Mock template data - in real app, this would come from API
        this.templates = [
            {
                id: 1,
                name: 'القالب العصري الأزرق',
                description: 'مثالي للمجالات التقنية والإبداعية',
                category: 'modern',
                tags: ['عصري', 'تقني', 'مطورين'],
                downloads: 15234,
                rating: 4.9,
                isPopular: true,
                isNew: false,
                isPremium: false,
                preview: 'template1.jpg'
            },
            {
                id: 2,
                name: 'القالب الكلاسيكي الأنيق',
                description: 'مناسب للمجالات الرسمية والإدارية',
                category: 'classic',
                tags: ['كلاسيكي', 'رسمي', 'إداري'],
                downloads: 12891,
                rating: 4.8,
                isPopular: false,
                isNew: false,
                isPremium: false,
                preview: 'template2.jpg'
            },
            {
                id: 3,
                name: 'القالب الإبداعي الملون',
                description: 'مميز للمصممين والفنانين',
                category: 'creative',
                tags: ['إبداعي', 'فني', 'مصممين'],
                downloads: 8567,
                rating: 4.7,
                isPopular: false,
                isNew: true,
                isPremium: false,
                preview: 'template3.jpg'
            },
            {
                id: 4,
                name: 'القالب البسيط الأنيق',
                description: 'تصميم نظيف ومرتب للجميع',
                category: 'minimal',
                tags: ['بسيط', 'نظيف', 'مرتب'],
                downloads: 9876,
                rating: 4.6,
                isPopular: false,
                isNew: false,
                isPremium: false,
                preview: 'template4.jpg'
            },
            {
                id: 5,
                name: 'القالب المهني المتقدم',
                description: 'للمدراء والمحترفين',
                category: 'professional',
                tags: ['مهني', 'إداري', 'قيادي'],
                downloads: 11234,
                rating: 4.9,
                isPopular: true,
                isNew: false,
                isPremium: true,
                preview: 'template5.jpg'
            },
            {
                id: 6,
                name: 'القالب العصري الأخضر',
                description: 'تصميم حديث بألوان طبيعية',
                category: 'modern',
                tags: ['عصري', 'طبيعي', 'حديث'],
                downloads: 7890,
                rating: 4.5,
                isPopular: false,
                isNew: true,
                isPremium: false,
                preview: 'template6.jpg'
            },
            {
                id: 7,
                name: 'القالب البسيط الأبيض',
                description: 'تصميم نظيف وبسيط للجميع',
                category: 'minimal',
                tags: ['بسيط', 'نظيف', 'أبيض'],
                downloads: 6543,
                rating: 4.4,
                isPopular: false,
                isNew: false,
                isPremium: false,
                preview: 'template7.jpg'
            },
            {
                id: 8,
                name: 'القالب المهني الرمادي',
                description: 'للمدراء والمحاسبين',
                category: 'professional',
                tags: ['مهني', 'رمادي', 'محاسبة'],
                downloads: 5432,
                rating: 4.6,
                isPopular: false,
                isNew: false,
                isPremium: true,
                preview: 'template8.jpg'
            },
            {
                id: 9,
                name: 'القالب الإبداعي البنفسجي',
                description: 'للمصممين والفنانين المبدعين',
                category: 'creative',
                tags: ['إبداعي', 'بنفسجي', 'فني'],
                downloads: 4321,
                rating: 4.8,
                isPopular: false,
                isNew: true,
                isPremium: false,
                preview: 'template9.jpg'
            },
            {
                id: 10,
                name: 'القالب الكلاسيكي الذهبي',
                description: 'أناقة كلاسيكية بلمسة ذهبية',
                category: 'classic',
                tags: ['كلاسيكي', 'ذهبي', 'أنيق'],
                downloads: 3210,
                rating: 4.7,
                isPopular: false,
                isNew: false,
                isPremium: true,
                preview: 'template10.jpg'
            }
        ];
        
        this.filteredTemplates = [...this.templates];
    }
    
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('templateSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value.toLowerCase();
                this.filterAndSort();
            });
        }
        
        // Filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.currentFilter = e.target.dataset.category;
                this.updateFilterButtons();
                this.filterAndSort();
            });
        });
        
        // Sort dropdown
        const sortSelect = document.getElementById('sortTemplates');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.filterAndSort();
            });
        }
        
        // Load more button
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreTemplates();
            });
        }
    }
    
    updateFilterButtons() {
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.category === this.currentFilter) {
                btn.classList.add('active');
            }
        });
    }
    
    filterAndSort() {
        // Filter templates
        this.filteredTemplates = this.templates.filter(template => {
            const matchesCategory = this.currentFilter === 'all' || template.category === this.currentFilter;
            const matchesSearch = this.searchQuery === '' || 
                template.name.toLowerCase().includes(this.searchQuery) ||
                template.description.toLowerCase().includes(this.searchQuery) ||
                template.tags.some(tag => tag.toLowerCase().includes(this.searchQuery));
            
            return matchesCategory && matchesSearch;
        });
        
        // Sort templates
        this.sortTemplates();
        
        // Reset pagination
        this.currentPage = 1;
        
        // Render templates
        this.renderTemplates();
    }
    
    sortTemplates() {
        switch (this.currentSort) {
            case 'popular':
                this.filteredTemplates.sort((a, b) => b.downloads - a.downloads);
                break;
            case 'newest':
                this.filteredTemplates.sort((a, b) => b.isNew - a.isNew);
                break;
            case 'name':
                this.filteredTemplates.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
                break;
            case 'category':
                this.filteredTemplates.sort((a, b) => a.category.localeCompare(b.category));
                break;
            case 'rating':
                this.filteredTemplates.sort((a, b) => b.rating - a.rating);
                break;
        }
    }
    
    renderTemplates() {
        const grid = document.getElementById('templatesGrid');
        if (!grid) return;
        
        const templatesToShow = this.filteredTemplates.slice(0, this.currentPage * this.templatesPerPage);
        
        grid.innerHTML = '';
        
        if (templatesToShow.length === 0) {
            grid.innerHTML = `
                <div class="no-templates">
                    <i class="fas fa-search"></i>
                    <h3>لم يتم العثور على قوالب</h3>
                    <p>جرب تغيير معايير البحث أو الفلترة</p>
                </div>
            `;
            return;
        }
        
        templatesToShow.forEach(template => {
            const templateCard = this.createTemplateCard(template);
            grid.appendChild(templateCard);
        });
        
        // Update load more button
        this.updateLoadMoreButton();
        
        // Add animations
        this.addCardAnimations();
    }
    
    createTemplateCard(template) {
        const card = document.createElement('div');
        card.className = 'template-card';
        card.dataset.category = template.category;
        card.dataset.template = template.id;
        
        const badges = [];
        if (template.isPopular) badges.push('<div class="template-badge popular">الأكثر شعبية</div>');
        if (template.isNew) badges.push('<div class="template-badge new">جديد</div>');
        if (template.isPremium) badges.push('<div class="template-badge premium">مميز</div>');
        
        card.innerHTML = `
            <div class="template-preview">
                <div class="template-image">
                    <div class="template-placeholder ${template.category}-template">
                        <div class="template-header">
                            <div class="template-photo"></div>
                            <div class="template-info">
                                <div class="template-name"></div>
                                <div class="template-title"></div>
                            </div>
                        </div>
                        <div class="template-content">
                            <div class="template-section"></div>
                            <div class="template-section"></div>
                        </div>
                    </div>
                </div>
                <div class="template-overlay">
                    <button class="btn btn-primary" onclick="previewTemplate(${template.id})">
                        <i class="fas fa-eye"></i>
                        معاينة
                    </button>
                    <button class="btn btn-outline" onclick="useTemplate(${template.id})">
                        <i class="fas fa-plus"></i>
                        استخدام
                    </button>
                </div>
                ${badges.join('')}
            </div>
            <div class="template-info">
                <h3>${template.name}</h3>
                <p>${template.description}</p>
                <div class="template-tags">
                    ${template.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
                <div class="template-stats">
                    <span><i class="fas fa-download"></i> ${template.downloads.toLocaleString('ar-EG')}</span>
                    <span><i class="fas fa-star"></i> ${template.rating}</span>
                </div>
            </div>
        `;
        
        return card;
    }
    
    updateLoadMoreButton() {
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (!loadMoreBtn) return;
        
        const totalShown = this.currentPage * this.templatesPerPage;
        const hasMore = totalShown < this.filteredTemplates.length;
        
        if (hasMore) {
            loadMoreBtn.style.display = 'inline-flex';
            const remaining = this.filteredTemplates.length - totalShown;
            loadMoreBtn.innerHTML = `
                <i class="fas fa-plus"></i>
                تحميل ${Math.min(remaining, this.templatesPerPage)} قوالب إضافية
            `;
        } else {
            loadMoreBtn.style.display = 'none';
        }
    }
    
    loadMoreTemplates() {
        this.currentPage++;
        this.renderTemplates();
    }
    
    addCardAnimations() {
        const cards = document.querySelectorAll('.template-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }
    
    getTemplateById(id) {
        return this.templates.find(template => template.id === parseInt(id));
    }
}

// Global functions
function previewTemplate(templateId) {
    const template = window.templatesManager.getTemplateById(templateId);
    if (!template) return;
    
    window.templatesManager.currentPreviewTemplate = template;
    
    const modal = document.getElementById('templatePreviewModal');
    const previewContainer = document.getElementById('templatePreviewLarge');
    
    // Create detailed preview
    previewContainer.innerHTML = `
        <div class="template-preview-detailed ${template.category}-template">
            <div class="preview-header">
                <div class="preview-photo"></div>
                <div class="preview-info">
                    <h2>أحمد محمد علي</h2>
                    <h3>مطور ويب متقدم</h3>
                    <div class="preview-contact">
                        <span><i class="fas fa-envelope"></i> <EMAIL></span>
                        <span><i class="fas fa-phone"></i> +966 50 123 4567</span>
                    </div>
                </div>
            </div>
            <div class="preview-content">
                <div class="preview-section">
                    <h4>الملخص المهني</h4>
                    <p>مطور ويب متخصص بخبرة 5 سنوات في تطوير التطبيقات الحديثة...</p>
                </div>
                <div class="preview-section">
                    <h4>الخبرات العملية</h4>
                    <div class="experience-item">
                        <h5>مطور ويب أول - شركة التقنية المتقدمة</h5>
                        <span>2020 - الآن</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeTemplatePreview() {
    const modal = document.getElementById('templatePreviewModal');
    modal.style.display = 'none';
    document.body.style.overflow = '';
    window.templatesManager.currentPreviewTemplate = null;
}

function useCurrentTemplate() {
    if (window.templatesManager.currentPreviewTemplate) {
        const templateId = window.templatesManager.currentPreviewTemplate.id;
        useTemplate(templateId);
    }
}

function useTemplate(templateId) {
    // Get template data
    const template = window.templatesManager.getTemplateById(templateId);
    if (!template) {
        console.error('Template not found:', templateId);
        return;
    }

    // Store selected template in localStorage
    localStorage.setItem('selected_template', JSON.stringify({
        id: templateId,
        name: template.name,
        category: template.category,
        selectedAt: Date.now()
    }));

    // Show loading message
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'template-loading-overlay';
    loadingOverlay.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>جاري تحضير القالب...</h3>
            <p>سيتم نقلك إلى منشئ السيرة الذاتية</p>
        </div>
    `;
    loadingOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(30, 64, 175, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        text-align: center;
    `;

    document.body.appendChild(loadingOverlay);

    // Redirect after animation
    setTimeout(() => {
        window.location.href = `cv-builder.html?template=${templateId}`;
    }, 1500);
}

function startBuilding() {
    window.location.href = 'cv-builder.html';
}

function showLogin() {
    // Show login modal (reuse from main app)
    if (window.app && window.app.showLogin) {
        window.app.showLogin();
    }
}

// Initialize templates manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.templatesManager = new TemplatesManager();
    
    // Close modal when clicking outside
    document.addEventListener('click', (e) => {
        const modal = document.getElementById('templatePreviewModal');
        if (e.target === modal) {
            closeTemplatePreview();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            closeTemplatePreview();
        }
    });
});
