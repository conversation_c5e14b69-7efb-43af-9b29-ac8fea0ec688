/**
 * نظام تصدير السيرة الذاتية
 * نظام متقدم لتصدير السيرة الذاتية بصيغ مختلفة عالية الجودة
 */

class CVExport {
    constructor() {
        this.isExporting = false;
        this.exportOptions = {
            pdf: {
                format: 'a4',
                orientation: 'portrait',
                unit: 'mm',
                compress: true,
                quality: 0.95
            },
            image: {
                format: 'png',
                quality: 0.95,
                scale: 2
            }
        };
        
        console.log('📄 تم تهيئة نظام التصدير');
    }

    /**
     * تصدير السيرة الذاتية كـ PDF
     */
    async exportToPDF(cvData, templateName = 'modern') {
        if (this.isExporting) {
            console.log('⚠️ عملية تصدير جارية بالفعل');
            return;
        }

        try {
            this.isExporting = true;
            this.showExportProgress('جاري إنشاء ملف PDF...', 0);

            // التحقق من توفر المكتبات المطلوبة
            if (typeof jsPDF === 'undefined' || typeof html2canvas === 'undefined') {
                throw new Error('مكتبات التصدير غير متوفرة');
            }

            // إنشاء عنصر مؤقت للتصدير
            const exportElement = await this.createExportElement(cvData, templateName);
            this.showExportProgress('جاري تحويل المحتوى...', 25);

            // تحويل إلى صورة باستخدام html2canvas
            const canvas = await html2canvas(exportElement, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: exportElement.offsetWidth,
                height: exportElement.offsetHeight,
                onclone: (clonedDoc) => {
                    // تطبيق الأنماط على النسخة المستنسخة
                    this.applyExportStyles(clonedDoc);
                }
            });

            this.showExportProgress('جاري إنشاء ملف PDF...', 50);

            // إنشاء PDF
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4',
                compress: true
            });

            // حساب أبعاد الصفحة
            const pageWidth = pdf.internal.pageSize.getWidth();
            const pageHeight = pdf.internal.pageSize.getHeight();
            const margin = 10;
            const contentWidth = pageWidth - (margin * 2);

            // حساب نسبة العرض إلى الارتفاع
            const imgWidth = contentWidth;
            const imgHeight = (canvas.height * imgWidth) / canvas.width;

            // إضافة الصورة إلى PDF
            const imgData = canvas.toDataURL('image/jpeg', 0.95);
            
            if (imgHeight <= pageHeight - (margin * 2)) {
                // المحتوى يناسب صفحة واحدة
                pdf.addImage(imgData, 'JPEG', margin, margin, imgWidth, imgHeight);
            } else {
                // المحتوى يحتاج لعدة صفحات
                await this.addMultiPageContent(pdf, imgData, imgWidth, imgHeight, pageWidth, pageHeight, margin);
            }

            this.showExportProgress('جاري حفظ الملف...', 75);

            // إضافة معلومات إضافية للـ PDF
            this.addPDFMetadata(pdf, cvData);

            // حفظ الملف
            const fileName = this.generateFileName(cvData, 'pdf');
            pdf.save(fileName);

            this.showExportProgress('تم إنشاء الملف بنجاح!', 100);

            // تنظيف العنصر المؤقت
            document.body.removeChild(exportElement);

            // إخفاء شريط التقدم بعد ثانيتين
            setTimeout(() => {
                this.hideExportProgress();
            }, 2000);

            console.log('✅ تم تصدير PDF بنجاح');
            return true;

        } catch (error) {
            console.error('❌ خطأ في تصدير PDF:', error);
            this.showExportError('حدث خطأ في تصدير ملف PDF: ' + error.message);
            throw error;
        } finally {
            this.isExporting = false;
        }
    }

    /**
     * تصدير السيرة الذاتية كصورة
     */
    async exportToImage(cvData, templateName = 'modern', format = 'png') {
        if (this.isExporting) {
            console.log('⚠️ عملية تصدير جارية بالفعل');
            return;
        }

        try {
            this.isExporting = true;
            this.showExportProgress('جاري إنشاء الصورة...', 0);

            // إنشاء عنصر مؤقت للتصدير
            const exportElement = await this.createExportElement(cvData, templateName);
            this.showExportProgress('جاري تحويل المحتوى...', 50);

            // تحويل إلى صورة
            const canvas = await html2canvas(exportElement, {
                scale: this.exportOptions.image.scale,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff'
            });

            this.showExportProgress('جاري حفظ الصورة...', 75);

            // تحويل إلى blob وتحميل
            canvas.toBlob((blob) => {
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = this.generateFileName(cvData, format);
                link.click();
                URL.revokeObjectURL(url);

                this.showExportProgress('تم إنشاء الصورة بنجاح!', 100);
                
                setTimeout(() => {
                    this.hideExportProgress();
                }, 2000);
            }, `image/${format}`, this.exportOptions.image.quality);

            // تنظيف العنصر المؤقت
            document.body.removeChild(exportElement);

            console.log('✅ تم تصدير الصورة بنجاح');
            return true;

        } catch (error) {
            console.error('❌ خطأ في تصدير الصورة:', error);
            this.showExportError('حدث خطأ في تصدير الصورة: ' + error.message);
            throw error;
        } finally {
            this.isExporting = false;
        }
    }

    /**
     * إنشاء عنصر مؤقت للتصدير
     */
    async createExportElement(cvData, templateName) {
        // إنشاء عنصر مؤقت
        const exportElement = document.createElement('div');
        exportElement.className = 'export-container';
        exportElement.style.cssText = `
            position: absolute;
            top: -10000px;
            left: -10000px;
            width: 210mm;
            min-height: 297mm;
            background: white;
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        `;

        // إضافة محرك القوالب إذا لم يكن متوفراً
        if (!window.cvBuilder?.templatesEngine) {
            window.cvBuilder = { templatesEngine: new CVTemplatesEngine() };
        }

        // عرض المحتوى
        exportElement.innerHTML = window.cvBuilder.templatesEngine.templates[templateName].call(
            window.cvBuilder.templatesEngine, 
            cvData, 
            false
        );

        // إضافة الأنماط المطلوبة للتصدير
        await this.addExportStyles(exportElement);

        // إضافة العنصر إلى الصفحة مؤقتاً
        document.body.appendChild(exportElement);

        // انتظار تحميل الخطوط والصور
        await this.waitForResourcesLoad(exportElement);

        return exportElement;
    }

    /**
     * إضافة الأنماط المطلوبة للتصدير
     */
    async addExportStyles(element) {
        const style = document.createElement('style');
        style.textContent = `
            .export-container * {
                box-sizing: border-box;
            }
            
            .cv-document {
                width: 100%;
                min-height: 297mm;
                background: white;
                padding: 20mm;
                font-size: 12pt;
                line-height: 1.4;
                color: #333;
            }
            
            .modern-template .modern-header {
                background: linear-gradient(135deg, var(--primary-color, #1e40af) 0%, var(--primary-light, #3b82f6) 100%);
                color: white;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 20px;
                display: flex;
                align-items: center;
                gap: 20px;
            }
            
            .cv-photo img {
                width: 100px;
                height: 100px;
                border-radius: 50%;
                object-fit: cover;
                border: 3px solid rgba(255, 255, 255, 0.3);
            }
            
            .cv-name {
                font-size: 24pt;
                font-weight: bold;
                margin: 0 0 8px 0;
            }
            
            .cv-title {
                font-size: 16pt;
                font-weight: 500;
                margin: 0 0 15px 0;
                opacity: 0.9;
            }
            
            .contact-info {
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
            }
            
            .contact-item {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 11pt;
            }
            
            .contact-item i {
                width: 16px;
                text-align: center;
            }
            
            .cv-section {
                margin-bottom: 25px;
            }
            
            .section-title {
                font-size: 16pt;
                font-weight: bold;
                color: var(--primary-color, #1e40af);
                margin-bottom: 15px;
                padding-bottom: 8px;
                border-bottom: 2px solid var(--primary-color, #1e40af);
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .section-title i {
                font-size: 14pt;
            }
            
            .summary-text {
                font-size: 12pt;
                line-height: 1.6;
                text-align: justify;
                margin: 0;
            }
            
            .experience-item,
            .education-item {
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 1px solid #eee;
            }
            
            .experience-item:last-child,
            .education-item:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }
            
            .experience-header,
            .education-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 8px;
            }
            
            .job-title,
            .degree {
                font-size: 14pt;
                font-weight: bold;
                color: #333;
                margin: 0;
            }
            
            .duration,
            .year {
                font-size: 11pt;
                color: #666;
                font-weight: 500;
            }
            
            .company-name,
            .institution {
                font-size: 12pt;
                color: var(--primary-color, #1e40af);
                font-weight: 500;
                margin-bottom: 8px;
            }
            
            .experience-description,
            .education-description {
                font-size: 11pt;
                line-height: 1.5;
                color: #555;
                margin: 8px 0 0 0;
            }
            
            .skills-category {
                margin-bottom: 15px;
            }
            
            .skills-subtitle {
                font-size: 13pt;
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
            }
            
            .skills-list {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }
            
            .skill-tag {
                background: var(--primary-color, #1e40af);
                color: white;
                padding: 6px 12px;
                border-radius: 15px;
                font-size: 10pt;
                font-weight: 500;
            }
            
            .skill-tag.soft {
                background: var(--secondary-color, #10b981);
            }
            
            .languages-list {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 10px;
            }
            
            .language-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 12px;
                background: #f8f9fa;
                border-radius: 6px;
            }
            
            .language-name {
                font-weight: 500;
                color: #333;
            }
            
            .language-level {
                font-size: 10pt;
                color: #666;
                background: var(--primary-color, #1e40af);
                color: white;
                padding: 2px 8px;
                border-radius: 10px;
            }
            
            /* القالب الكلاسيكي */
            .classic-template .classic-header {
                text-align: center;
                padding-bottom: 20px;
                border-bottom: 3px solid var(--primary-color, #1e40af);
                margin-bottom: 25px;
            }
            
            .classic-template .cv-name {
                font-size: 28pt;
                color: var(--primary-color, #1e40af);
            }
            
            .classic-template .cv-title {
                font-size: 18pt;
                color: #666;
            }
            
            .contact-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
                margin-top: 15px;
            }
            
            /* القالب الإبداعي */
            .creative-template .creative-layout {
                display: grid;
                grid-template-columns: 35% 65%;
                gap: 0;
                min-height: 297mm;
            }
            
            .creative-sidebar {
                background: var(--primary-color, #1e40af);
                color: white;
                padding: 30px 20px;
            }
            
            .creative-main {
                padding: 30px 25px;
            }
            
            .sidebar-photo img {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                object-fit: cover;
                margin-bottom: 20px;
            }
            
            .creative-sidebar .cv-name {
                font-size: 20pt;
                margin-bottom: 8px;
            }
            
            .creative-sidebar .cv-title {
                font-size: 14pt;
                margin-bottom: 25px;
                opacity: 0.9;
            }
            
            .sidebar-section {
                margin-bottom: 25px;
            }
            
            .sidebar-section h3 {
                font-size: 14pt;
                font-weight: bold;
                margin-bottom: 15px;
                padding-bottom: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            }
            
            .sidebar-contact .contact-item {
                margin-bottom: 10px;
                font-size: 10pt;
            }
            
            .sidebar-skill-tag {
                display: inline-block;
                background: rgba(255, 255, 255, 0.2);
                padding: 4px 8px;
                border-radius: 10px;
                font-size: 9pt;
                margin: 2px;
            }
            
            .sidebar-language {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
                font-size: 10pt;
            }
            
            .lang-level {
                background: rgba(255, 255, 255, 0.2);
                padding: 2px 6px;
                border-radius: 8px;
                font-size: 8pt;
            }
            
            @media print {
                .cv-document {
                    margin: 0;
                    padding: 15mm;
                }
                
                .cv-section {
                    page-break-inside: avoid;
                }
                
                .experience-item,
                .education-item {
                    page-break-inside: avoid;
                }
            }
        `;
        
        element.appendChild(style);
    }

    /**
     * تطبيق الأنماط على النسخة المستنسخة
     */
    applyExportStyles(clonedDoc) {
        // إضافة خطوط Google Fonts
        const fontLink = clonedDoc.createElement('link');
        fontLink.href = 'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap';
        fontLink.rel = 'stylesheet';
        clonedDoc.head.appendChild(fontLink);

        // إضافة Font Awesome
        const faLink = clonedDoc.createElement('link');
        faLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
        faLink.rel = 'stylesheet';
        clonedDoc.head.appendChild(faLink);
    }

    /**
     * انتظار تحميل الموارد
     */
    async waitForResourcesLoad(element) {
        // انتظار تحميل الصور
        const images = element.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            return new Promise((resolve) => {
                if (img.complete) {
                    resolve();
                } else {
                    img.onload = resolve;
                    img.onerror = resolve;
                }
            });
        });

        await Promise.all(imagePromises);

        // انتظار إضافي لتحميل الخطوط
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    /**
     * إضافة محتوى متعدد الصفحات
     */
    async addMultiPageContent(pdf, imgData, imgWidth, imgHeight, pageWidth, pageHeight, margin) {
        const maxHeight = pageHeight - (margin * 2);
        let currentY = 0;
        let pageCount = 0;

        while (currentY < imgHeight) {
            if (pageCount > 0) {
                pdf.addPage();
            }

            const remainingHeight = imgHeight - currentY;
            const currentPageHeight = Math.min(maxHeight, remainingHeight);
            
            // إنشاء canvas مؤقت للجزء الحالي
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            
            tempCanvas.width = imgWidth * 2; // scale factor
            tempCanvas.height = currentPageHeight * 2;
            
            const img = new Image();
            img.src = imgData;
            
            await new Promise(resolve => {
                img.onload = () => {
                    tempCtx.drawImage(
                        img,
                        0, currentY * 2, // source x, y
                        imgWidth * 2, currentPageHeight * 2, // source width, height
                        0, 0, // destination x, y
                        imgWidth * 2, currentPageHeight * 2 // destination width, height
                    );
                    resolve();
                };
            });

            const pageImgData = tempCanvas.toDataURL('image/jpeg', 0.95);
            pdf.addImage(pageImgData, 'JPEG', margin, margin, imgWidth, currentPageHeight);

            currentY += maxHeight;
            pageCount++;
        }
    }

    /**
     * إضافة معلومات إضافية للـ PDF
     */
    addPDFMetadata(pdf, cvData) {
        const personal = cvData.personal || {};
        
        pdf.setProperties({
            title: `السيرة الذاتية - ${personal.fullName || 'غير محدد'}`,
            subject: 'السيرة الذاتية',
            author: personal.fullName || 'غير محدد',
            creator: 'منشئ السيرة الذاتية - إلاشرافي',
            producer: 'Elashrafy CV Builder',
            keywords: `سيرة ذاتية, ${personal.jobTitle || ''}, ${personal.fullName || ''}`,
            creationDate: new Date()
        });
    }

    /**
     * توليد اسم الملف
     */
    generateFileName(cvData, extension) {
        const personal = cvData.personal || {};
        const name = personal.fullName || 'السيرة_الذاتية';
        const jobTitle = personal.jobTitle || '';
        const date = new Date().toISOString().split('T')[0];
        
        let fileName = name.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_');
        
        if (jobTitle) {
            fileName += '_' + jobTitle.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_');
        }
        
        fileName += '_' + date;
        
        return `${fileName}.${extension}`;
    }

    /**
     * إظهار شريط تقدم التصدير
     */
    showExportProgress(message, percentage) {
        let progressContainer = document.getElementById('exportProgressContainer');
        
        if (!progressContainer) {
            progressContainer = document.createElement('div');
            progressContainer.id = 'exportProgressContainer';
            progressContainer.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                z-index: 10000;
                min-width: 300px;
                text-align: center;
                font-family: 'Cairo', sans-serif;
            `;
            document.body.appendChild(progressContainer);
        }

        progressContainer.innerHTML = `
            <div style="margin-bottom: 20px;">
                <i class="fas fa-file-pdf" style="font-size: 2rem; color: var(--primary-color); margin-bottom: 10px;"></i>
                <h3 style="margin: 0; color: #333;">${message}</h3>
            </div>
            <div style="background: #f0f0f0; border-radius: 10px; overflow: hidden; margin-bottom: 10px;">
                <div style="background: var(--primary-color); height: 8px; width: ${percentage}%; transition: width 0.3s ease;"></div>
            </div>
            <div style="color: #666; font-size: 0.9rem;">${percentage}%</div>
        `;
    }

    /**
     * إظهار خطأ التصدير
     */
    showExportError(message) {
        const progressContainer = document.getElementById('exportProgressContainer');
        if (progressContainer) {
            progressContainer.innerHTML = `
                <div style="text-align: center;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: var(--error-color); margin-bottom: 10px;"></i>
                    <h3 style="margin: 0 0 10px 0; color: var(--error-color);">خطأ في التصدير</h3>
                    <p style="margin: 0 0 20px 0; color: #666;">${message}</p>
                    <button onclick="this.closest('#exportProgressContainer').remove()" 
                            style="background: var(--error-color); color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
                        إغلاق
                    </button>
                </div>
            `;
        }
    }

    /**
     * إخفاء شريط تقدم التصدير
     */
    hideExportProgress() {
        const progressContainer = document.getElementById('exportProgressContainer');
        if (progressContainer) {
            progressContainer.remove();
        }
    }

    /**
     * تصدير البيانات كـ JSON
     */
    exportDataAsJSON(cvData) {
        const dataStr = JSON.stringify(cvData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = this.generateFileName(cvData, 'json');
        link.click();
        
        URL.revokeObjectURL(url);
        console.log('✅ تم تصدير البيانات كـ JSON');
    }

    /**
     * طباعة السيرة الذاتية
     */
    async printCV(cvData, templateName = 'modern') {
        try {
            // إنشاء نافذة جديدة للطباعة
            const printWindow = window.open('', '_blank');
            
            // إنشاء المحتوى
            const templatesEngine = new CVTemplatesEngine();
            const content = templatesEngine.templates[templateName].call(templatesEngine, cvData, false);
            
            // إنشاء HTML كامل للطباعة
            const printHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>طباعة السيرة الذاتية</title>
                    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
                    <style>
                        ${await this.getPrintStyles()}
                    </style>
                </head>
                <body>
                    ${content}
                </body>
                </html>
            `;
            
            printWindow.document.write(printHTML);
            printWindow.document.close();
            
            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = () => {
                setTimeout(() => {
                    printWindow.print();
                    printWindow.close();
                }, 500);
            };
            
            console.log('✅ تم فتح نافذة الطباعة');
            
        } catch (error) {
            console.error('❌ خطأ في الطباعة:', error);
            throw error;
        }
    }

    /**
     * الحصول على أنماط الطباعة
     */
    async getPrintStyles() {
        return `
            * { box-sizing: border-box; }
            body { 
                font-family: 'Cairo', sans-serif; 
                margin: 0; 
                padding: 15mm; 
                direction: rtl; 
                text-align: right;
                font-size: 12pt;
                line-height: 1.4;
                color: #333;
            }
            .cv-document { 
                width: 100%; 
                background: white; 
            }
            @page { 
                size: A4; 
                margin: 15mm; 
            }
            @media print {
                .cv-section { page-break-inside: avoid; }
                .experience-item, .education-item { page-break-inside: avoid; }
            }
            /* باقي الأنماط من addExportStyles */
        `;
    }
}

console.log('📄 تم تحميل نظام التصدير بنجاح');
