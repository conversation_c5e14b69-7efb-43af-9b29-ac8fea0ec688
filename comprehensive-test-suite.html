<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجموعة الاختبارات الشاملة - CV Builder</title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/cv-builder.css">
    <link rel="stylesheet" href="css/cv-templates.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        .test-suite {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Cairo', sans-serif;
        }

        .test-header {
            background: var(--gradient-primary);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-section {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 20px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--bg-secondary);
        }

        .test-section h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .test-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .test-btn.primary { background: var(--primary-color); color: white; }
        .test-btn.success { background: var(--success-color); color: white; }
        .test-btn.warning { background: var(--warning-color); color: white; }
        .test-btn.danger { background: var(--danger-color); color: white; }
        .test-btn.info { background: var(--info-color); color: white; }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .test-results {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success { background: var(--success-color); }
        .status-error { background: var(--danger-color); }
        .status-warning { background: var(--warning-color); }
        .status-info { background: var(--info-color); }
        .status-pending { background: var(--text-light); }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-secondary);
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: var(--gradient-primary);
            width: 0%;
            transition: width 0.3s ease;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            background: var(--bg-card);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid var(--bg-secondary);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        .browser-info {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .feature-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: var(--bg-card);
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .cv-preview-mini {
            width: 100%;
            height: 400px;
            border: 1px solid var(--bg-secondary);
            border-radius: 8px;
            overflow: hidden;
            background: var(--bg-card);
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }

            .test-buttons {
                flex-direction: column;
            }

            .test-btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="test-suite">
        <!-- Header -->
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> مجموعة الاختبارات الشاملة</h1>
            <p>اختبار شامل لجميع وظائف منشئ السيرة الذاتية</p>
            <div class="browser-info" id="browserInfo">
                <strong>معلومات المتصفح:</strong> <span id="browserDetails">جاري التحميل...</span>
            </div>
        </div>

        <!-- Progress Overview -->
        <div class="test-section">
            <h3><i class="fas fa-chart-line"></i> نظرة عامة على التقدم</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="overallProgress"></div>
            </div>
            <div class="summary-stats" id="summaryStats">
                <div class="stat-card">
                    <div class="stat-number" id="totalTests">0</div>
                    <div class="stat-label">إجمالي الاختبارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="passedTests">0</div>
                    <div class="stat-label">نجح</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failedTests">0</div>
                    <div class="stat-label">فشل</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="warningTests">0</div>
                    <div class="stat-label">تحذيرات</div>
                </div>
            </div>
        </div>

        <!-- Test Grid -->
        <div class="test-grid">
            <!-- Core Functionality Tests -->
            <div class="test-section">
                <h3><i class="fas fa-cogs"></i> الوظائف الأساسية</h3>
                <div class="test-buttons">
                    <button class="test-btn primary" onclick="runTest('initialization')">
                        <i class="fas fa-play"></i> اختبار التهيئة
                    </button>
                    <button class="test-btn info" onclick="runTest('libraries')">
                        <i class="fas fa-book"></i> اختبار المكتبات
                    </button>
                    <button class="test-btn success" onclick="runTest('dom-elements')">
                        <i class="fas fa-code"></i> عناصر DOM
                    </button>
                    <button class="test-btn warning" onclick="runTest('event-listeners')">
                        <i class="fas fa-mouse-pointer"></i> مستمعي الأحداث
                    </button>
                </div>
                <div class="test-results" id="coreResults"></div>
            </div>

            <!-- Preview System Tests -->
            <div class="test-section">
                <h3><i class="fas fa-eye"></i> نظام المعاينة</h3>
                <div class="test-buttons">
                    <button class="test-btn primary" onclick="runTest('preview-init')">
                        <i class="fas fa-play"></i> تهيئة المعاينة
                    </button>
                    <button class="test-btn info" onclick="runTest('real-time-update')">
                        <i class="fas fa-sync"></i> التحديث المباشر
                    </button>
                    <button class="test-btn success" onclick="runTest('template-switching')">
                        <i class="fas fa-exchange-alt"></i> تبديل القوالب
                    </button>
                    <button class="test-btn warning" onclick="runTest('zoom-controls')">
                        <i class="fas fa-search-plus"></i> أدوات التكبير
                    </button>
                </div>
                <div class="test-results" id="previewResults"></div>
            </div>
        </div>
    </div>
