/**
 * نظام التحقق من صحة البيانات
 * نظام شامل للتحقق من صحة جميع حقول السيرة الذاتية
 */

class CVValidation {
    constructor() {
        this.rules = {
            // قواعد التحقق من البيانات الشخصية
            fullName: {
                required: true,
                minLength: 2,
                maxLength: 100,
                pattern: /^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\u0020-\u007E]+$/,
                message: 'يرجى إدخال اسم صحيح (عربي أو إنجليزي)'
            },
            
            jobTitle: {
                required: true,
                minLength: 2,
                maxLength: 100,
                message: 'يرجى إدخال مسمى وظيفي صحيح'
            },
            
            email: {
                required: true,
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: 'يرجى إدخال بريد إلكتروني صحيح'
            },
            
            phone: {
                required: true,
                pattern: /^[\+]?[0-9\s\-\(\)]{8,20}$/,
                message: 'يرجى إدخال رقم هاتف صحيح'
            },
            
            city: {
                required: false,
                minLength: 2,
                maxLength: 100,
                message: 'يرجى إدخال اسم مدينة صحيح'
            },
            
            website: {
                required: false,
                pattern: /^https?:\/\/.+\..+/,
                message: 'يرجى إدخال رابط موقع صحيح (يبدأ بـ http:// أو https://)'
            },
            
            linkedin: {
                required: false,
                pattern: /^https?:\/\/(www\.)?linkedin\.com\/.+/,
                message: 'يرجى إدخال رابط LinkedIn صحيح'
            },
            
            github: {
                required: false,
                pattern: /^https?:\/\/(www\.)?github\.com\/.+/,
                message: 'يرجى إدخال رابط GitHub صحيح'
            },
            
            // قواعد التحقق من الملخص المهني
            professionalSummary: {
                required: true,
                minLength: 50,
                maxLength: 500,
                message: 'يرجى كتابة ملخص مهني بين 50-500 حرف'
            },
            
            // قواعد التحقق من الخبرات العملية
            jobTitleExp: {
                required: true,
                minLength: 2,
                maxLength: 100,
                message: 'يرجى إدخال مسمى وظيفي صحيح'
            },
            
            company: {
                required: true,
                minLength: 2,
                maxLength: 100,
                message: 'يرجى إدخال اسم شركة صحيح'
            },
            
            startDate: {
                required: true,
                pattern: /^\d{4}-\d{2}$/,
                message: 'يرجى اختيار تاريخ بداية صحيح'
            },
            
            endDate: {
                required: false,
                pattern: /^\d{4}-\d{2}$/,
                message: 'يرجى اختيار تاريخ نهاية صحيح'
            },
            
            // قواعد التحقق من التعليم
            degree: {
                required: true,
                minLength: 2,
                maxLength: 100,
                message: 'يرجى إدخال درجة علمية صحيحة'
            },
            
            institution: {
                required: true,
                minLength: 2,
                maxLength: 100,
                message: 'يرجى إدخال اسم مؤسسة تعليمية صحيح'
            },
            
            year: {
                required: true,
                pattern: /^\d{4}$/,
                min: 1950,
                max: new Date().getFullYear() + 10,
                message: 'يرجى إدخال سنة تخرج صحيحة'
            },
            
            gpa: {
                required: false,
                pattern: /^[0-9\.\/\s]+$/,
                message: 'يرجى إدخال معدل تراكمي صحيح'
            },
            
            // قواعد التحقق من اللغات
            languageName: {
                required: true,
                minLength: 2,
                maxLength: 50,
                message: 'يرجى إدخال اسم لغة صحيح'
            },
            
            languageLevel: {
                required: true,
                options: ['مبتدئ', 'متوسط', 'متقدم', 'ممتاز', 'اللغة الأم'],
                message: 'يرجى اختيار مستوى إتقان اللغة'
            }
        };
        
        this.errorMessages = {};
        console.log('✅ تم تهيئة نظام التحقق من صحة البيانات');
    }

    /**
     * التحقق من صحة حقل واحد
     */
    validateField(field) {
        if (!field) return false;

        const fieldName = field.name || field.id;
        const value = field.value ? field.value.trim() : '';
        
        // الحصول على قواعد التحقق
        let rule = this.rules[fieldName];
        
        // قواعد خاصة للحقول المتكررة
        if (!rule) {
            if (fieldName === 'jobTitle' && field.closest('.experience-item')) {
                rule = this.rules.jobTitleExp;
            } else if (fieldName === 'name' && field.closest('.language-item')) {
                rule = this.rules.languageName;
            } else if (fieldName === 'level' && field.closest('.language-item')) {
                rule = this.rules.languageLevel;
            }
        }

        if (!rule) return true; // لا توجد قواعد للتحقق

        // إجراء التحقق
        const validation = this.performValidation(value, rule, field);
        
        // تطبيق النتيجة على الحقل
        this.applyValidationResult(field, validation);
        
        return validation.isValid;
    }

    /**
     * تنفيذ التحقق من صحة البيانات
     */
    performValidation(value, rule, field) {
        const errors = [];

        // التحقق من الحقول المطلوبة
        if (rule.required && !value) {
            errors.push('هذا الحقل مطلوب');
        }

        // إذا كان الحقل فارغاً وغير مطلوب، فهو صحيح
        if (!value && !rule.required) {
            return { isValid: true, errors: [] };
        }

        // التحقق من الحد الأدنى للطول
        if (rule.minLength && value.length < rule.minLength) {
            errors.push(`يجب أن يكون النص ${rule.minLength} أحرف على الأقل`);
        }

        // التحقق من الحد الأقصى للطول
        if (rule.maxLength && value.length > rule.maxLength) {
            errors.push(`يجب أن يكون النص ${rule.maxLength} حرف كحد أقصى`);
        }

        // التحقق من النمط (Pattern)
        if (rule.pattern && !rule.pattern.test(value)) {
            errors.push(rule.message || 'تنسيق البيانات غير صحيح');
        }

        // التحقق من القيم المسموحة
        if (rule.options && !rule.options.includes(value)) {
            errors.push(rule.message || 'يرجى اختيار قيمة صحيحة');
        }

        // التحقق من القيم الرقمية
        if (rule.min !== undefined || rule.max !== undefined) {
            const numValue = parseInt(value);
            if (isNaN(numValue)) {
                errors.push('يرجى إدخال رقم صحيح');
            } else {
                if (rule.min !== undefined && numValue < rule.min) {
                    errors.push(`القيمة يجب أن تكون ${rule.min} أو أكثر`);
                }
                if (rule.max !== undefined && numValue > rule.max) {
                    errors.push(`القيمة يجب أن تكون ${rule.max} أو أقل`);
                }
            }
        }

        // تحقق خاص للتواريخ
        if (field.name === 'endDate' && value) {
            const startDateField = field.closest('.experience-item')?.querySelector('[name="startDate"]');
            if (startDateField && startDateField.value) {
                const startDate = new Date(startDateField.value + '-01');
                const endDate = new Date(value + '-01');
                
                if (endDate <= startDate) {
                    errors.push('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
                }
            }
        }

        // تحقق خاص للبريد الإلكتروني المتقدم
        if (field.name === 'email' && value) {
            if (!this.isValidEmailDomain(value)) {
                errors.push('نطاق البريد الإلكتروني غير صحيح');
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * تطبيق نتيجة التحقق على الحقل
     */
    applyValidationResult(field, validation) {
        const fieldName = field.name || field.id;
        
        // إزالة الفئات السابقة
        field.classList.remove('valid', 'invalid');
        
        // إضافة الفئة المناسبة
        if (validation.isValid) {
            field.classList.add('valid');
            this.clearFieldError(fieldName);
        } else {
            field.classList.add('invalid');
            this.showFieldError(fieldName, validation.errors[0]);
        }
    }

    /**
     * إظهار رسالة خطأ للحقل
     */
    showFieldError(fieldName, message) {
        const errorElement = document.getElementById(`${fieldName}Error`);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
        
        this.errorMessages[fieldName] = message;
    }

    /**
     * مسح رسالة خطأ الحقل
     */
    clearFieldError(fieldName) {
        const errorElement = document.getElementById(`${fieldName}Error`);
        if (errorElement) {
            errorElement.textContent = '';
            errorElement.style.display = 'none';
        }
        
        delete this.errorMessages[fieldName];
    }

    /**
     * التحقق من صحة نطاق البريد الإلكتروني
     */
    isValidEmailDomain(email) {
        const domain = email.split('@')[1];
        if (!domain) return false;
        
        // قائمة النطاقات المحظورة
        const blockedDomains = [
            'tempmail.org',
            '10minutemail.com',
            'guerrillamail.com',
            'mailinator.com'
        ];
        
        return !blockedDomains.includes(domain.toLowerCase());
    }

    /**
     * التحقق من صحة نموذج كامل
     */
    validateForm(formElement) {
        if (!formElement) return false;

        const fields = formElement.querySelectorAll('input, textarea, select');
        let isValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    /**
     * التحقق من صحة خطوة معينة
     */
    validateStep(stepNumber) {
        const stepElement = document.getElementById(`step${stepNumber}`);
        if (!stepElement) return true;

        return this.validateForm(stepElement);
    }

    /**
     * التحقق من صحة جميع البيانات
     */
    validateAllData(cvData) {
        const errors = [];

        // التحقق من البيانات الشخصية
        const personalErrors = this.validatePersonalData(cvData.personal);
        errors.push(...personalErrors);

        // التحقق من الملخص المهني
        if (!cvData.professionalSummary || cvData.professionalSummary.trim().length < 50) {
            errors.push('الملخص المهني مطلوب ويجب أن يكون 50 حرف على الأقل');
        }

        // التحقق من الخبرات العملية
        if (!cvData.experiences || cvData.experiences.length === 0) {
            errors.push('يجب إضافة خبرة عملية واحدة على الأقل');
        } else {
            cvData.experiences.forEach((exp, index) => {
                if (!exp.jobTitle || !exp.company) {
                    errors.push(`الخبرة العملية ${index + 1}: المسمى الوظيفي واسم الشركة مطلوبان`);
                }
            });
        }

        // التحقق من التعليم
        if (!cvData.education || cvData.education.length === 0) {
            errors.push('يجب إضافة مؤهل أكاديمي واحد على الأقل');
        } else {
            cvData.education.forEach((edu, index) => {
                if (!edu.degree || !edu.institution) {
                    errors.push(`المؤهل الأكاديمي ${index + 1}: الدرجة العلمية واسم المؤسسة مطلوبان`);
                }
            });
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * التحقق من صحة البيانات الشخصية
     */
    validatePersonalData(personal) {
        const errors = [];
        const requiredFields = ['fullName', 'email', 'phone'];

        requiredFields.forEach(field => {
            if (!personal[field] || personal[field].trim().length === 0) {
                errors.push(`${this.getFieldDisplayName(field)} مطلوب`);
            }
        });

        // التحقق من تنسيق البريد الإلكتروني
        if (personal.email && !this.rules.email.pattern.test(personal.email)) {
            errors.push('تنسيق البريد الإلكتروني غير صحيح');
        }

        // التحقق من تنسيق رقم الهاتف
        if (personal.phone && !this.rules.phone.pattern.test(personal.phone)) {
            errors.push('تنسيق رقم الهاتف غير صحيح');
        }

        return errors;
    }

    /**
     * الحصول على اسم الحقل للعرض
     */
    getFieldDisplayName(fieldName) {
        const displayNames = {
            fullName: 'الاسم الكامل',
            jobTitle: 'المسمى الوظيفي',
            email: 'البريد الإلكتروني',
            phone: 'رقم الهاتف',
            city: 'المدينة',
            website: 'الموقع الشخصي',
            linkedin: 'LinkedIn',
            github: 'GitHub',
            professionalSummary: 'الملخص المهني'
        };

        return displayNames[fieldName] || fieldName;
    }

    /**
     * الحصول على جميع رسائل الخطأ الحالية
     */
    getAllErrors() {
        return Object.values(this.errorMessages);
    }

    /**
     * مسح جميع رسائل الخطأ
     */
    clearAllErrors() {
        Object.keys(this.errorMessages).forEach(fieldName => {
            this.clearFieldError(fieldName);
        });
        this.errorMessages = {};
    }

    /**
     * التحقق من قوة كلمة المرور (للاستخدام المستقبلي)
     */
    validatePasswordStrength(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

        const score = [
            password.length >= minLength,
            hasUpperCase,
            hasLowerCase,
            hasNumbers,
            hasSpecialChar
        ].filter(Boolean).length;

        return {
            score: score,
            strength: score < 3 ? 'ضعيف' : score < 4 ? 'متوسط' : 'قوي',
            isValid: score >= 3
        };
    }
}

console.log('✅ تم تحميل نظام التحقق من صحة البيانات');
