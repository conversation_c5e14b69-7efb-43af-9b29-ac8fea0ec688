/* ===== منشئ السيرة الذاتية المتقدم - التصميم الجديد ===== */

/* المتغيرات الأساسية */
:root {
    /* الألوان الأساسية */
    --primary-color: #1e40af;
    --primary-light: #3b82f6;
    --primary-dark: #1e3a8a;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;

    /* ألوان الخلفية */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: #ffffff;
    --bg-overlay: rgba(0, 0, 0, 0.5);

    /* ألوان النص */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-light: #94a3b8;
    --text-white: #ffffff;

    /* ألوان الحالة */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;

    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* الحدود */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;

    /* المسافات */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;

    /* الخطوط */
    --font-family-primary: 'Cairo', sans-serif;
    --font-family-secondary: 'Amiri', serif;

    /* أحجام الخط */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* الانتقالات */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    direction: rtl;
    text-align: right;
}

/* شاشة التحميل */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow);
}

.loading-screen.fade-out {
    opacity: 0;
}

.loading-content {
    text-align: center;
    color: var(--text-white);
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    animation: pulse 2s infinite;
}

.loading-content h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--text-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: var(--spacing-lg) auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* الحاوي الرئيسي */
.main-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: opacity var(--transition-normal);
}

.main-container.hidden {
    opacity: 0;
    pointer-events: none;
}

/* شريط التنقل العلوي */
.top-navbar {
    background: var(--bg-card);
    border-bottom: 1px solid var(--bg-tertiary);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
}

.navbar-brand i {
    font-size: var(--font-size-xl);
}

.navbar-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* شريط التقدم */
.progress-bar-container {
    background: var(--bg-card);
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--bg-tertiary);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-radius: var(--border-radius-sm);
    transition: width var(--transition-normal);
    width: 0%;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 280px 1fr 350px;
    gap: 0;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* الشريط الجانبي */
.sidebar {
    background: var(--bg-card);
    border-left: 1px solid var(--bg-tertiary);
    padding: var(--spacing-lg);
    overflow-y: auto;
    height: calc(100vh - 140px);
}

.sidebar-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

/* خطوات التنقل */
.steps-navigation {
    margin-bottom: var(--spacing-xl);
}

.step-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.step-item:hover {
    background: var(--bg-secondary);
}

.step-item.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--text-white);
}

.step-item.completed {
    background: var(--bg-secondary);
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.step-item.active .step-icon {
    background: rgba(255, 255, 255, 0.3);
}

.step-content h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.step-content p {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.step-status {
    margin-right: auto;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.step-item.completed .step-status {
    opacity: 1;
    color: var(--success-color);
}

/* إحصائيات التقدم */
.progress-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* منطقة المحتوى */
.content-area {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    overflow-y: auto;
    height: calc(100vh - 140px);
}

/* حاوي النماذج */
.form-container {
    max-width: 800px;
    margin: 0 auto;
}

/* محتوى الخطوات */
.step-content {
    display: none;
    animation: fadeInUp 0.5s ease-out;
}

.step-content.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* رأس الخطوة */
.step-header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.step-header h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.step-header h2 i {
    color: var(--primary-color);
}

.step-header p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* قسم النموذج */
.form-section {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
}

/* شبكة النموذج */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

/* مجموعة النموذج */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group.full-width {
    grid-column: 1 / -1;
}

/* تسميات النموذج */
label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

label.required::after {
    content: ' *';
    color: var(--error-color);
}

.section-label {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: block;
}

/* حقول الإدخال */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="url"],
textarea,
select {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--bg-tertiary);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
    direction: rtl;
}

input:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

input.valid {
    border-color: var(--success-color);
}

input.invalid {
    border-color: var(--error-color);
}

textarea {
    resize: vertical;
    min-height: 120px;
}

/* تلميحات الحقول */
.field-hint {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    margin-top: var(--spacing-xs);
}

/* رسائل الخطأ */
.error-message {
    font-size: var(--font-size-xs);
    color: var(--error-color);
    margin-top: var(--spacing-xs);
    display: none;
}

.error-message:not(:empty) {
    display: block;
}

/* عداد الأحرف */
.character-counter {
    text-align: left;
    font-size: var(--font-size-xs);
    color: var(--text-light);
    margin-top: var(--spacing-xs);
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-weight: 500;
    font-family: var(--font-family-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--text-white);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-color);
    color: var(--text-white);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    color: var(--text-white);
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

/* أزرار التنقل */
.navigation-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--bg-tertiary);
}

/* رفع الصورة */
.photo-upload-section {
    margin-bottom: var(--spacing-xl);
}

.photo-upload-area {
    border: 2px dashed var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--bg-secondary);
}

.photo-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(30, 64, 175, 0.05);
}

.photo-upload-area.drag-over {
    border-color: var(--primary-color);
    background: rgba(30, 64, 175, 0.1);
}

.photo-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.photo-preview i {
    font-size: 3rem;
    color: var(--text-light);
}

.photo-preview h4 {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    margin: 0;
}

.photo-preview p {
    color: var(--text-secondary);
    margin: 0;
}

.photo-preview small {
    color: var(--text-light);
}

/* لوحة المعاينة */
.preview-panel {
    background: var(--bg-card);
    border-right: 1px solid var(--bg-tertiary);
    padding: var(--spacing-lg);
    overflow-y: auto;
    height: calc(100vh - 140px);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--bg-tertiary);
}

.preview-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.preview-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.control-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.control-btn:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

.zoom-level {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    min-width: 40px;
    text-align: center;
}

/* اختيار القالب */
.template-selector {
    margin-bottom: var(--spacing-lg);
}

.template-selector label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.template-options {
    display: flex;
    gap: var(--spacing-sm);
}

.template-option {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    border: 2px solid var(--bg-tertiary);
    background: var(--bg-primary);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.template-option:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.template-option.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--text-white);
}

.template-option i {
    font-size: var(--font-size-lg);
}

/* منطقة المعاينة */
.preview-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    min-height: 400px;
}

.cv-preview {
    background: var(--bg-primary);
    border-radius: var(--border-radius-sm);
    min-height: 380px;
    box-shadow: var(--shadow-md);
    transform-origin: top center;
    transition: transform var(--transition-normal);
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 380px;
    color: var(--text-light);
    text-align: center;
}

.preview-placeholder i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.preview-placeholder h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
}

/* التصميم المتجاوب */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 250px 1fr 300px;
    }
}

@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }

    .sidebar {
        height: auto;
        border-left: none;
        border-bottom: 1px solid var(--bg-tertiary);
    }

    .preview-panel {
        display: none;
    }

    .content-area {
        height: auto;
    }
}

@media (max-width: 768px) {
    .navbar-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .navbar-actions {
        width: 100%;
        justify-content: center;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .navigation-buttons {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .steps-navigation {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .step-item {
        flex-direction: column;
        text-align: center;
        padding: var(--spacing-sm);
    }

    .step-content h4 {
        font-size: var(--font-size-sm);
    }

    .step-content p {
        display: none;
    }
}

@media (max-width: 480px) {
    .content-area {
        padding: var(--spacing-md);
    }

    .form-section {
        padding: var(--spacing-lg);
    }

    .step-header h2 {
        font-size: var(--font-size-2xl);
        flex-direction: column;
    }
}

/* أنماط إضافية للعناصر الجديدة */

/* قسم النصائح */
.tips-section {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #bbdefb;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.tips-section h4 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.tips-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tips-section li {
    position: relative;
    padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
    color: var(--text-secondary);
    line-height: 1.6;
}

.tips-section li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: var(--spacing-sm);
    color: var(--success-color);
    font-weight: bold;
    font-size: var(--font-size-sm);
}

/* قسم الأمثلة */
.examples-section {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.examples-section h4 {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.example-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.example-card {
    background: var(--bg-card);
    border: 2px solid var(--bg-tertiary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.example-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 64, 175, 0.1), transparent);
    transition: left 0.5s ease;
}

.example-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.example-card:hover::before {
    left: 100%;
}

.example-card h5 {
    color: var(--primary-color);
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: 0 0 var(--spacing-sm) 0;
}

.example-card p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin: 0;
}

/* عناصر النماذج المتقدمة */
.form-group.has-icon {
    position: relative;
}

.form-group.has-icon input {
    padding-right: 40px;
}

.form-group.has-icon .input-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

.form-group.has-icon input:focus + .input-icon {
    color: var(--primary-color);
}

/* أزرار إضافة العناصر */
.add-item-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    color: var(--text-secondary);
    border: 2px dashed var(--bg-tertiary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.add-item-btn:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--text-white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.add-item-btn i {
    font-size: var(--font-size-lg);
}

/* عناصر الخبرات والتعليم */
.experience-item,
.education-item,
.language-item {
    background: var(--bg-card);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    position: relative;
    transition: all var(--transition-fast);
}

.experience-item:hover,
.education-item:hover,
.language-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--bg-tertiary);
}

.item-header h4 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.remove-item-btn {
    background: var(--error-color);
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.remove-item-btn:hover {
    background: #dc2626;
    transform: scale(1.05);
}

/* مدخلات المهارات */
.skills-input-container {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.skills-input-container input {
    flex: 1;
}

.skills-tags {
    min-height: 60px;
    border: 2px dashed var(--bg-tertiary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    align-items: flex-start;
    align-content: flex-start;
}

.skills-tags:empty::before {
    content: 'ستظهر المهارات هنا بعد إضافتها';
    color: var(--text-light);
    font-style: italic;
    font-size: var(--font-size-sm);
}

.skill-tag {
    background: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.skill-tag:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.skill-tag.soft-skill {
    background: var(--secondary-color);
}

.skill-tag.soft-skill:hover {
    background: #059669;
}

.skill-tag button {
    background: none;
    border: none;
    color: var(--text-white);
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    transition: all var(--transition-fast);
}

.skill-tag button:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* اختيار القوالب */
.template-selection-section {
    margin-bottom: var(--spacing-xl);
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.template-card {
    border: 2px solid var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--bg-card);
}

.template-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.template-card.active {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.template-preview {
    height: 150px;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, var(--primary-color) 0%, var(--primary-light) 100%);
    opacity: 0.1;
}

.template-info {
    padding: var(--spacing-md);
    text-align: center;
}

.template-info h4 {
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: 0 0 var(--spacing-xs) 0;
}

.template-info p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* اختيار الألوان */
.color-selection-section {
    margin-bottom: var(--spacing-xl);
}

.color-schemes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.color-scheme {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: 2px solid var(--bg-tertiary);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--bg-card);
}

.color-scheme:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.color-scheme.active {
    border-color: var(--primary-color);
    background: rgba(30, 64, 175, 0.05);
}

.color-preview {
    display: flex;
    gap: 4px;
}

.color-primary,
.color-secondary {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: var(--shadow-sm);
}

.color-scheme span {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

/* خيارات إضافية */
.additional-options {
    margin-bottom: var(--spacing-xl);
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.option-item {
    background: var(--bg-card);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    transition: all var(--transition-fast);
}

.option-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--bg-tertiary);
    border-radius: var(--border-radius-sm);
    position: relative;
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .example-cards {
        grid-template-columns: 1fr;
    }

    .templates-grid {
        grid-template-columns: 1fr;
    }

    .color-schemes {
        grid-template-columns: repeat(2, 1fr);
    }

    .options-grid {
        grid-template-columns: 1fr;
    }

    .skills-input-container {
        flex-direction: column;
    }
}

/* ===== أنماط الذكاء الاصطناعي ===== */

/* اقتراحات الذكاء الاصطناعي */
.ai-suggestions {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 2px solid #0ea5e9;
    border-radius: var(--border-radius-lg);
    margin-top: var(--spacing-md);
    overflow: hidden;
    animation: slideInUp 0.3s ease-out;
}

.ai-suggestions-header {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    color: white;
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    position: relative;
}

.ai-suggestions-header i {
    font-size: var(--font-size-lg);
    animation: pulse 2s infinite;
}

.ai-close-btn {
    position: absolute;
    left: var(--spacing-md);
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.ai-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.ai-suggestions-content {
    padding: var(--spacing-md);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.ai-suggestion-btn {
    background: white;
    border: 1px solid #0ea5e9;
    color: #0ea5e9;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.ai-suggestion-btn:hover {
    background: #0ea5e9;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
}

.ai-suggestions-list {
    padding: var(--spacing-md);
    max-height: 300px;
    overflow-y: auto;
}

.ai-suggestion-item {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.ai-suggestion-item:hover {
    border-color: #0ea5e9;
    box-shadow: 0 2px 8px rgba(14, 165, 233, 0.1);
    transform: translateY(-1px);
}

.suggestion-text {
    flex: 1;
    line-height: 1.5;
    color: var(--text-primary);
}

.suggestion-apply-btn {
    background: #10b981;
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.suggestion-apply-btn:hover {
    background: #059669;
    transform: scale(1.1);
}

/* تحليل الملخص المهني */
.ai-analysis {
    background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
    border: 2px solid #f59e0b;
    border-radius: var(--border-radius-lg);
    margin-top: var(--spacing-md);
    overflow: hidden;
    animation: slideInUp 0.3s ease-out;
}

.ai-analysis-header {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
}

.ai-analysis-content {
    padding: var(--spacing-md);
}

.analysis-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.metric {
    text-align: center;
    padding: var(--spacing-sm);
    background: white;
    border-radius: var(--border-radius-md);
    border: 1px solid #fbbf24;
}

.metric-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.metric-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
}

.metric-value.good {
    color: #10b981;
}

.metric-value.warning {
    color: #f59e0b;
}

.analysis-suggestions h5 {
    color: #d97706;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-base);
}

.analysis-suggestions ul {
    list-style: none;
    padding: 0;
}

.analysis-suggestions li {
    background: white;
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    border-right: 3px solid #f59e0b;
    font-size: var(--font-size-sm);
}

/* لوحة تحليل الجودة */
.ai-quality-panel {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border: 2px solid #8b5cf6;
}

.quality-header h3 {
    color: #8b5cf6;
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.quality-score {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.score-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    position: relative;
    background: conic-gradient(from 0deg, var(--score-color) 0deg, var(--score-color) calc(var(--score-percentage) * 3.6deg), #e5e7eb calc(var(--score-percentage) * 3.6deg));
}

.score-circle.excellent {
    --score-color: #10b981;
}

.score-circle.good {
    --score-color: #3b82f6;
}

.score-circle.fair {
    --score-color: #f59e0b;
}

.score-circle.poor {
    --score-color: #ef4444;
}

.score-circle::before {
    content: '';
    position: absolute;
    width: 70px;
    height: 70px;
    background: var(--bg-card);
    border-radius: 50%;
}

.score-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    z-index: 1;
}

.score-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    z-index: 1;
}

.score-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.quality-metrics {
    margin-bottom: var(--spacing-xl);
}

.metric-item {
    margin-bottom: var(--spacing-md);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.metric-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.metric-score {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-secondary);
}

.metric-bar {
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #8b5cf6 0%, #a78bfa 100%);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.quality-recommendations {
    margin-bottom: var(--spacing-lg);
}

.quality-recommendations h4 {
    color: #8b5cf6;
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.recommendation-item {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    border-right: 4px solid;
}

.recommendation-item.high {
    border-right-color: #ef4444;
}

.recommendation-item.medium {
    border-right-color: #f59e0b;
}

.recommendation-item.low {
    border-right-color: #10b981;
}

.recommendation-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
}

.recommendation-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.recommendation-description {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    line-height: 1.4;
}

.recommendation-action {
    font-size: var(--font-size-xs);
    color: #8b5cf6;
    font-weight: 500;
    background: rgba(139, 92, 246, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    display: inline-block;
}

.quality-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.quality-actions .btn {
    flex: 1;
    font-size: var(--font-size-xs);
    padding: var(--spacing-sm) var(--spacing-md);
}

/* رسوم متحركة للذكاء الاصطناعي */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .ai-suggestions-content {
        flex-direction: column;
    }

    .ai-suggestion-btn {
        width: 100%;
        justify-content: center;
    }

    .analysis-metrics {
        grid-template-columns: 1fr;
    }

    .score-circle {
        width: 80px;
        height: 80px;
    }

    .score-circle::before {
        width: 56px;
        height: 56px;
    }

    .quality-actions {
        flex-direction: column;
    }
}
