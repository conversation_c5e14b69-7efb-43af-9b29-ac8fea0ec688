/* ===== منشئ السيرة الذاتية المتقدم - التصميم الجديد ===== */

/* المتغيرات الأساسية */
:root {
    /* الألوان الأساسية */
    --primary-color: #1e40af;
    --primary-light: #3b82f6;
    --primary-dark: #1e3a8a;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    
    /* ألوان الخلفية */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: #ffffff;
    --bg-overlay: rgba(0, 0, 0, 0.5);
    
    /* ألوان النص */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-light: #94a3b8;
    --text-white: #ffffff;
    
    /* ألوان الحالة */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* الحدود */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    
    /* المسافات */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* الخطوط */
    --font-family-primary: 'Cairo', sans-serif;
    --font-family-secondary: 'Amiri', serif;
    
    /* أحجام الخط */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* الانتقالات */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    direction: rtl;
    text-align: right;
}

/* شاشة التحميل */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow);
}

.loading-screen.fade-out {
    opacity: 0;
}

.loading-content {
    text-align: center;
    color: var(--text-white);
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    animation: pulse 2s infinite;
}

.loading-content h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--text-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: var(--spacing-lg) auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* الحاوي الرئيسي */
.main-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: opacity var(--transition-normal);
}

.main-container.hidden {
    opacity: 0;
    pointer-events: none;
}

/* شريط التنقل العلوي */
.top-navbar {
    background: var(--bg-card);
    border-bottom: 1px solid var(--bg-tertiary);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
}

.navbar-brand i {
    font-size: var(--font-size-xl);
}

.navbar-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* شريط التقدم */
.progress-bar-container {
    background: var(--bg-card);
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--bg-tertiary);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-radius: var(--border-radius-sm);
    transition: width var(--transition-normal);
    width: 0%;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 280px 1fr 350px;
    gap: 0;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* الشريط الجانبي */
.sidebar {
    background: var(--bg-card);
    border-left: 1px solid var(--bg-tertiary);
    padding: var(--spacing-lg);
    overflow-y: auto;
    height: calc(100vh - 140px);
}

.sidebar-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

/* خطوات التنقل */
.steps-navigation {
    margin-bottom: var(--spacing-xl);
}

.step-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.step-item:hover {
    background: var(--bg-secondary);
}

.step-item.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--text-white);
}

.step-item.completed {
    background: var(--bg-secondary);
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.step-item.active .step-icon {
    background: rgba(255, 255, 255, 0.3);
}

.step-content h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.step-content p {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.step-status {
    margin-right: auto;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.step-item.completed .step-status {
    opacity: 1;
    color: var(--success-color);
}

/* إحصائيات التقدم */
.progress-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* منطقة المحتوى */
.content-area {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    overflow-y: auto;
    height: calc(100vh - 140px);
}

/* حاوي النماذج */
.form-container {
    max-width: 800px;
    margin: 0 auto;
}

/* محتوى الخطوات */
.step-content {
    display: none;
    animation: fadeInUp 0.5s ease-out;
}

.step-content.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* رأس الخطوة */
.step-header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.step-header h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.step-header h2 i {
    color: var(--primary-color);
}

.step-header p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* قسم النموذج */
.form-section {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
}

/* شبكة النموذج */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

/* مجموعة النموذج */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group.full-width {
    grid-column: 1 / -1;
}

/* تسميات النموذج */
label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

label.required::after {
    content: ' *';
    color: var(--error-color);
}

.section-label {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: block;
}

/* حقول الإدخال */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="url"],
textarea,
select {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--bg-tertiary);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
    direction: rtl;
}

input:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

input.valid {
    border-color: var(--success-color);
}

input.invalid {
    border-color: var(--error-color);
}

textarea {
    resize: vertical;
    min-height: 120px;
}

/* تلميحات الحقول */
.field-hint {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    margin-top: var(--spacing-xs);
}

/* رسائل الخطأ */
.error-message {
    font-size: var(--font-size-xs);
    color: var(--error-color);
    margin-top: var(--spacing-xs);
    display: none;
}

.error-message:not(:empty) {
    display: block;
}

/* عداد الأحرف */
.character-counter {
    text-align: left;
    font-size: var(--font-size-xs);
    color: var(--text-light);
    margin-top: var(--spacing-xs);
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-weight: 500;
    font-family: var(--font-family-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--text-white);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-color);
    color: var(--text-white);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    color: var(--text-white);
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

/* أزرار التنقل */
.navigation-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--bg-tertiary);
}

/* رفع الصورة */
.photo-upload-section {
    margin-bottom: var(--spacing-xl);
}

.photo-upload-area {
    border: 2px dashed var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--bg-secondary);
}

.photo-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(30, 64, 175, 0.05);
}

.photo-upload-area.drag-over {
    border-color: var(--primary-color);
    background: rgba(30, 64, 175, 0.1);
}

.photo-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.photo-preview i {
    font-size: 3rem;
    color: var(--text-light);
}

.photo-preview h4 {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    margin: 0;
}

.photo-preview p {
    color: var(--text-secondary);
    margin: 0;
}

.photo-preview small {
    color: var(--text-light);
}

/* لوحة المعاينة */
.preview-panel {
    background: var(--bg-card);
    border-right: 1px solid var(--bg-tertiary);
    padding: var(--spacing-lg);
    overflow-y: auto;
    height: calc(100vh - 140px);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--bg-tertiary);
}

.preview-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.preview-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.control-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.control-btn:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

.zoom-level {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    min-width: 40px;
    text-align: center;
}

/* اختيار القالب */
.template-selector {
    margin-bottom: var(--spacing-lg);
}

.template-selector label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.template-options {
    display: flex;
    gap: var(--spacing-sm);
}

.template-option {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    border: 2px solid var(--bg-tertiary);
    background: var(--bg-primary);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.template-option:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.template-option.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--text-white);
}

.template-option i {
    font-size: var(--font-size-lg);
}

/* منطقة المعاينة */
.preview-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    min-height: 400px;
}

.cv-preview {
    background: var(--bg-primary);
    border-radius: var(--border-radius-sm);
    min-height: 380px;
    box-shadow: var(--shadow-md);
    transform-origin: top center;
    transition: transform var(--transition-normal);
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 380px;
    color: var(--text-light);
    text-align: center;
}

.preview-placeholder i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.preview-placeholder h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
}

/* التصميم المتجاوب */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 250px 1fr 300px;
    }
}

@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .sidebar {
        height: auto;
        border-left: none;
        border-bottom: 1px solid var(--bg-tertiary);
    }
    
    .preview-panel {
        display: none;
    }
    
    .content-area {
        height: auto;
    }
}

@media (max-width: 768px) {
    .navbar-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .navbar-actions {
        width: 100%;
        justify-content: center;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .navigation-buttons {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .steps-navigation {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }
    
    .step-item {
        flex-direction: column;
        text-align: center;
        padding: var(--spacing-sm);
    }
    
    .step-content h4 {
        font-size: var(--font-size-sm);
    }
    
    .step-content p {
        display: none;
    }
}

@media (max-width: 480px) {
    .content-area {
        padding: var(--spacing-md);
    }
    
    .form-section {
        padding: var(--spacing-lg);
    }
    
    .step-header h2 {
        font-size: var(--font-size-2xl);
        flex-direction: column;
    }
}
