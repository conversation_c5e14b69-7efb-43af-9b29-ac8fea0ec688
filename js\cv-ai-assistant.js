/**
 * نظام الذكاء الاصطناعي المتقدم لمنشئ السيرة الذاتية
 * يوفر اقتراحات ذكية وتحليل جودة السيرة الذاتية
 */

class CVAIAssistant {
    constructor() {
        this.isInitialized = false;
        this.currentField = '';
        this.userProfile = {};
        this.suggestions = {};
        this.qualityScore = 0;
        this.recommendations = [];

        // قاعدة بيانات المهن والمهارات
        this.professionsDatabase = {
            'مطور ويب': {
                keywords: ['JavaScript', 'HTML', 'CSS', 'React', 'Vue', 'Angular', 'Node.js', 'PHP', 'Python'],
                skills: ['تطوير الواجهات الأمامية', 'تطوير الخلفية', 'قواعد البيانات', 'API', 'Git'],
                softSkills: ['حل المشكلات', 'العمل الجماعي', 'التعلم المستمر', 'الإبداع'],
                summaryTemplate: 'مطور ويب محترف مع {experience} سنوات من الخبرة في تطوير التطبيقات الحديثة باستخدام {technologies}. متخصص في {specialization} وشغوف بإنشاء حلول تقنية مبتكرة.'
            },
            'مصمم جرافيك': {
                keywords: ['Photoshop', 'Illustrator', 'InDesign', 'Figma', 'Sketch', 'After Effects'],
                skills: ['تصميم الهوية البصرية', 'تصميم الطباعة', 'تصميم الويب', 'الرسوم المتحركة'],
                softSkills: ['الإبداع', 'الانتباه للتفاصيل', 'التواصل البصري', 'إدارة الوقت'],
                summaryTemplate: 'مصمم جرافيك إبداعي مع {experience} سنوات من الخبرة في {specialization}. متخصص في إنشاء تصاميم مبتكرة وجذابة باستخدام {tools}.'
            },
            'مدير مشاريع': {
                keywords: ['PMP', 'Agile', 'Scrum', 'Kanban', 'JIRA', 'Trello', 'MS Project'],
                skills: ['إدارة المشاريع', 'التخطيط الاستراتيجي', 'إدارة الفرق', 'إدارة المخاطر'],
                softSkills: ['القيادة', 'التواصل', 'حل المشكلات', 'اتخاذ القرارات'],
                summaryTemplate: 'مدير مشاريع معتمد مع {experience} سنوات من الخبرة في إدارة المشاريع {projectType}. خبرة في {methodologies} وقيادة فرق متعددة التخصصات.'
            },
            'محاسب': {
                keywords: ['Excel', 'QuickBooks', 'SAP', 'Oracle', 'CPA', 'CMA', 'IFRS'],
                skills: ['المحاسبة المالية', 'التدقيق', 'التحليل المالي', 'إعداد التقارير'],
                softSkills: ['الدقة', 'التحليل', 'النزاهة', 'إدارة الوقت'],
                summaryTemplate: 'محاسب محترف مع {experience} سنوات من الخبرة في {specialization}. خبرة في {systems} وإعداد التقارير المالية.'
            },
            'مهندس': {
                keywords: ['AutoCAD', 'SolidWorks', 'MATLAB', 'Python', 'C++', 'PLC'],
                skills: ['التصميم الهندسي', 'التحليل الفني', 'إدارة المشاريع الهندسية', 'ضمان الجودة'],
                softSkills: ['التفكير التحليلي', 'حل المشكلات', 'الدقة', 'العمل الجماعي'],
                summaryTemplate: 'مهندس {specialty} مع {experience} سنوات من الخبرة في {field}. متخصص في {technologies} وتطوير حلول هندسية مبتكرة.'
            },
            'مسوق رقمي': {
                keywords: ['Google Ads', 'Facebook Ads', 'SEO', 'SEM', 'Analytics', 'Social Media'],
                skills: ['التسويق الرقمي', 'تحليل البيانات', 'إدارة الحملات', 'تحسين محركات البحث'],
                softSkills: ['الإبداع', 'التحليل', 'التواصل', 'التكيف'],
                summaryTemplate: 'مسوق رقمي محترف مع {experience} سنوات من الخبرة في {channels}. خبرة في إدارة الحملات الإعلانية وتحقيق نتائج مميزة.'
            }
        };

        // قوالب الملخص المهني
        this.summaryTemplates = {
            entry: 'محترف طموح في مجال {field} مع شغف كبير للتعلم والنمو. أسعى لتطبيق مهاراتي في {skills} والمساهمة في نجاح الفريق.',
            experienced: '{title} محترف مع {experience} سنوات من الخبرة في {field}. أتمتع بخبرة واسعة في {specialization} وأسعى لتحقيق التميز في كل مشروع.',
            senior: '{title} خبير مع أكثر من {experience} سنوات من الخبرة في قيادة {teams} وإدارة {projects}. متخصص في {expertise} مع سجل حافل من النجاحات.',
            executive: 'قائد تنفيذي مع {experience} سنوات من الخبرة في {industry}. خبرة مثبتة في {achievements} وقيادة التحول الرقمي والنمو الاستراتيجي.'
        };

        // معايير تقييم الجودة
        this.qualityMetrics = {
            completeness: { weight: 0.3, score: 0 },
            relevance: { weight: 0.25, score: 0 },
            clarity: { weight: 0.2, score: 0 },
            keywords: { weight: 0.15, score: 0 },
            formatting: { weight: 0.1, score: 0 }
        };

        this.init();
    }

    /**
     * تهيئة النظام
     */
    init() {
        console.log('🤖 تهيئة نظام الذكاء الاصطناعي...');
        this.setupEventListeners();
        this.loadUserProfile();
        this.isInitialized = true;
        console.log('✅ تم تهيئة نظام الذكاء الاصطناعي بنجاح');
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // مراقبة تغييرات الحقول
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.handleFieldChange(e.target);
            }
        });

        // مراقبة تغيير المسمى الوظيفي
        const jobTitleField = document.getElementById('jobTitle');
        if (jobTitleField) {
            jobTitleField.addEventListener('blur', () => {
                this.analyzeJobTitle(jobTitleField.value);
            });
        }

        // مراقبة تغيير الملخص المهني
        const summaryField = document.getElementById('professionalSummary');
        if (summaryField) {
            summaryField.addEventListener('input', () => {
                this.analyzeSummary(summaryField.value);
            });
        }
    }

    /**
     * تحليل المسمى الوظيفي وتقديم اقتراحات
     */
    analyzeJobTitle(jobTitle) {
        if (!jobTitle || jobTitle.length < 3) return;

        console.log('🔍 تحليل المسمى الوظيفي:', jobTitle);

        // البحث عن مطابقات في قاعدة البيانات
        const matchedProfession = this.findMatchingProfession(jobTitle);

        if (matchedProfession) {
            this.userProfile.profession = matchedProfession;
            this.showJobTitleSuggestions(matchedProfession);
            this.suggestSkills(matchedProfession);
            this.generateSummaryTemplate(matchedProfession);
        }

        // تحديث تقييم الجودة
        this.updateQualityScore();
    }

    /**
     * البحث عن المهنة المطابقة
     */
    findMatchingProfession(jobTitle) {
        const normalizedTitle = jobTitle.toLowerCase().trim();

        for (const [profession, data] of Object.entries(this.professionsDatabase)) {
            const normalizedProfession = profession.toLowerCase();

            // مطابقة مباشرة
            if (normalizedTitle.includes(normalizedProfession) ||
                normalizedProfession.includes(normalizedTitle)) {
                return { name: profession, ...data };
            }

            // مطابقة الكلمات المفتاحية
            const titleWords = normalizedTitle.split(' ');
            const professionWords = normalizedProfession.split(' ');

            const matchCount = titleWords.filter(word =>
                professionWords.some(pWord => pWord.includes(word) || word.includes(pWord))
            ).length;

            if (matchCount > 0) {
                return { name: profession, ...data };
            }
        }

        return null;
    }

    /**
     * عرض اقتراحات المسمى الوظيفي
     */
    showJobTitleSuggestions(profession) {
        const suggestions = [
            profession.name,
            `${profession.name} أول`,
            `${profession.name} محترف`,
            `${profession.name} خبير`,
            `مدير ${profession.name}`
        ];

        this.displaySuggestions('jobTitle', suggestions, 'اقتراحات للمسمى الوظيفي');
    }

    /**
     * اقتراح المهارات بناءً على المهنة
     */
    suggestSkills(profession) {
        // اقتراح المهارات التقنية
        this.displaySkillSuggestions('technical', profession.skills.concat(profession.keywords));

        // اقتراح المهارات الشخصية
        this.displaySkillSuggestions('soft', profession.softSkills);
    }

    /**
     * عرض اقتراحات المهارات
     */
    displaySkillSuggestions(type, skills) {
        const containerId = type === 'technical' ? 'technicalSkillsTags' : 'softSkillsTags';
        const container = document.getElementById(containerId);

        if (!container) return;

        // إنشاء حاوي الاقتراحات
        let suggestionsContainer = container.parentElement.querySelector('.ai-suggestions');
        if (!suggestionsContainer) {
            suggestionsContainer = document.createElement('div');
            suggestionsContainer.className = 'ai-suggestions';
            suggestionsContainer.innerHTML = `
                <div class="ai-suggestions-header">
                    <i class="fas fa-robot"></i>
                    <span>اقتراحات ذكية</span>
                </div>
                <div class="ai-suggestions-content"></div>
            `;
            container.parentElement.appendChild(suggestionsContainer);
        }

        const content = suggestionsContainer.querySelector('.ai-suggestions-content');
        content.innerHTML = skills.map(skill => `
            <button type="button" class="ai-suggestion-btn" onclick="cvAI.addSuggestedSkill('${type}', '${skill}')">
                <i class="fas fa-plus"></i>
                ${skill}
            </button>
        `).join('');
    }

    /**
     * إضافة مهارة مقترحة
     */
    addSuggestedSkill(type, skill) {
        if (type === 'technical') {
            const input = document.getElementById('technicalSkillInput');
            if (input) {
                input.value = skill;
                if (window.cvBuilder && window.cvBuilder.addTechnicalSkill) {
                    window.cvBuilder.addTechnicalSkill();
                }
            }
        } else {
            const input = document.getElementById('softSkillInput');
            if (input) {
                input.value = skill;
                if (window.cvBuilder && window.cvBuilder.addSoftSkill) {
                    window.cvBuilder.addSoftSkill();
                }
            }
        }
    }

    /**
     * توليد قالب الملخص المهني
     */
    generateSummaryTemplate(profession) {
        const summaryField = document.getElementById('professionalSummary');
        if (!summaryField || summaryField.value.trim()) return;

        const template = profession.summaryTemplate;
        const suggestions = [
            template.replace('{experience}', '3-5')
                   .replace('{technologies}', profession.keywords.slice(0, 3).join(' و'))
                   .replace('{specialization}', profession.skills[0]),

            template.replace('{experience}', '5+')
                   .replace('{technologies}', profession.keywords.slice(0, 4).join(' و'))
                   .replace('{specialization}', profession.skills.slice(0, 2).join(' و')),

            template.replace('{experience}', '7+')
                   .replace('{technologies}', profession.keywords.slice(0, 5).join(' و'))
                   .replace('{specialization}', profession.skills.slice(0, 3).join(' و'))
        ];

        this.displaySuggestions('professionalSummary', suggestions, 'اقتراحات للملخص المهني');
    }

    /**
     * تحليل الملخص المهني
     */
    analyzeSummary(summary) {
        if (!summary || summary.length < 10) return;

        const analysis = {
            length: summary.length,
            wordCount: summary.split(' ').length,
            keywordDensity: this.calculateKeywordDensity(summary),
            readability: this.calculateReadability(summary),
            suggestions: []
        };

        // تحليل الطول
        if (analysis.length < 100) {
            analysis.suggestions.push('الملخص قصير جداً. يُنصح بكتابة 100-300 حرف');
        } else if (analysis.length > 500) {
            analysis.suggestions.push('الملخص طويل جداً. يُنصح بتقليله إلى 300-500 حرف');
        }

        // تحليل الكلمات المفتاحية
        if (analysis.keywordDensity < 0.1) {
            analysis.suggestions.push('أضف المزيد من الكلمات المفتاحية المتعلقة بمجالك');
        }

        // عرض التحليل
        this.displaySummaryAnalysis(analysis);
    }

    /**
     * حساب كثافة الكلمات المفتاحية
     */
    calculateKeywordDensity(text) {
        if (!this.userProfile.profession) return 0;

        const keywords = this.userProfile.profession.keywords;
        const textLower = text.toLowerCase();
        let keywordCount = 0;
        let totalWords = text.split(' ').length;

        keywords.forEach(keyword => {
            if (textLower.includes(keyword.toLowerCase())) {
                keywordCount++;
            }
        });

        return keywordCount / totalWords;
    }

    /**
     * حساب قابلية القراءة
     */
    calculateReadability(text) {
        const sentences = text.split(/[.!?]+/).length - 1;
        const words = text.split(' ').length;
        const avgWordsPerSentence = words / Math.max(sentences, 1);

        // تقييم بسيط للقابلية
        if (avgWordsPerSentence <= 15) return 'ممتاز';
        if (avgWordsPerSentence <= 20) return 'جيد';
        if (avgWordsPerSentence <= 25) return 'متوسط';
        return 'صعب';
    }

    /**
     * عرض تحليل الملخص المهني
     */
    displaySummaryAnalysis(analysis) {
        const summaryField = document.getElementById('professionalSummary');
        if (!summaryField) return;

        // إزالة التحليل السابق
        const existingAnalysis = summaryField.parentElement.querySelector('.ai-analysis');
        if (existingAnalysis) {
            existingAnalysis.remove();
        }

        // إنشاء حاوي التحليل
        const analysisContainer = document.createElement('div');
        analysisContainer.className = 'ai-analysis';
        analysisContainer.innerHTML = `
            <div class="ai-analysis-header">
                <i class="fas fa-chart-line"></i>
                <span>تحليل الملخص المهني</span>
            </div>
            <div class="ai-analysis-content">
                <div class="analysis-metrics">
                    <div class="metric">
                        <span class="metric-label">عدد الأحرف:</span>
                        <span class="metric-value ${analysis.length >= 100 && analysis.length <= 500 ? 'good' : 'warning'}">${analysis.length}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">عدد الكلمات:</span>
                        <span class="metric-value">${analysis.wordCount}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">قابلية القراءة:</span>
                        <span class="metric-value ${analysis.readability === 'ممتاز' || analysis.readability === 'جيد' ? 'good' : 'warning'}">${analysis.readability}</span>
                    </div>
                </div>
                ${analysis.suggestions.length > 0 ? `
                    <div class="analysis-suggestions">
                        <h5>اقتراحات للتحسين:</h5>
                        <ul>
                            ${analysis.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;

        summaryField.parentElement.appendChild(analysisContainer);
    }

    /**
     * تحليل جودة السيرة الذاتية الشاملة
     */
    analyzeOverallQuality() {
        const cvData = this.getCurrentCVData();
        if (!cvData) return;

        // تحليل الاكتمال
        this.qualityMetrics.completeness.score = this.calculateCompletenessScore(cvData);

        // تحليل الصلة
        this.qualityMetrics.relevance.score = this.calculateRelevanceScore(cvData);

        // تحليل الوضوح
        this.qualityMetrics.clarity.score = this.calculateClarityScore(cvData);

        // تحليل الكلمات المفتاحية
        this.qualityMetrics.keywords.score = this.calculateKeywordsScore(cvData);

        // تحليل التنسيق
        this.qualityMetrics.formatting.score = this.calculateFormattingScore(cvData);

        // حساب النتيجة الإجمالية
        this.qualityScore = Object.values(this.qualityMetrics).reduce((total, metric) => {
            return total + (metric.score * metric.weight);
        }, 0);

        // توليد التوصيات
        this.generateRecommendations();

        // عرض النتائج
        this.displayQualityAnalysis();
    }

    /**
     * حساب نقاط الاكتمال
     */
    calculateCompletenessScore(cvData) {
        const requiredFields = [
            'personal.fullName', 'personal.email', 'personal.phone', 'personal.jobTitle',
            'professionalSummary', 'experiences', 'education', 'technicalSkills'
        ];

        let completedFields = 0;

        requiredFields.forEach(field => {
            const fieldValue = this.getNestedValue(cvData, field);
            if (fieldValue && (Array.isArray(fieldValue) ? fieldValue.length > 0 : fieldValue.trim())) {
                completedFields++;
            }
        });

        return (completedFields / requiredFields.length) * 100;
    }

    /**
     * حساب نقاط الصلة
     */
    calculateRelevanceScore(cvData) {
        if (!this.userProfile.profession) return 50;

        let relevanceScore = 0;
        const profession = this.userProfile.profession;

        // فحص المهارات التقنية
        if (cvData.technicalSkills) {
            const matchingSkills = cvData.technicalSkills.filter(skill =>
                profession.keywords.some(keyword =>
                    skill.toLowerCase().includes(keyword.toLowerCase()) ||
                    keyword.toLowerCase().includes(skill.toLowerCase())
                )
            );
            relevanceScore += (matchingSkills.length / profession.keywords.length) * 40;
        }

        // فحص الملخص المهني
        if (cvData.professionalSummary) {
            const summaryKeywords = profession.keywords.filter(keyword =>
                cvData.professionalSummary.toLowerCase().includes(keyword.toLowerCase())
            );
            relevanceScore += (summaryKeywords.length / profession.keywords.length) * 30;
        }

        // فحص الخبرات
        if (cvData.experiences && cvData.experiences.length > 0) {
            relevanceScore += 30;
        }

        return Math.min(relevanceScore, 100);
    }

    /**
     * حساب نقاط الوضوح
     */
    calculateClarityScore(cvData) {
        let clarityScore = 100;

        // فحص الملخص المهني
        if (cvData.professionalSummary) {
            const readability = this.calculateReadability(cvData.professionalSummary);
            if (readability === 'صعب') clarityScore -= 20;
            else if (readability === 'متوسط') clarityScore -= 10;
        }

        // فحص أوصاف الخبرات
        if (cvData.experiences) {
            cvData.experiences.forEach(exp => {
                if (exp.description && exp.description.length > 500) {
                    clarityScore -= 5;
                }
            });
        }

        return Math.max(clarityScore, 0);
    }

    /**
     * حساب نقاط الكلمات المفتاحية
     */
    calculateKeywordsScore(cvData) {
        if (!this.userProfile.profession) return 50;

        const profession = this.userProfile.profession;
        let keywordScore = 0;
        let totalText = '';

        // جمع النصوص
        if (cvData.professionalSummary) totalText += cvData.professionalSummary + ' ';
        if (cvData.experiences) {
            cvData.experiences.forEach(exp => {
                if (exp.description) totalText += exp.description + ' ';
            });
        }

        // حساب الكلمات المفتاحية الموجودة
        const foundKeywords = profession.keywords.filter(keyword =>
            totalText.toLowerCase().includes(keyword.toLowerCase())
        );

        keywordScore = (foundKeywords.length / profession.keywords.length) * 100;
        return keywordScore;
    }

    /**
     * حساب نقاط التنسيق
     */
    calculateFormattingScore(cvData) {
        let formattingScore = 100;

        // فحص التواريخ
        if (cvData.experiences) {
            cvData.experiences.forEach(exp => {
                if (!exp.startDate) formattingScore -= 10;
                if (!exp.current && !exp.endDate) formattingScore -= 10;
            });
        }

        // فحص المعلومات الأساسية
        if (!cvData.personal.email || !this.isValidEmail(cvData.personal.email)) {
            formattingScore -= 15;
        }

        if (!cvData.personal.phone || !this.isValidPhone(cvData.personal.phone)) {
            formattingScore -= 10;
        }

        return Math.max(formattingScore, 0);
    }

    /**
     * توليد التوصيات
     */
    generateRecommendations() {
        this.recommendations = [];

        // توصيات الاكتمال
        if (this.qualityMetrics.completeness.score < 80) {
            this.recommendations.push({
                type: 'completeness',
                priority: 'high',
                title: 'أكمل المعلومات المفقودة',
                description: 'هناك حقول مهمة لم يتم ملؤها بعد',
                action: 'املأ جميع الحقول المطلوبة'
            });
        }

        // توصيات الصلة
        if (this.qualityMetrics.relevance.score < 70) {
            this.recommendations.push({
                type: 'relevance',
                priority: 'high',
                title: 'أضف مهارات متعلقة بمجالك',
                description: 'المهارات المذكورة لا تتطابق بشكل كافٍ مع متطلبات مجالك',
                action: 'أضف المزيد من المهارات التقنية المطلوبة'
            });
        }

        // توصيات الوضوح
        if (this.qualityMetrics.clarity.score < 80) {
            this.recommendations.push({
                type: 'clarity',
                priority: 'medium',
                title: 'حسّن وضوح النصوص',
                description: 'بعض النصوص طويلة أو معقدة',
                action: 'اجعل الجمل أقصر وأوضح'
            });
        }

        // توصيات الكلمات المفتاحية
        if (this.qualityMetrics.keywords.score < 60) {
            this.recommendations.push({
                type: 'keywords',
                priority: 'medium',
                title: 'أضف كلمات مفتاحية',
                description: 'السيرة الذاتية تفتقر للكلمات المفتاحية المهمة',
                action: 'استخدم مصطلحات تقنية متعلقة بمجالك'
            });
        }

        // توصيات التنسيق
        if (this.qualityMetrics.formatting.score < 90) {
            this.recommendations.push({
                type: 'formatting',
                priority: 'low',
                title: 'تحسين التنسيق',
                description: 'هناك مشاكل في تنسيق التواريخ أو المعلومات',
                action: 'راجع صحة التواريخ والمعلومات'
            });
        }
    }

    /**
     * عرض تحليل الجودة
     */
    displayQualityAnalysis() {
        // إنشاء أو تحديث لوحة تحليل الجودة
        let qualityPanel = document.getElementById('aiQualityPanel');
        if (!qualityPanel) {
            qualityPanel = document.createElement('div');
            qualityPanel.id = 'aiQualityPanel';
            qualityPanel.className = 'ai-quality-panel';

            // إضافة اللوحة إلى الشريط الجانبي
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.appendChild(qualityPanel);
            }
        }

        const scoreColor = this.qualityScore >= 80 ? 'excellent' :
                          this.qualityScore >= 60 ? 'good' :
                          this.qualityScore >= 40 ? 'fair' : 'poor';

        qualityPanel.innerHTML = `
            <div class="quality-header">
                <h3><i class="fas fa-chart-line"></i> تحليل جودة السيرة الذاتية</h3>
            </div>

            <div class="quality-score">
                <div class="score-circle ${scoreColor}">
                    <span class="score-number">${Math.round(this.qualityScore)}</span>
                    <span class="score-label">%</span>
                </div>
                <div class="score-description">
                    ${this.getScoreDescription(this.qualityScore)}
                </div>
            </div>

            <div class="quality-metrics">
                ${Object.entries(this.qualityMetrics).map(([key, metric]) => `
                    <div class="metric-item">
                        <div class="metric-header">
                            <span class="metric-name">${this.getMetricName(key)}</span>
                            <span class="metric-score">${Math.round(metric.score)}%</span>
                        </div>
                        <div class="metric-bar">
                            <div class="metric-fill" style="width: ${metric.score}%"></div>
                        </div>
                    </div>
                `).join('')}
            </div>

            ${this.recommendations.length > 0 ? `
                <div class="quality-recommendations">
                    <h4><i class="fas fa-lightbulb"></i> توصيات للتحسين</h4>
                    ${this.recommendations.map(rec => `
                        <div class="recommendation-item ${rec.priority}">
                            <div class="recommendation-header">
                                <i class="fas fa-${this.getRecommendationIcon(rec.type)}"></i>
                                <span class="recommendation-title">${rec.title}</span>
                            </div>
                            <p class="recommendation-description">${rec.description}</p>
                            <div class="recommendation-action">${rec.action}</div>
                        </div>
                    `).join('')}
                </div>
            ` : ''}

            <div class="quality-actions">
                <button class="btn btn-primary btn-sm" onclick="cvAI.analyzeOverallQuality()">
                    <i class="fas fa-refresh"></i>
                    إعادة التحليل
                </button>
                <button class="btn btn-outline btn-sm" onclick="cvAI.exportQualityReport()">
                    <i class="fas fa-download"></i>
                    تصدير التقرير
                </button>
            </div>
        `;
    }

    /**
     * الحصول على وصف النتيجة
     */
    getScoreDescription(score) {
        if (score >= 90) return 'ممتاز! سيرة ذاتية عالية الجودة';
        if (score >= 80) return 'جيد جداً! تحتاج لتحسينات طفيفة';
        if (score >= 70) return 'جيد! يمكن تحسينها أكثر';
        if (score >= 60) return 'مقبول! تحتاج لتحسينات مهمة';
        if (score >= 40) return 'ضعيف! تحتاج لتحسينات كبيرة';
        return 'ضعيف جداً! ابدأ من جديد';
    }

    /**
     * الحصول على اسم المقياس
     */
    getMetricName(key) {
        const names = {
            completeness: 'الاكتمال',
            relevance: 'الصلة',
            clarity: 'الوضوح',
            keywords: 'الكلمات المفتاحية',
            formatting: 'التنسيق'
        };
        return names[key] || key;
    }

    /**
     * الحصول على أيقونة التوصية
     */
    getRecommendationIcon(type) {
        const icons = {
            completeness: 'check-circle',
            relevance: 'bullseye',
            clarity: 'eye',
            keywords: 'key',
            formatting: 'align-left'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * تحسين تلقائي للنص
     */
    improveText(text, type = 'general') {
        if (!text || text.length < 10) return text;

        let improvedText = text;

        // تحسينات عامة
        improvedText = this.fixCommonIssues(improvedText);

        // تحسينات خاصة بالنوع
        switch (type) {
            case 'summary':
                improvedText = this.improveSummary(improvedText);
                break;
            case 'experience':
                improvedText = this.improveExperience(improvedText);
                break;
            case 'education':
                improvedText = this.improveEducation(improvedText);
                break;
        }

        return improvedText;
    }

    /**
     * إصلاح المشاكل الشائعة
     */
    fixCommonIssues(text) {
        return text
            // إصلاح المسافات المتعددة
            .replace(/\s+/g, ' ')
            // إصلاح علامات الترقيم
            .replace(/\s+([.,!?;:])/g, '$1')
            .replace(/([.,!?;:])\s*/g, '$1 ')
            // إصلاح الأحرف الكبيرة في بداية الجمل
            .replace(/(^|\. )([a-z])/g, (match, p1, p2) => p1 + p2.toUpperCase())
            .trim();
    }

    /**
     * تحسين الملخص المهني
     */
    improveSummary(text) {
        // إضافة كلمات انتقالية
        if (!text.includes('متخصص') && !text.includes('خبرة')) {
            text = text.replace(/\.$/, '. متخصص في تقديم حلول مبتكرة وفعالة.');
        }

        // تحسين البداية
        if (!text.match(/^(مطور|مصمم|مهندس|محاسب|مدير|مسوق)/)) {
            const profession = this.userProfile.profession?.name || 'محترف';
            text = `${profession} ${text}`;
        }

        return text;
    }

    /**
     * تحسين وصف الخبرة
     */
    improveExperience(text) {
        // إضافة أفعال قوية
        const strongVerbs = ['طور', 'أنشأ', 'قاد', 'حسّن', 'نفذ', 'أدار', 'صمم'];

        if (!strongVerbs.some(verb => text.includes(verb))) {
            text = `طور وتنفيذ ${text}`;
        }

        // إضافة أرقام إذا لم تكن موجودة
        if (!/\d/.test(text)) {
            text += '. حقق تحسينات ملحوظة في الأداء والكفاءة.';
        }

        return text;
    }

    /**
     * وظائف مساعدة
     */
    getCurrentCVData() {
        if (window.cvBuilder && window.cvBuilder.cvData) {
            return window.cvBuilder.cvData;
        }
        return null;
    }

    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }

    isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    isValidPhone(phone) {
        return /^[\+]?[0-9\s\-\(\)]{10,}$/.test(phone);
    }

    handleFieldChange(field) {
        // تحديث تقييم الجودة عند تغيير الحقول
        clearTimeout(this.updateTimeout);
        this.updateTimeout = setTimeout(() => {
            this.updateQualityScore();
        }, 1000);
    }

    updateQualityScore() {
        if (this.isInitialized) {
            this.analyzeOverallQuality();
        }
    }

    loadUserProfile() {
        const saved = localStorage.getItem('cvAI_userProfile');
        if (saved) {
            try {
                this.userProfile = JSON.parse(saved);
            } catch (e) {
                console.warn('فشل في تحميل ملف المستخدم المحفوظ');
            }
        }
    }

    saveUserProfile() {
        localStorage.setItem('cvAI_userProfile', JSON.stringify(this.userProfile));
    }

    exportQualityReport() {
        const report = {
            timestamp: new Date().toISOString(),
            overallScore: this.qualityScore,
            metrics: this.qualityMetrics,
            recommendations: this.recommendations
        };

        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `cv-quality-report-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// تهيئة النظام عند تحميل الصفحة
let cvAI;
document.addEventListener('DOMContentLoaded', () => {
    cvAI = new CVAIAssistant();
    window.cvAI = cvAI; // جعل النظام متاحاً عالمياً
});