<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elashrafy CV - منشئ السيرة الذاتية الذكي</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Google Fonts for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Meta <PERSON>s -->
    <meta name="description" content="منشئ السيرة الذاتية الذكي - أنشئ سيرتك الذاتية بسهولة مع 30 قالب احترافي وذكاء اصطناعي">
    <meta name="keywords" content="سيرة ذاتية، CV، منشئ السيرة الذاتية، قوالب احترافية، ذكاء اصطناعي">
    <meta name="author" content="Elashrafy CV">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
    <link rel="alternate icon" type="image/x-icon" href="assets/favicon.ico">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#1e40af">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Elashrafy CV">
    <link rel="apple-touch-icon" href="assets/favicon.svg">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="logo-animation">
                <i class="fas fa-file-alt"></i>
            </div>
            <h2>Elashrafy CV</h2>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <p>جاري تحميل منشئ السيرة الذاتية الذكي...</p>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-file-alt"></i>
                <span>Elashrafy CV</span>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <a href="#home" class="nav-link active">الرئيسية</a>
                <a href="#features" class="nav-link">الميزات</a>
                <a href="#templates" class="nav-link">القوالب</a>
                <a href="#pricing" class="nav-link">الأسعار</a>
                <a href="#contact" class="nav-link">اتصل بنا</a>
            </div>
            
            <div class="nav-actions">
                <button class="btn btn-outline" onclick="showLogin()">تسجيل الدخول</button>
                <button class="btn btn-primary" onclick="startBuilding()">ابدأ الآن</button>
                <button class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
            </div>
        </div>
        
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        أنشئ سيرتك الذاتية
                        <span class="gradient-text">بالذكاء الاصطناعي</span>
                    </h1>
                    <p class="hero-description">
                        منشئ السيرة الذاتية الأكثر تطوراً في العالم العربي. 
                        30+ قالب احترافي، ذكاء اصطناعي متقدم، وتصدير بصيغ متعددة
                    </p>
                    
                    <div class="hero-features">
                        <div class="feature-item">
                            <i class="fas fa-robot"></i>
                            <span>مساعد ذكي</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-palette"></i>
                            <span>30+ قالب</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-download"></i>
                            <span>تصدير متعدد</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-qrcode"></i>
                            <span>QR & NFC</span>
                        </div>
                    </div>
                    
                    <div class="hero-actions">
                        <button class="btn btn-primary btn-large" onclick="startBuilding()">
                            <i class="fas fa-plus"></i>
                            ابدأ إنشاء سيرتك الذاتية
                        </button>
                        <button class="btn btn-outline btn-large" onclick="viewTemplates()">
                            <i class="fas fa-eye"></i>
                            استعرض القوالب
                        </button>
                    </div>
                </div>
                
                <div class="hero-visual">
                    <div class="cv-preview-container">
                        <div class="cv-preview">
                            <div class="cv-header">
                                <div class="cv-avatar"></div>
                                <div class="cv-info">
                                    <div class="cv-name"></div>
                                    <div class="cv-title"></div>
                                </div>
                            </div>
                            <div class="cv-sections">
                                <div class="cv-section"></div>
                                <div class="cv-section"></div>
                                <div class="cv-section"></div>
                            </div>
                        </div>
                        
                        <div class="floating-elements">
                            <div class="floating-icon" style="--delay: 0s">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="floating-icon" style="--delay: 1s">
                                <i class="fas fa-award"></i>
                            </div>
                            <div class="floating-icon" style="--delay: 2s">
                                <i class="fas fa-briefcase"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="scroll-indicator">
            <div class="scroll-arrow">
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">لماذا Elashrafy CV؟</h2>
                <p class="section-description">
                    نقدم لك أحدث التقنيات لإنشاء سيرة ذاتية احترافية تميزك عن المنافسين
                </p>
            </div>

            <div class="features-grid">
                <div class="feature-card" data-animate="fade" data-direction="up" data-delay="100">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>مساعد ذكي متقدم</h3>
                    <p>ذكاء اصطناعي يساعدك في كتابة وتحسين محتوى سيرتك الذاتية بشكل احترافي</p>
                    <ul class="feature-list">
                        <li>اقتراحات تلقائية للمهارات</li>
                        <li>تحسين الصياغة والأسلوب</li>
                        <li>إنشاء ملخص مهني</li>
                    </ul>
                </div>

                <div class="feature-card" data-animate="fade" data-direction="up" data-delay="200">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>30+ قالب احترافي</h3>
                    <p>مجموعة متنوعة من القوالب المصممة بعناية لتناسب جميع المجالات المهنية</p>
                    <ul class="feature-list">
                        <li>تصاميم عصرية وكلاسيكية</li>
                        <li>قابلة للتخصيص بالكامل</li>
                        <li>متوافقة مع جميع الأجهزة</li>
                    </ul>
                </div>

                <div class="feature-card" data-animate="fade" data-direction="up" data-delay="300">
                    <div class="feature-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <h3>تصدير متعدد الصيغ</h3>
                    <p>احفظ وشارك سيرتك الذاتية بصيغ متعددة تناسب احتياجاتك</p>
                    <ul class="feature-list">
                        <li>PDF عالي الجودة</li>
                        <li>صور PNG/JPG</li>
                        <li>رابط ويب تفاعلي</li>
                    </ul>
                </div>

                <div class="feature-card" data-animate="fade" data-direction="up" data-delay="400">
                    <div class="feature-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <h3>QR & NFC</h3>
                    <p>تقنيات متقدمة للمشاركة السريعة والذكية لسيرتك الذاتية</p>
                    <ul class="feature-list">
                        <li>رموز QR قابلة للتخصيص</li>
                        <li>دعم تقنية NFC</li>
                        <li>باركود للمشاركة</li>
                    </ul>
                </div>

                <div class="feature-card" data-animate="fade" data-direction="up" data-delay="500">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>تصميم متجاوب</h3>
                    <p>سيرتك الذاتية تبدو مثالية على جميع الأجهزة والشاشات</p>
                    <ul class="feature-list">
                        <li>متوافق مع الهواتف</li>
                        <li>محسن للأجهزة اللوحية</li>
                        <li>مثالي لأجهزة الكمبيوتر</li>
                    </ul>
                </div>

                <div class="feature-card" data-animate="fade" data-direction="up" data-delay="600">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>أمان وخصوصية</h3>
                    <p>بياناتك محمية بأعلى معايير الأمان والخصوصية</p>
                    <ul class="feature-list">
                        <li>تشفير متقدم للبيانات</li>
                        <li>نسخ احتياطية آمنة</li>
                        <li>خصوصية كاملة</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Templates Preview Section -->
    <section id="templates" class="templates-preview">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">قوالب احترافية متنوعة</h2>
                <p class="section-description">
                    اختر من بين أكثر من 30 قالب مصمم بعناية ليناسب مجالك المهني
                </p>
            </div>

            <div class="template-categories">
                <button class="category-btn active" data-category="all">جميع القوالب</button>
                <button class="category-btn" data-category="modern">عصري</button>
                <button class="category-btn" data-category="classic">كلاسيكي</button>
                <button class="category-btn" data-category="creative">إبداعي</button>
                <button class="category-btn" data-category="minimal">بسيط</button>
            </div>

            <div class="templates-grid" id="templates-grid">
                <div class="template-card" data-category="modern" data-animate="fade" data-direction="up">
                    <div class="template-preview">
                        <div class="template-image">
                            <div class="template-placeholder">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="template-overlay">
                            <button class="btn btn-primary" onclick="previewTemplate(1)">
                                <i class="fas fa-eye"></i>
                                معاينة
                            </button>
                            <button class="btn btn-outline" onclick="useTemplate(1)">
                                <i class="fas fa-plus"></i>
                                استخدام
                            </button>
                        </div>
                    </div>
                    <div class="template-info">
                        <h4>القالب العصري</h4>
                        <p>مثالي للمجالات التقنية والإبداعية</p>
                        <div class="template-tags">
                            <span class="tag">عصري</span>
                            <span class="tag">تقني</span>
                        </div>
                    </div>
                </div>

                <div class="template-card" data-category="classic" data-animate="fade" data-direction="up" data-delay="100">
                    <div class="template-preview">
                        <div class="template-image">
                            <div class="template-placeholder">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="template-overlay">
                            <button class="btn btn-primary" onclick="previewTemplate(2)">
                                <i class="fas fa-eye"></i>
                                معاينة
                            </button>
                            <button class="btn btn-outline" onclick="useTemplate(2)">
                                <i class="fas fa-plus"></i>
                                استخدام
                            </button>
                        </div>
                    </div>
                    <div class="template-info">
                        <h4>القالب الكلاسيكي</h4>
                        <p>أنيق ومناسب للمجالات الرسمية</p>
                        <div class="template-tags">
                            <span class="tag">كلاسيكي</span>
                            <span class="tag">رسمي</span>
                        </div>
                    </div>
                </div>

                <div class="template-card" data-category="creative" data-animate="fade" data-direction="up" data-delay="200">
                    <div class="template-preview">
                        <div class="template-image">
                            <div class="template-placeholder">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="template-overlay">
                            <button class="btn btn-primary" onclick="previewTemplate(3)">
                                <i class="fas fa-eye"></i>
                                معاينة
                            </button>
                            <button class="btn btn-outline" onclick="useTemplate(3)">
                                <i class="fas fa-plus"></i>
                                استخدام
                            </button>
                        </div>
                    </div>
                    <div class="template-info">
                        <h4>القالب الإبداعي</h4>
                        <p>مميز للمصممين والفنانين</p>
                        <div class="template-tags">
                            <span class="tag">إبداعي</span>
                            <span class="tag">فني</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="templates-actions">
                <button class="btn btn-outline btn-large" onclick="viewAllTemplates()">
                    <i class="fas fa-th"></i>
                    عرض جميع القوالب
                </button>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">خطط تناسب احتياجاتك</h2>
                <p class="section-description">
                    اختر الخطة المناسبة لك واحصل على أفضل الميزات لإنشاء سيرة ذاتية مميزة
                </p>
            </div>

            <div class="pricing-grid">
                <div class="pricing-card" data-animate="fade" data-direction="up" data-delay="100">
                    <div class="pricing-header">
                        <h3>مجاني</h3>
                        <div class="price">
                            <span class="currency">ر.س</span>
                            <span class="amount">0</span>
                            <span class="period">/شهر</span>
                        </div>
                        <p>مثالي للمبتدئين</p>
                    </div>
                    <div class="pricing-features">
                        <ul>
                            <li><i class="fas fa-check"></i> 3 قوالب أساسية</li>
                            <li><i class="fas fa-check"></i> تصدير PDF</li>
                            <li><i class="fas fa-check"></i> مساعد ذكي محدود</li>
                            <li><i class="fas fa-times"></i> رموز QR</li>
                            <li><i class="fas fa-times"></i> دعم NFC</li>
                        </ul>
                    </div>
                    <button class="btn btn-outline btn-large">ابدأ مجاناً</button>
                </div>

                <div class="pricing-card featured" data-animate="fade" data-direction="up" data-delay="200">
                    <div class="featured-badge">الأكثر شعبية</div>
                    <div class="pricing-header">
                        <h3>احترافي</h3>
                        <div class="price">
                            <span class="currency">ر.س</span>
                            <span class="amount">29</span>
                            <span class="period">/شهر</span>
                        </div>
                        <p>للمحترفين والباحثين عن عمل</p>
                    </div>
                    <div class="pricing-features">
                        <ul>
                            <li><i class="fas fa-check"></i> جميع القوالب (30+)</li>
                            <li><i class="fas fa-check"></i> تصدير متعدد الصيغ</li>
                            <li><i class="fas fa-check"></i> مساعد ذكي متقدم</li>
                            <li><i class="fas fa-check"></i> رموز QR مخصصة</li>
                            <li><i class="fas fa-check"></i> دعم NFC</li>
                            <li><i class="fas fa-check"></i> تحليلات متقدمة</li>
                        </ul>
                    </div>
                    <button class="btn btn-primary btn-large">اشترك الآن</button>
                </div>

                <div class="pricing-card" data-animate="fade" data-direction="up" data-delay="300">
                    <div class="pricing-header">
                        <h3>مؤسسي</h3>
                        <div class="price">
                            <span class="currency">ر.س</span>
                            <span class="amount">99</span>
                            <span class="period">/شهر</span>
                        </div>
                        <p>للشركات والمؤسسات</p>
                    </div>
                    <div class="pricing-features">
                        <ul>
                            <li><i class="fas fa-check"></i> جميع ميزات الاحترافي</li>
                            <li><i class="fas fa-check"></i> قوالب مخصصة</li>
                            <li><i class="fas fa-check"></i> إدارة فرق العمل</li>
                            <li><i class="fas fa-check"></i> دعم أولوية</li>
                            <li><i class="fas fa-check"></i> تدريب مخصص</li>
                            <li><i class="fas fa-check"></i> API متقدم</li>
                        </ul>
                    </div>
                    <button class="btn btn-outline btn-large">تواصل معنا</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="statistics">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item" data-animate="fade" data-direction="up">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number" data-counter="50000">0</div>
                    <div class="stat-label">مستخدم راضٍ</div>
                </div>

                <div class="stat-item" data-animate="fade" data-direction="up" data-delay="100">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-number" data-counter="100000">0</div>
                    <div class="stat-label">سيرة ذاتية تم إنشاؤها</div>
                </div>

                <div class="stat-item" data-animate="fade" data-direction="up" data-delay="200">
                    <div class="stat-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="stat-number" data-counter="25000">0</div>
                    <div class="stat-label">وظيفة تم الحصول عليها</div>
                </div>

                <div class="stat-item" data-animate="fade" data-direction="up" data-delay="300">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-number" data-counter="98">0</div>
                    <div class="stat-label">% معدل الرضا</div>
                </div>
            </div>
        </div>
    </section>

    <!-- AI Assistant Floating Button -->
    <div class="ai-floating-assistant" id="aiFloatingAssistant">
        <div class="ai-toggle-btn" onclick="toggleAIAssistant()">
            <i class="fas fa-robot"></i>
            <span class="ai-pulse"></span>
        </div>

        <div class="ai-chat-window" id="aiChatWindow" style="display: none;">
            <div class="ai-chat-header">
                <div class="ai-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="ai-info">
                    <h4>المساعد الذكي</h4>
                    <p class="ai-status">متصل ومستعد للمساعدة</p>
                </div>
                <button class="ai-close-btn" onclick="toggleAIAssistant()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="ai-chat-messages" id="aiChatMessages">
                <div class="ai-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        مرحباً! أنا المساعد الذكي لـ Elashrafy CV. يمكنني مساعدتك في:
                        <ul>
                            <li>اختيار القالب المناسب لمجالك</li>
                            <li>كتابة ملخص مهني مميز</li>
                            <li>اقتراح المهارات المطلوبة</li>
                            <li>تحسين محتوى سيرتك الذاتية</li>
                        </ul>
                        ما هو مجالك المهني؟
                    </div>
                </div>
            </div>

            <div class="ai-quick-actions">
                <button class="quick-action-btn" onclick="sendQuickMessage('مطور ويب')">
                    مطور ويب
                </button>
                <button class="quick-action-btn" onclick="sendQuickMessage('مصمم جرافيك')">
                    مصمم جرافيك
                </button>
                <button class="quick-action-btn" onclick="sendQuickMessage('مدير مشاريع')">
                    مدير مشاريع
                </button>
                <button class="quick-action-btn" onclick="sendQuickMessage('محاسب')">
                    محاسب
                </button>
            </div>

            <div class="ai-chat-input">
                <input type="text" id="aiMessageInput" placeholder="اكتب رسالتك هنا..."
                       onkeypress="handleAIKeyPress(event)">
                <button onclick="sendAIMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">تواصل معنا</h2>
                <p class="section-description">
                    نحن هنا لمساعدتك في إنشاء سيرة ذاتية مميزة. تواصل معنا لأي استفسار
                </p>
            </div>

            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item" data-animate="fade" data-direction="right">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h4>البريد الإلكتروني</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>

                    <div class="contact-item" data-animate="fade" data-direction="right" data-delay="100">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <h4>الهاتف</h4>
                            <p>+966 50 123 4567</p>
                        </div>
                    </div>

                    <div class="contact-item" data-animate="fade" data-direction="right" data-delay="200">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h4>العنوان</h4>
                            <p>الرياض، المملكة العربية السعودية</p>
                        </div>
                    </div>
                </div>

                <div class="contact-form-container" data-animate="fade" data-direction="left">
                    <form class="contact-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label>الاسم</label>
                                <input type="text" required>
                            </div>
                            <div class="form-group">
                                <label>البريد الإلكتروني</label>
                                <input type="email" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>الموضوع</label>
                            <input type="text" required>
                        </div>
                        <div class="form-group">
                            <label>الرسالة</label>
                            <textarea rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary btn-large">
                            <i class="fas fa-paper-plane"></i>
                            إرسال الرسالة
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-file-alt"></i>
                        <span>Elashrafy CV</span>
                    </div>
                    <p>منشئ السيرة الذاتية الذكي الأول في العالم العربي</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="#home">الرئيسية</a></li>
                        <li><a href="#features">الميزات</a></li>
                        <li><a href="#templates">القوالب</a></li>
                        <li><a href="#pricing">الأسعار</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>الدعم</h4>
                    <ul>
                        <li><a href="#">مركز المساعدة</a></li>
                        <li><a href="#">الأسئلة الشائعة</a></li>
                        <li><a href="#">تواصل معنا</a></li>
                        <li><a href="#">الدعم الفني</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>قانوني</h4>
                    <ul>
                        <li><a href="#">سياسة الخصوصية</a></li>
                        <li><a href="#">شروط الاستخدام</a></li>
                        <li><a href="#">سياسة الاسترداد</a></li>
                        <li><a href="#">ملفات تعريف الارتباط</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 Elashrafy CV. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="js/app.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/homepage-ai.js"></script>
    <script src="js/test-functions.js"></script>

    <!-- Template Functions -->
    <script>
        function previewTemplate(id) {
            // Show template preview modal
            console.log('Preview template:', id);
        }

        function useTemplate(id) {
            // Redirect to CV builder with selected template
            window.location.href = `cv-builder.html?template=${id}`;
        }

        function viewAllTemplates() {
            window.location.href = 'templates.html';
        }

        // Template category filtering
        document.addEventListener('DOMContentLoaded', function() {
            const categoryBtns = document.querySelectorAll('.category-btn');
            const templateCards = document.querySelectorAll('.template-card');

            categoryBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const category = this.dataset.category;

                    // Update active button
                    categoryBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // Filter templates
                    templateCards.forEach(card => {
                        if (category === 'all' || card.dataset.category === category) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>
