/**
 * منشئ السيرة الذاتية المتقدم - الملف الأساسي
 * نظام شامل لإنشاء السيرة الذاتية مع معاينة مباشرة وتصدير عالي الجودة
 */

class CVBuilder {
    constructor() {
        // إعدادات التطبيق
        this.currentStep = 1;
        this.totalSteps = 6;
        this.zoomLevel = 1;
        this.selectedTemplate = 'modern';
        this.selectedColorScheme = 'blue';
        this.isInitialized = false;

        // بيانات السيرة الذاتية
        this.cvData = {
            personal: {
                fullName: '',
                jobTitle: '',
                email: '',
                phone: '',
                city: '',
                website: '',
                linkedin: '',
                github: '',
                photo: null
            },
            professionalSummary: '',
            experiences: [],
            education: [],
            technicalSkills: [],
            softSkills: [],
            languages: [],
            customization: {
                template: 'modern',
                colorScheme: 'blue',
                includePhoto: true,
                includeQR: false,
                twoColumns: false,
                showProgress: true
            }
        };

        // مؤقتات للحفظ التلقائي والتحديث
        this.autoSaveTimer = null;
        this.updateTimer = null;

        // تهيئة التطبيق
        this.init();

        // إعداد منع إلغاء التحميل
        this.setupUnloadPrevention();
    }

    /**
     * تهيئة التطبيق الأساسية
     */
    async init() {
        try {
            console.log('🚀 بدء تهيئة منشئ السيرة الذاتية المتقدم...');

            // انتظار تحميل DOM
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // تهيئة المكونات
            await this.initializeComponents();

            // تحميل البيانات المحفوظة
            this.loadSavedData();

            // إعداد مستمعي الأحداث
            this.setupEventListeners();

            // تهيئة رفع الصور
            this.initializePhotoUpload();

            // تهيئة الحفظ التلقائي
            this.setupAutoSave();

            // تحديث المعاينة الأولية
            this.updatePreview();
            this.updateProgress();

            // تهيئة الاختصارات والتفاعلات المتقدمة
            this.initializeAdvancedShortcuts();

            this.isInitialized = true;
            console.log('✅ تم تهيئة منشئ السيرة الذاتية بنجاح');

            // إظهار رسالة ترحيب
            this.showNotification('مرحباً! ابدأ في إنشاء سيرتك الذاتية المميزة', 'success');

        } catch (error) {
            console.error('❌ خطأ في تهيئة منشئ السيرة الذاتية:', error);
            this.showNotification('حدث خطأ في تحميل التطبيق. يرجى إعادة تحميل الصفحة.', 'error');
        }
    }

    /**
     * تهيئة المكونات الأساسية
     */
    async initializeComponents() {
        // تهيئة محرك القوالب
        if (typeof CVTemplatesEngine !== 'undefined') {
            this.templatesEngine = new CVTemplatesEngine();
        }

        // تهيئة نظام التحقق
        if (typeof CVValidation !== 'undefined') {
            this.validation = new CVValidation();
        }

        // تهيئة نظام التصدير
        if (typeof CVExport !== 'undefined') {
            this.exportSystem = new CVExport();
        }

        // إضافة الخبرة الأولى والتعليم الأول
        this.addExperience();
        this.addEducation();
        this.addLanguage();
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // مستمعي أحداث النماذج
        document.addEventListener('input', (e) => {
            if (e.target.closest('.form-container')) {
                this.handleInputChange(e);
            }
        });

        document.addEventListener('change', (e) => {
            if (e.target.closest('.form-container')) {
                this.handleInputChange(e);
            }
        });

        // مستمعي أحداث التنقل بين الخطوات
        document.addEventListener('click', (e) => {
            if (e.target.matches('.step-item') || e.target.closest('.step-item')) {
                const stepItem = e.target.closest('.step-item');
                const stepNumber = parseInt(stepItem.dataset.step);
                this.goToStep(stepNumber);
            }
        });

        // مستمعي أحداث اختيار القالب
        document.addEventListener('click', (e) => {
            if (e.target.matches('.template-option') || e.target.closest('.template-option')) {
                const templateOption = e.target.closest('.template-option');
                const template = templateOption.dataset.template;
                this.selectTemplate(template);
            }
        });

        // مستمعي أحداث اختيار الألوان
        document.addEventListener('click', (e) => {
            if (e.target.matches('.color-scheme') || e.target.closest('.color-scheme')) {
                const colorScheme = e.target.closest('.color-scheme');
                const scheme = colorScheme.dataset.scheme;
                this.selectColorScheme(scheme);
            }
        });

        // مستمعي أحداث لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // مستمع حدث إغلاق النافذة للحفظ التلقائي
        window.addEventListener('beforeunload', (e) => {
            this.saveProgress();
        });

        console.log('✅ تم إعداد مستمعي الأحداث');
    }

    /**
     * معالجة تغيير المدخلات
     */
    handleInputChange(e) {
        const input = e.target;
        const fieldName = input.name || input.id;

        if (!fieldName) return;

        // التحقق من صحة الإدخال
        if (this.validation) {
            this.validation.validateField(input);
        }

        // تحديث البيانات
        this.updateDataFromInput(input);

        // تحديث المعاينة مع التأخير
        clearTimeout(this.updateTimer);
        this.updateTimer = setTimeout(() => {
            this.updatePreview();
            this.updateProgress();
        }, 300);

        // الحفظ التلقائي مع التأخير
        clearTimeout(this.autoSaveTimer);
        this.autoSaveTimer = setTimeout(() => {
            this.saveProgress();
        }, 1000);
    }

    /**
     * تحديث البيانات من المدخل
     */
    updateDataFromInput(input) {
        const fieldName = input.name || input.id;
        const value = input.value.trim();

        // تحديث البيانات الشخصية
        if (this.cvData.personal.hasOwnProperty(fieldName)) {
            this.cvData.personal[fieldName] = value;
        }

        // تحديث الملخص المهني
        if (fieldName === 'professionalSummary') {
            this.cvData.professionalSummary = value;
            this.updateCharacterCounter('summaryCounter', value, 500);
        }

        // تحديث خيارات التخصيص
        if (input.type === 'checkbox') {
            const customizationField = fieldName.replace(/([A-Z])/g, '_$1').toLowerCase();
            if (this.cvData.customization.hasOwnProperty(customizationField)) {
                this.cvData.customization[customizationField] = input.checked;
            }
        }
    }

    /**
     * تحديث عداد الأحرف
     */
    updateCharacterCounter(counterId, text, maxLength) {
        const counter = document.getElementById(counterId);
        if (counter) {
            const length = text.length;
            counter.textContent = length;

            if (length > maxLength) {
                counter.style.color = 'var(--error-color)';
            } else if (length > maxLength * 0.8) {
                counter.style.color = 'var(--warning-color)';
            } else {
                counter.style.color = 'var(--text-secondary)';
            }
        }
    }

    /**
     * الانتقال إلى الخطوة التالية
     */
    nextStep() {
        if (this.currentStep < this.totalSteps) {
            // التحقق من صحة الخطوة الحالية
            if (this.validateCurrentStep()) {
                this.goToStep(this.currentStep + 1);
            }
        }
    }

    /**
     * الانتقال إلى الخطوة السابقة
     */
    previousStep() {
        if (this.currentStep > 1) {
            this.goToStep(this.currentStep - 1);
        }
    }

    /**
     * الانتقال إلى خطوة محددة
     */
    goToStep(stepNumber) {
        if (stepNumber < 1 || stepNumber > this.totalSteps) return;

        // إخفاء الخطوة الحالية
        const currentStepElement = document.getElementById(`step${this.currentStep}`);
        if (currentStepElement) {
            currentStepElement.classList.remove('active');
        }

        // إزالة الحالة النشطة من عنصر التنقل
        const currentNavItem = document.querySelector(`.step-item[data-step="${this.currentStep}"]`);
        if (currentNavItem) {
            currentNavItem.classList.remove('active');
        }

        // تحديث الخطوة الحالية
        this.currentStep = stepNumber;

        // إظهار الخطوة الجديدة
        const newStepElement = document.getElementById(`step${this.currentStep}`);
        if (newStepElement) {
            newStepElement.classList.add('active');
        }

        // تحديث عنصر التنقل
        const newNavItem = document.querySelector(`.step-item[data-step="${this.currentStep}"]`);
        if (newNavItem) {
            newNavItem.classList.add('active');
        }

        // تحديث أزرار التنقل
        this.updateNavigationButtons();

        // تحديث شريط التقدم
        this.updateProgress();

        // التمرير إلى أعلى المحتوى
        const contentArea = document.querySelector('.content-area');
        if (contentArea) {
            contentArea.scrollTop = 0;
        }

        console.log(`📍 انتقال إلى الخطوة ${stepNumber}`);
    }

    /**
     * تحديث أزرار التنقل
     */
    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        if (prevBtn) {
            prevBtn.disabled = this.currentStep === 1;
        }

        if (nextBtn) {
            if (this.currentStep === this.totalSteps) {
                nextBtn.innerHTML = '<i class="fas fa-check"></i> إنهاء';
                nextBtn.onclick = () => this.finishCV();
            } else {
                nextBtn.innerHTML = 'التالي <i class="fas fa-arrow-left"></i>';
                nextBtn.onclick = () => this.nextStep();
            }
        }
    }

    /**
     * التحقق من صحة الخطوة الحالية
     */
    validateCurrentStep() {
        if (!this.validation) return true;

        const currentStepElement = document.getElementById(`step${this.currentStep}`);
        if (!currentStepElement) return true;

        const requiredFields = currentStepElement.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!this.validation.validateField(field)) {
                isValid = false;
            }
        });

        if (!isValid) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة بشكل صحيح', 'warning');
        }

        return isValid;
    }

    /**
     * تحديث شريط التقدم
     */
    updateProgress() {
        // حساب نسبة التقدم
        const progressPercentage = ((this.currentStep - 1) / (this.totalSteps - 1)) * 100;

        // تحديث شريط التقدم
        const progressFill = document.getElementById('progressFill');
        if (progressFill) {
            progressFill.style.width = `${progressPercentage}%`;
        }

        // تحديث نص التقدم
        const progressText = document.getElementById('progressText');
        if (progressText) {
            progressText.textContent = `${Math.round(progressPercentage)}% مكتمل`;
        }

        const progressStep = document.getElementById('progressStep');
        if (progressStep) {
            progressStep.textContent = `الخطوة ${this.currentStep} من ${this.totalSteps}`;
        }

        // تحديث إحصائيات الحقول المكتملة
        this.updateFieldsStats();
    }

    /**
     * تحديث إحصائيات الحقول
     */
    updateFieldsStats() {
        const completedFields = this.getCompletedFieldsCount();
        const totalFields = this.getTotalFieldsCount();

        const completedFieldsElement = document.getElementById('completedFields');
        const totalFieldsElement = document.getElementById('totalFields');

        if (completedFieldsElement) {
            completedFieldsElement.textContent = completedFields;
        }

        if (totalFieldsElement) {
            totalFieldsElement.textContent = totalFields;
        }
    }

    /**
     * حساب عدد الحقول المكتملة
     */
    getCompletedFieldsCount() {
        let count = 0;

        // الحقول الشخصية
        Object.values(this.cvData.personal).forEach(value => {
            if (value && value.toString().trim()) count++;
        });

        // الملخص المهني
        if (this.cvData.professionalSummary.trim()) count++;

        // الخبرات
        count += this.cvData.experiences.filter(exp =>
            exp.jobTitle && exp.company
        ).length;

        // التعليم
        count += this.cvData.education.filter(edu =>
            edu.degree && edu.institution
        ).length;

        // المهارات
        count += this.cvData.technicalSkills.length;
        count += this.cvData.softSkills.length;

        // اللغات
        count += this.cvData.languages.filter(lang =>
            lang.name && lang.level
        ).length;

        return count;
    }

    /**
     * حساب إجمالي عدد الحقول
     */
    getTotalFieldsCount() {
        return 25; // عدد تقديري للحقول الأساسية
    }

    /**
     * تحديث المعاينة
     */
    updatePreview() {
        if (this.templatesEngine) {
            this.templatesEngine.renderPreview(this.cvData, this.selectedTemplate);
        }
    }

    /**
     * اختيار القالب
     */
    selectTemplate(template) {
        this.selectedTemplate = template;
        this.cvData.customization.template = template;

        // تحديث الأزرار النشطة
        document.querySelectorAll('.template-option').forEach(btn => {
            btn.classList.remove('active');
        });

        document.querySelector(`[data-template="${template}"]`)?.classList.add('active');

        // تحديث المعاينة
        this.updatePreview();

        // حفظ التغيير
        this.saveProgress();

        this.showNotification(`تم تغيير القالب إلى ${this.getTemplateDisplayName(template)}`, 'success');
    }

    /**
     * الحصول على اسم القالب للعرض
     */
    getTemplateDisplayName(template) {
        const names = {
            'modern': 'العصري',
            'classic': 'الكلاسيكي',
            'creative': 'الإبداعي'
        };
        return names[template] || template;
    }

    /**
     * اختيار نظام الألوان
     */
    selectColorScheme(scheme) {
        this.selectedColorScheme = scheme;
        this.cvData.customization.colorScheme = scheme;

        // تحديث الأزرار النشطة
        document.querySelectorAll('.color-scheme').forEach(btn => {
            btn.classList.remove('active');
        });

        document.querySelector(`[data-scheme="${scheme}"]`)?.classList.add('active');

        // تطبيق الألوان
        this.applyColorScheme(scheme);

        // تحديث المعاينة
        this.updatePreview();

        // حفظ التغيير
        this.saveProgress();

        this.showNotification(`تم تغيير نظام الألوان`, 'success');
    }

    /**
     * تطبيق نظام الألوان
     */
    applyColorScheme(scheme) {
        const schemes = {
            'blue': { primary: '#1e40af', secondary: '#3b82f6' },
            'green': { primary: '#059669', secondary: '#10b981' },
            'purple': { primary: '#7c3aed', secondary: '#8b5cf6' },
            'orange': { primary: '#ea580c', secondary: '#f97316' }
        };

        const colors = schemes[scheme];
        if (colors) {
            document.documentElement.style.setProperty('--primary-color', colors.primary);
            document.documentElement.style.setProperty('--primary-light', colors.secondary);
        }
    }

    /**
     * تهيئة رفع الصور
     */
    initializePhotoUpload() {
        const uploadArea = document.getElementById('photoUploadArea');
        const photoInput = document.getElementById('photoInput');
        const photoPreview = document.getElementById('photoPreview');

        if (!uploadArea || !photoInput || !photoPreview) return;

        // النقر لرفع الصورة
        uploadArea.addEventListener('click', () => {
            photoInput.click();
        });

        // السحب والإفلات
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handlePhotoUpload(files[0]);
            }
        });

        // تغيير ملف الإدخال
        photoInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handlePhotoUpload(e.target.files[0]);
            }
        });

        console.log('✅ تم تهيئة رفع الصور');
    }

    /**
     * معالجة رفع الصورة
     */
    handlePhotoUpload(file) {
        // التحقق من نوع الملف
        if (!file.type.startsWith('image/')) {
            this.showNotification('يرجى اختيار ملف صورة صحيح (JPG, PNG)', 'error');
            return;
        }

        // التحقق من حجم الملف (5 ميجابايت كحد أقصى)
        if (file.size > 5 * 1024 * 1024) {
            this.showNotification('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const imageData = e.target.result;
            this.cvData.personal.photo = imageData;

            // تحديث معاينة الصورة
            const photoPreview = document.getElementById('photoPreview');
            if (photoPreview) {
                photoPreview.innerHTML = `
                    <div class="uploaded-photo">
                        <img src="${imageData}" alt="الصورة الشخصية" style="width: 120px; height: 120px; object-fit: cover; border-radius: 50%;">
                        <button type="button" class="remove-photo-btn" onclick="cvBuilder.removePhoto()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <p>انقر لتغيير الصورة</p>
                `;
            }

            // تحديث المعاينة
            this.updatePreview();

            // حفظ البيانات
            this.saveProgress();

            this.showNotification('تم رفع الصورة بنجاح', 'success');
        };

        reader.readAsDataURL(file);
    }

    /**
     * حذف الصورة الشخصية
     */
    removePhoto() {
        this.cvData.personal.photo = null;

        const photoPreview = document.getElementById('photoPreview');
        if (photoPreview) {
            photoPreview.innerHTML = `
                <i class="fas fa-camera"></i>
                <h4>اسحب الصورة هنا أو انقر للاختيار</h4>
                <p>يُفضل صورة بحجم 300x300 بكسل</p>
                <small>الحد الأقصى: 5 ميجابايت (JPG, PNG)</small>
            `;
        }

        this.updatePreview();
        this.saveProgress();

        this.showNotification('تم حذف الصورة', 'info');
    }

    /**
     * إضافة خبرة عملية جديدة
     */
    addExperience() {
        const experience = {
            id: Date.now(),
            jobTitle: '',
            company: '',
            startDate: '',
            endDate: '',
            current: false,
            description: ''
        };

        this.cvData.experiences.push(experience);
        this.renderExperience(experience);

        console.log('➕ تم إضافة خبرة عملية جديدة');
    }

    /**
     * عرض خبرة عملية
     */
    renderExperience(experience) {
        const container = document.getElementById('experiencesContainer');
        if (!container) return;

        const experienceElement = document.createElement('div');
        experienceElement.className = 'experience-item';
        experienceElement.dataset.id = experience.id;

        experienceElement.innerHTML = `
            <div class="item-header">
                <h4><i class="fas fa-briefcase"></i> خبرة عملية</h4>
                <button type="button" class="remove-item-btn" onclick="cvBuilder.removeExperience(${experience.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label for="jobTitle_${experience.id}" class="required">المسمى الوظيفي</label>
                    <input type="text" id="jobTitle_${experience.id}" name="jobTitle"
                           value="${experience.jobTitle}" required
                           placeholder="مثال: مطور ويب أول">
                </div>

                <div class="form-group">
                    <label for="company_${experience.id}" class="required">اسم الشركة</label>
                    <input type="text" id="company_${experience.id}" name="company"
                           value="${experience.company}" required
                           placeholder="مثال: شركة التقنية المتقدمة">
                </div>

                <div class="form-group">
                    <label for="startDate_${experience.id}" class="required">تاريخ البداية</label>
                    <input type="month" id="startDate_${experience.id}" name="startDate"
                           value="${experience.startDate}" required>
                </div>

                <div class="form-group">
                    <label for="endDate_${experience.id}">تاريخ النهاية</label>
                    <input type="month" id="endDate_${experience.id}" name="endDate"
                           value="${experience.endDate}" ${experience.current ? 'disabled' : ''}>
                    <label class="checkbox-label">
                        <input type="checkbox" name="current" ${experience.current ? 'checked' : ''}
                               onchange="cvBuilder.toggleCurrentJob(${experience.id}, this.checked)">
                        <span class="checkmark"></span>
                        أعمل حالياً في هذا المنصب
                    </label>
                </div>

                <div class="form-group full-width">
                    <label for="description_${experience.id}">وصف المهام والإنجازات</label>
                    <textarea id="description_${experience.id}" name="description" rows="4"
                              placeholder="اكتب وصفاً مختصراً لمهامك الرئيسية وإنجازاتك في هذا المنصب...">${experience.description}</textarea>
                </div>
            </div>
        `;

        container.appendChild(experienceElement);

        // إضافة مستمعي الأحداث للحقول الجديدة
        this.attachExperienceListeners(experienceElement, experience.id);
    }

    /**
     * إضافة مستمعي الأحداث للخبرة العملية
     */
    attachExperienceListeners(element, experienceId) {
        const inputs = element.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.updateExperienceData(experienceId, input.name, input.value);
            });
        });
    }

    /**
     * تحديث بيانات الخبرة العملية
     */
    updateExperienceData(experienceId, fieldName, value) {
        const experience = this.cvData.experiences.find(exp => exp.id === experienceId);
        if (experience) {
            experience[fieldName] = value;
            this.updatePreview();
        }
    }

    /**
     * تبديل حالة الوظيفة الحالية
     */
    toggleCurrentJob(experienceId, isCurrent) {
        const experience = this.cvData.experiences.find(exp => exp.id === experienceId);
        if (experience) {
            experience.current = isCurrent;

            const endDateInput = document.getElementById(`endDate_${experienceId}`);
            if (endDateInput) {
                endDateInput.disabled = isCurrent;
                if (isCurrent) {
                    endDateInput.value = '';
                    experience.endDate = '';
                }
            }

            this.updatePreview();
        }
    }

    /**
     * حذف خبرة عملية
     */
    removeExperience(experienceId) {
        this.cvData.experiences = this.cvData.experiences.filter(exp => exp.id !== experienceId);

        const experienceElement = document.querySelector(`[data-id="${experienceId}"]`);
        if (experienceElement) {
            experienceElement.remove();
        }

        this.updatePreview();
        this.showNotification('تم حذف الخبرة العملية', 'info');
    }

    /**
     * إضافة مؤهل أكاديمي جديد
     */
    addEducation() {
        const education = {
            id: Date.now(),
            degree: '',
            institution: '',
            year: '',
            gpa: '',
            description: ''
        };

        this.cvData.education.push(education);
        this.renderEducation(education);

        console.log('➕ تم إضافة مؤهل أكاديمي جديد');
    }

    /**
     * عرض مؤهل أكاديمي
     */
    renderEducation(education) {
        const container = document.getElementById('educationContainer');
        if (!container) return;

        const educationElement = document.createElement('div');
        educationElement.className = 'education-item';
        educationElement.dataset.id = education.id;

        educationElement.innerHTML = `
            <div class="item-header">
                <h4><i class="fas fa-graduation-cap"></i> مؤهل أكاديمي</h4>
                <button type="button" class="remove-item-btn" onclick="cvBuilder.removeEducation(${education.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label for="degree_${education.id}" class="required">الدرجة العلمية</label>
                    <input type="text" id="degree_${education.id}" name="degree"
                           value="${education.degree}" required
                           placeholder="مثال: بكالوريوس علوم الحاسب">
                </div>

                <div class="form-group">
                    <label for="institution_${education.id}" class="required">اسم المؤسسة</label>
                    <input type="text" id="institution_${education.id}" name="institution"
                           value="${education.institution}" required
                           placeholder="مثال: جامعة الملك سعود">
                </div>

                <div class="form-group">
                    <label for="year_${education.id}" class="required">سنة التخرج</label>
                    <input type="number" id="year_${education.id}" name="year"
                           value="${education.year}" required min="1950" max="2030"
                           placeholder="2023">
                </div>

                <div class="form-group">
                    <label for="gpa_${education.id}">المعدل التراكمي</label>
                    <input type="text" id="gpa_${education.id}" name="gpa"
                           value="${education.gpa}"
                           placeholder="مثال: 3.8 من 4.0">
                </div>

                <div class="form-group full-width">
                    <label for="eduDescription_${education.id}">تفاصيل إضافية</label>
                    <textarea id="eduDescription_${education.id}" name="description" rows="3"
                              placeholder="اذكر أي تفاصيل إضافية مثل التخصص الدقيق، الأنشطة، أو الإنجازات...">${education.description}</textarea>
                </div>
            </div>
        `;

        container.appendChild(educationElement);

        // إضافة مستمعي الأحداث للحقول الجديدة
        this.attachEducationListeners(educationElement, education.id);
    }

    /**
     * إضافة مستمعي الأحداث للمؤهل الأكاديمي
     */
    attachEducationListeners(element, educationId) {
        const inputs = element.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.updateEducationData(educationId, input.name, input.value);
            });
        });
    }

    /**
     * تحديث بيانات المؤهل الأكاديمي
     */
    updateEducationData(educationId, fieldName, value) {
        const education = this.cvData.education.find(edu => edu.id === educationId);
        if (education) {
            education[fieldName] = value;
            this.updatePreview();
        }
    }

    /**
     * حذف مؤهل أكاديمي
     */
    removeEducation(educationId) {
        this.cvData.education = this.cvData.education.filter(edu => edu.id !== educationId);

        const educationElement = document.querySelector(`[data-id="${educationId}"]`);
        if (educationElement) {
            educationElement.remove();
        }

        this.updatePreview();
        this.showNotification('تم حذف المؤهل الأكاديمي', 'info');
    }

    /**
     * إضافة مهارة تقنية
     */
    addTechnicalSkill() {
        const input = document.getElementById('technicalSkillInput');
        if (!input || !input.value.trim()) return;

        const skill = input.value.trim();
        if (!this.cvData.technicalSkills.includes(skill)) {
            this.cvData.technicalSkills.push(skill);
            this.renderTechnicalSkills();
            input.value = '';
            this.updatePreview();
            this.saveProgress();
        }
    }

    /**
     * عرض المهارات التقنية
     */
    renderTechnicalSkills() {
        const container = document.getElementById('technicalSkillsTags');
        if (!container) return;

        container.innerHTML = this.cvData.technicalSkills.map(skill => `
            <span class="skill-tag">
                ${skill}
                <button type="button" onclick="cvBuilder.removeTechnicalSkill('${skill}')">
                    <i class="fas fa-times"></i>
                </button>
            </span>
        `).join('');
    }

    /**
     * حذف مهارة تقنية
     */
    removeTechnicalSkill(skill) {
        this.cvData.technicalSkills = this.cvData.technicalSkills.filter(s => s !== skill);
        this.renderTechnicalSkills();
        this.updatePreview();
        this.saveProgress();
    }

    /**
     * إضافة مهارة شخصية
     */
    addSoftSkill() {
        const input = document.getElementById('softSkillInput');
        if (!input || !input.value.trim()) return;

        const skill = input.value.trim();
        if (!this.cvData.softSkills.includes(skill)) {
            this.cvData.softSkills.push(skill);
            this.renderSoftSkills();
            input.value = '';
            this.updatePreview();
            this.saveProgress();
        }
    }

    /**
     * عرض المهارات الشخصية
     */
    renderSoftSkills() {
        const container = document.getElementById('softSkillsTags');
        if (!container) return;

        container.innerHTML = this.cvData.softSkills.map(skill => `
            <span class="skill-tag soft-skill">
                ${skill}
                <button type="button" onclick="cvBuilder.removeSoftSkill('${skill}')">
                    <i class="fas fa-times"></i>
                </button>
            </span>
        `).join('');
    }

    /**
     * حذف مهارة شخصية
     */
    removeSoftSkill(skill) {
        this.cvData.softSkills = this.cvData.softSkills.filter(s => s !== skill);
        this.renderSoftSkills();
        this.updatePreview();
        this.saveProgress();
    }

    /**
     * إضافة لغة جديدة
     */
    addLanguage() {
        const language = {
            id: Date.now(),
            name: '',
            level: ''
        };

        this.cvData.languages.push(language);
        this.renderLanguage(language);

        console.log('➕ تم إضافة لغة جديدة');
    }

    /**
     * عرض لغة
     */
    renderLanguage(language) {
        const container = document.getElementById('languagesContainer');
        if (!container) return;

        const languageElement = document.createElement('div');
        languageElement.className = 'language-item';
        languageElement.dataset.id = language.id;

        languageElement.innerHTML = `
            <div class="form-grid">
                <div class="form-group">
                    <label for="langName_${language.id}" class="required">اسم اللغة</label>
                    <input type="text" id="langName_${language.id}" name="name"
                           value="${language.name}" required
                           placeholder="مثال: الإنجليزية">
                </div>

                <div class="form-group">
                    <label for="langLevel_${language.id}" class="required">مستوى الإتقان</label>
                    <select id="langLevel_${language.id}" name="level" required>
                        <option value="">اختر المستوى</option>
                        <option value="مبتدئ" ${language.level === 'مبتدئ' ? 'selected' : ''}>مبتدئ</option>
                        <option value="متوسط" ${language.level === 'متوسط' ? 'selected' : ''}>متوسط</option>
                        <option value="متقدم" ${language.level === 'متقدم' ? 'selected' : ''}>متقدم</option>
                        <option value="ممتاز" ${language.level === 'ممتاز' ? 'selected' : ''}>ممتاز</option>
                        <option value="اللغة الأم" ${language.level === 'اللغة الأم' ? 'selected' : ''}>اللغة الأم</option>
                    </select>
                </div>

                <div class="form-group">
                    <button type="button" class="btn btn-outline btn-sm remove-item-btn"
                            onclick="cvBuilder.removeLanguage(${language.id})">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                </div>
            </div>
        `;

        container.appendChild(languageElement);

        // إضافة مستمعي الأحداث للحقول الجديدة
        this.attachLanguageListeners(languageElement, language.id);
    }

    /**
     * إضافة مستمعي الأحداث للغة
     */
    attachLanguageListeners(element, languageId) {
        const inputs = element.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.updateLanguageData(languageId, input.name, input.value);
            });
        });
    }

    /**
     * تحديث بيانات اللغة
     */
    updateLanguageData(languageId, fieldName, value) {
        const language = this.cvData.languages.find(lang => lang.id === languageId);
        if (language) {
            language[fieldName] = value;
            this.updatePreview();
        }
    }

    /**
     * حذف لغة
     */
    removeLanguage(languageId) {
        this.cvData.languages = this.cvData.languages.filter(lang => lang.id !== languageId);

        const languageElement = document.querySelector(`[data-id="${languageId}"]`);
        if (languageElement) {
            languageElement.remove();
        }

        this.updatePreview();
        this.showNotification('تم حذف اللغة', 'info');
    }

    /**
     * إعداد الحفظ التلقائي
     */
    setupAutoSave() {
        // حفظ كل 30 ثانية
        setInterval(() => {
            this.saveProgress();
        }, 30000);

        console.log('✅ تم إعداد الحفظ التلقائي');
    }

    /**
     * حفظ التقدم
     */
    saveProgress() {
        try {
            const dataToSave = {
                ...this.cvData,
                currentStep: this.currentStep,
                selectedTemplate: this.selectedTemplate,
                selectedColorScheme: this.selectedColorScheme,
                lastSaved: new Date().toISOString(),
                version: '2.0.0'
            };

            localStorage.setItem('elashrafy_cv_data', JSON.stringify(dataToSave));
            console.log('💾 تم حفظ البيانات بنجاح');

            // إظهار مؤشر الحفظ
            this.showSaveIndicator();

        } catch (error) {
            console.error('❌ خطأ في حفظ البيانات:', error);
            this.showNotification('حدث خطأ في حفظ البيانات', 'error');
        }
    }

    /**
     * تحميل البيانات المحفوظة
     */
    loadSavedData() {
        try {
            const savedData = localStorage.getItem('elashrafy_cv_data');
            if (savedData) {
                const parsedData = JSON.parse(savedData);

                // دمج البيانات مع الهيكل الافتراضي
                this.cvData = { ...this.cvData, ...parsedData };
                this.currentStep = parsedData.currentStep || 1;
                this.selectedTemplate = parsedData.selectedTemplate || 'modern';
                this.selectedColorScheme = parsedData.selectedColorScheme || 'blue';

                // تطبيق الإعدادات المحفوظة
                this.applyColorScheme(this.selectedColorScheme);
                this.goToStep(this.currentStep);

                // ملء النماذج بالبيانات المحفوظة
                this.populateFormsWithData();

                console.log('📂 تم تحميل البيانات المحفوظة بنجاح');
                this.showNotification('تم استعادة بياناتك المحفوظة', 'success');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات المحفوظة:', error);
        }
    }

    /**
     * ملء النماذج بالبيانات المحفوظة
     */
    populateFormsWithData() {
        // ملء البيانات الشخصية
        Object.keys(this.cvData.personal).forEach(field => {
            const input = document.getElementById(field);
            if (input && this.cvData.personal[field]) {
                input.value = this.cvData.personal[field];
            }
        });

        // ملء الملخص المهني
        const summaryTextarea = document.getElementById('professionalSummary');
        if (summaryTextarea && this.cvData.professionalSummary) {
            summaryTextarea.value = this.cvData.professionalSummary;
        }

        // عرض الخبرات المحفوظة
        this.cvData.experiences.forEach(experience => {
            this.renderExperience(experience);
        });

        // عرض التعليم المحفوظ
        this.cvData.education.forEach(education => {
            this.renderEducation(education);
        });

        // عرض المهارات المحفوظة
        this.renderTechnicalSkills();
        this.renderSoftSkills();

        // عرض اللغات المحفوظة
        this.cvData.languages.forEach(language => {
            this.renderLanguage(language);
        });

        // تطبيق إعدادات التخصيص
        this.applyCustomizationSettings();
    }

    /**
     * تطبيق إعدادات التخصيص
     */
    applyCustomizationSettings() {
        const customization = this.cvData.customization;

        // تطبيق القالب المحفوظ
        this.selectTemplate(customization.template || 'modern');

        // تطبيق نظام الألوان المحفوظ
        this.selectColorScheme(customization.colorScheme || 'blue');

        // تطبيق الخيارات الإضافية
        Object.keys(customization).forEach(option => {
            const checkbox = document.getElementById(option);
            if (checkbox && checkbox.type === 'checkbox') {
                checkbox.checked = customization[option];
            }
        });
    }

    /**
     * إظهار مؤشر الحفظ
     */
    showSaveIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'save-indicator';
        indicator.innerHTML = '<i class="fas fa-check"></i> تم الحفظ';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: var(--success-color);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            z-index: 1000;
            animation: fadeInOut 2s ease-in-out;
        `;

        document.body.appendChild(indicator);

        setTimeout(() => {
            indicator.remove();
        }, 2000);
    }

    /**
     * معالجة اختصارات لوحة المفاتيح
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + S للحفظ
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            this.saveProgress();
            this.showNotification('تم حفظ التقدم', 'success');
        }

        // Ctrl/Cmd + Enter للانتقال للخطوة التالية
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            this.nextStep();
        }

        // Escape لإغلاق النوافذ المنبثقة
        if (e.key === 'Escape') {
            this.closeFullPreview();
        }

        // Enter في حقول المهارات
        if (e.key === 'Enter') {
            if (e.target.id === 'technicalSkillInput') {
                e.preventDefault();
                this.addTechnicalSkill();
            } else if (e.target.id === 'softSkillInput') {
                e.preventDefault();
                this.addSoftSkill();
            }
        }
    }

    /**
     * التكبير
     */
    zoomIn() {
        if (this.zoomLevel < 2) {
            this.zoomLevel += 0.1;
            this.updateZoom();
        }
    }

    /**
     * التصغير
     */
    zoomOut() {
        if (this.zoomLevel > 0.5) {
            this.zoomLevel -= 0.1;
            this.updateZoom();
        }
    }

    /**
     * إعادة تعيين التكبير
     */
    resetZoom() {
        this.zoomLevel = 1;
        this.updateZoom();
    }

    /**
     * تحديث التكبير
     */
    updateZoom() {
        const previewElement = document.querySelector('.cv-preview');
        const zoomLevelElement = document.getElementById('zoomLevel');

        if (previewElement) {
            previewElement.style.transform = `scale(${this.zoomLevel})`;
        }

        if (zoomLevelElement) {
            zoomLevelElement.textContent = `${Math.round(this.zoomLevel * 100)}%`;
        }
    }

    /**
     * معاينة السيرة الذاتية
     */
    previewCV() {
        this.openFullPreview();
    }

    /**
     * فتح المعاينة الكاملة
     */
    openFullPreview() {
        const modal = document.getElementById('fullPreviewModal');
        if (modal) {
            modal.classList.add('active');

            // عرض المحتوى في المعاينة الكاملة
            const container = document.getElementById('fullPreviewContainer');
            if (container && this.templatesEngine) {
                this.templatesEngine.renderFullPreview(container, this.cvData, this.selectedTemplate);
            }
        }
    }

    /**
     * إغلاق المعاينة الكاملة
     */
    closeFullPreview() {
        const modal = document.getElementById('fullPreviewModal');
        if (modal) {
            modal.classList.remove('active');
        }
    }

    /**
     * تصدير إلى PDF
     */
    async exportToPDF() {
        if (this.exportSystem) {
            try {
                this.showNotification('جاري إنشاء ملف PDF...', 'info');
                await this.exportSystem.exportToPDF(this.cvData, this.selectedTemplate);
                this.showNotification('تم تصدير ملف PDF بنجاح', 'success');
            } catch (error) {
                console.error('خطأ في تصدير PDF:', error);
                this.showNotification('حدث خطأ في تصدير ملف PDF', 'error');
            }
        } else {
            this.showNotification('نظام التصدير غير متاح حالياً', 'warning');
        }
    }

    /**
     * إنهاء إنشاء السيرة الذاتية
     */
    finishCV() {
        // التحقق من اكتمال البيانات الأساسية
        if (!this.validateRequiredData()) {
            this.showNotification('يرجى ملء البيانات الأساسية المطلوبة', 'warning');
            return;
        }

        // حفظ البيانات النهائية
        this.saveProgress();

        // إظهار رسالة التهنئة
        this.showCompletionMessage();

        // فتح المعاينة الكاملة
        this.openFullPreview();
    }

    /**
     * التحقق من البيانات المطلوبة
     */
    validateRequiredData() {
        const required = [
            this.cvData.personal.fullName,
            this.cvData.personal.email,
            this.cvData.personal.phone,
            this.cvData.professionalSummary
        ];

        return required.every(field => field && field.trim().length > 0);
    }

    /**
     * إظهار رسالة الإكمال
     */
    showCompletionMessage() {
        const message = `
            <div style="text-align: center; padding: 20px;">
                <i class="fas fa-trophy" style="font-size: 3rem; color: var(--success-color); margin-bottom: 16px;"></i>
                <h3>تهانينا! تم إنشاء سيرتك الذاتية بنجاح</h3>
                <p>يمكنك الآن معاينة سيرتك الذاتية وتصديرها كملف PDF</p>
            </div>
        `;

        this.showNotification(message, 'success');
    }

    /**
     * إظهار الإشعارات
     */
    showNotification(message, type = 'info') {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };

        const colors = {
            success: 'var(--success-color)',
            error: 'var(--error-color)',
            warning: 'var(--warning-color)',
            info: 'var(--info-color)'
        };

        notification.style.cssText = `
            background: ${colors[type]};
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: var(--shadow-lg);
            animation: slideInRight 0.3s ease-out;
            display: flex;
            align-items: center;
            gap: 12px;
        `;

        notification.innerHTML = `
            <i class="fas fa-${icons[type]}"></i>
            <div>${message}</div>
            <button onclick="this.parentElement.remove()" style="background: none; border: none; color: white; cursor: pointer; margin-right: auto;">
                <i class="fas fa-times"></i>
            </button>
        `;

        container.appendChild(notification);

        // إزالة الإشعار تلقائياً بعد 5 ثوانٍ
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    }

    /**
     * تصدير البيانات كـ JSON
     */
    exportData() {
        const dataStr = JSON.stringify(this.cvData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `${this.cvData.personal.fullName || 'السيرة_الذاتية'}_data.json`;
        link.click();

        URL.revokeObjectURL(url);
        this.showNotification('تم تصدير البيانات بنجاح', 'success');
    }

    /**
     * استيراد البيانات من JSON
     */
    importData(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedData = JSON.parse(e.target.result);
                this.cvData = { ...this.cvData, ...importedData };
                this.populateFormsWithData();
                this.updatePreview();
                this.showNotification('تم استيراد البيانات بنجاح', 'success');
            } catch (error) {
                this.showNotification('خطأ في قراءة ملف البيانات', 'error');
            }
        };
        reader.readAsText(file);
    }

    /**
     * مسح جميع البيانات
     */
    clearAllData() {
        if (confirm('هل أنت متأكد من رغبتك في مسح جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء.')) {
            localStorage.removeItem('elashrafy_cv_data');
            location.reload();
        }
    }

    /**
     * الحصول على إحصائيات السيرة الذاتية
     */
    getCVStats() {
        return {
            completedFields: this.getCompletedFieldsCount(),
            totalFields: this.getTotalFieldsCount(),
            experiencesCount: this.cvData.experiences.length,
            educationCount: this.cvData.education.length,
            technicalSkillsCount: this.cvData.technicalSkills.length,
            softSkillsCount: this.cvData.softSkills.length,
            languagesCount: this.cvData.languages.length,
            completionPercentage: Math.round((this.getCompletedFieldsCount() / this.getTotalFieldsCount()) * 100)
        };
    }

    /**
     * ملء مثال للملخص المهني
     */
    fillExampleSummary(type) {
        const examples = {
            developer: 'مطور برمجيات محترف مع أكثر من 5 سنوات من الخبرة في تطوير التطبيقات الحديثة باستخدام JavaScript وReact وNode.js. متخصص في إنشاء واجهات مستخدم تفاعلية وحلول تقنية مبتكرة. شغوف بالتعلم المستمر ومواكبة أحدث التقنيات في مجال تطوير الويب، مع سجل حافل في تسليم المشاريع في الوقت المحدد وبجودة عالية.',

            designer: 'مصمم جرافيك إبداعي متخصص في تصميم الهوية البصرية والتسويق الرقمي مع خبرة 4 سنوات في العمل مع العلامات التجارية المحلية والعالمية. أتقن استخدام Adobe Creative Suite وأدوات التصميم الحديثة لإنتاج تصاميم مبتكرة تحقق أهداف العملاء التسويقية. أسعى لتطوير مهاراتي في مجال UX/UI Design وتصميم التطبيقات.',

            manager: 'مدير مشاريع معتمد PMP مع خبرة 8 سنوات في إدارة المشاريع التقنية والتحول الرقمي. نجحت في قيادة فرق متعددة التخصصات وتسليم مشاريع بقيمة تزيد عن 10 مليون ريال في الوقت المحدد وضمن الميزانية. أتمتع بمهارات قيادية قوية وقدرة على حل المشكلات المعقدة، مع التركيز على تحسين العمليات وزيادة الكفاءة التشغيلية.'
        };

        const summaryTextarea = document.getElementById('professionalSummary');
        if (summaryTextarea && examples[type]) {
            summaryTextarea.value = examples[type];
            this.cvData.professionalSummary = examples[type];

            // تحديث عداد الأحرف
            this.updateCharacterCounter('summaryCounter', examples[type], 500);

            // تحديث المعاينة
            this.updatePreview();

            // حفظ البيانات
            this.saveProgress();

            this.showNotification('تم إدراج مثال الملخص المهني بنجاح', 'success');
        }
    }

    /**
     * إضافة مستمعي أحداث للمهارات
     */
    setupSkillsEventListeners() {
        // مستمع Enter للمهارات التقنية
        const technicalSkillInput = document.getElementById('technicalSkillInput');
        if (technicalSkillInput) {
            technicalSkillInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.addTechnicalSkill();
                }
            });
        }

        // مستمع Enter للمهارات الشخصية
        const softSkillInput = document.getElementById('softSkillInput');
        if (softSkillInput) {
            softSkillInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.addSoftSkill();
                }
            });
        }
    }

    /**
     * إضافة اقتراحات للمهارات
     */
    setupSkillsSuggestions() {
        const technicalSuggestions = [
            'JavaScript', 'Python', 'Java', 'React', 'Angular', 'Vue.js', 'Node.js',
            'PHP', 'Laravel', 'Django', 'Spring Boot', 'MySQL', 'PostgreSQL', 'MongoDB',
            'Docker', 'Kubernetes', 'AWS', 'Azure', 'Git', 'Jenkins', 'HTML', 'CSS',
            'TypeScript', 'C#', 'C++', 'Swift', 'Kotlin', 'Flutter', 'React Native'
        ];

        const softSuggestions = [
            'العمل الجماعي', 'القيادة', 'التواصل الفعال', 'حل المشكلات', 'إدارة الوقت',
            'التفكير النقدي', 'الإبداع', 'التكيف', 'إدارة الضغط', 'التفاوض',
            'العرض والتقديم', 'خدمة العملاء', 'التخطيط الاستراتيجي', 'اتخاذ القرارات',
            'التحليل', 'التنظيم', 'المرونة', 'الصبر', 'الدقة', 'المبادرة'
        ];

        this.addSkillSuggestions('technicalSkillInput', technicalSuggestions);
        this.addSkillSuggestions('softSkillInput', softSuggestions);
    }

    /**
     * إضافة اقتراحات لحقل المهارات
     */
    addSkillSuggestions(inputId, suggestions) {
        const input = document.getElementById(inputId);
        if (!input) return;

        const suggestionsList = document.createElement('div');
        suggestionsList.className = 'skills-suggestions';
        suggestionsList.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--bg-tertiary);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-lg);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        `;

        input.parentElement.style.position = 'relative';
        input.parentElement.appendChild(suggestionsList);

        input.addEventListener('input', () => {
            const value = input.value.toLowerCase().trim();
            if (value.length < 2) {
                suggestionsList.style.display = 'none';
                return;
            }

            const filteredSuggestions = suggestions.filter(skill =>
                skill.toLowerCase().includes(value) &&
                !this.isSkillAlreadyAdded(skill, inputId)
            );

            if (filteredSuggestions.length === 0) {
                suggestionsList.style.display = 'none';
                return;
            }

            suggestionsList.innerHTML = filteredSuggestions.map(skill => `
                <div class="suggestion-item" data-skill="${skill}">
                    ${skill}
                </div>
            `).join('');

            suggestionsList.style.display = 'block';

            // إضافة مستمعي الأحداث للاقتراحات
            suggestionsList.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', () => {
                    input.value = item.dataset.skill;
                    suggestionsList.style.display = 'none';

                    if (inputId === 'technicalSkillInput') {
                        this.addTechnicalSkill();
                    } else {
                        this.addSoftSkill();
                    }
                });
            });
        });

        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!input.parentElement.contains(e.target)) {
                suggestionsList.style.display = 'none';
            }
        });
    }

    /**
     * التحقق من إضافة المهارة مسبقاً
     */
    isSkillAlreadyAdded(skill, inputId) {
        if (inputId === 'technicalSkillInput') {
            return this.cvData.technicalSkills.includes(skill);
        } else {
            return this.cvData.softSkills.includes(skill);
        }
    }

    /**
     * إضافة تأثيرات بصرية للتفاعل
     */
    addVisualEffects() {
        // تأثير النقر على الأزرار
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn') || e.target.closest('.btn')) {
                const btn = e.target.matches('.btn') ? e.target : e.target.closest('.btn');
                this.createRippleEffect(btn, e);
            }
        });

        // تأثير التمرير السلس للخطوات
        document.addEventListener('click', (e) => {
            if (e.target.matches('.step-item') || e.target.closest('.step-item')) {
                const stepItem = e.target.closest('.step-item');
                this.addStepTransition(stepItem);
            }
        });
    }

    /**
     * إنشاء تأثير الموجة عند النقر
     */
    createRippleEffect(element, event) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;

        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    /**
     * إضافة انتقال سلس للخطوات
     */
    addStepTransition(stepItem) {
        stepItem.style.transform = 'scale(0.95)';
        setTimeout(() => {
            stepItem.style.transform = 'scale(1)';
        }, 150);
    }

    /**
     * تهيئة النصائح التفاعلية
     */
    initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');

        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });

            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    /**
     * إظهار النصيحة
     */
    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'custom-tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: var(--text-primary);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            white-space: nowrap;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';

        setTimeout(() => {
            tooltip.style.opacity = '1';
        }, 10);

        this.currentTooltip = tooltip;
    }

    /**
     * إخفاء النصيحة
     */
    hideTooltip() {
        if (this.currentTooltip) {
            this.currentTooltip.style.opacity = '0';
            setTimeout(() => {
                if (this.currentTooltip && this.currentTooltip.parentElement) {
                    this.currentTooltip.remove();
                }
                this.currentTooltip = null;
            }, 300);
        }
    }

    /**
     * تهيئة الاختصارات المتقدمة
     */
    initializeAdvancedShortcuts() {
        // إضافة مستمعي الأحداث للاختصارات المتقدمة
        this.setupSkillsEventListeners();
        this.setupSkillsSuggestions();
        this.addVisualEffects();
        this.initializeTooltips();

        console.log('✅ تم تهيئة الاختصارات والتفاعلات المتقدمة');
    }
}

// إضافة الأنماط المطلوبة للرسوم المتحركة
const animationStyles = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    @keyframes fadeInOut {
        0%, 100% { opacity: 0; }
        50% { opacity: 1; }
    }

    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .notification-container {
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
    }

    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .modal-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .modal-content {
        background: var(--bg-card);
        border-radius: 12px;
        width: 90%;
        max-width: 1000px;
        height: 90%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        transform: scale(0.9);
        transition: transform 0.3s ease;
    }

    .modal-overlay.active .modal-content {
        transform: scale(1);
    }

    .modal-header {
        padding: 20px 24px;
        border-bottom: 1px solid var(--bg-tertiary);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-body {
        flex: 1;
        overflow: auto;
        padding: 24px;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--text-secondary);
        transition: color 0.3s ease;
    }

    .close-btn:hover {
        color: var(--error-color);
    }

    .skill-tag {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: var(--primary-color);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        margin: 4px;
    }

    .skill-tag.soft-skill {
        background: var(--secondary-color);
    }

    .skill-tag button {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 0;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.3s ease;
    }

    .skill-tag button:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    .skills-input-container {
        display: flex;
        gap: 8px;
        margin-bottom: 12px;
    }

    .skills-input-container input {
        flex: 1;
    }

    .skills-tags {
        min-height: 40px;
        border: 1px solid var(--bg-tertiary);
        border-radius: 8px;
        padding: 8px;
        background: var(--bg-secondary);
    }

    .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid var(--bg-tertiary);
    }

    .item-header h4 {
        color: var(--primary-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .remove-item-btn {
        background: var(--error-color);
        color: white;
        border: none;
        padding: 6px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.8rem;
        transition: all 0.3s ease;
    }

    .remove-item-btn:hover {
        background: #dc2626;
        transform: scale(1.05);
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        margin-top: 8px;
    }

    .checkmark {
        width: 18px;
        height: 18px;
        border: 2px solid var(--bg-tertiary);
        border-radius: 3px;
        position: relative;
        transition: all 0.3s ease;
    }

    .checkbox-label input:checked + .checkmark {
        background: var(--primary-color);
        border-color: var(--primary-color);
    }

    .checkbox-label input:checked + .checkmark::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .checkbox-label input {
        display: none;
    }

    .skills-suggestions {
        background: white;
        border: 1px solid var(--bg-tertiary);
        border-radius: var(--border-radius-md);
        box-shadow: var(--shadow-lg);
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
    }

    .suggestion-item {
        padding: 12px 16px;
        cursor: pointer;
        transition: background 0.3s ease;
        border-bottom: 1px solid var(--bg-tertiary);
        font-size: 0.9rem;
        color: var(--text-primary);
    }

    .suggestion-item:last-child {
        border-bottom: none;
    }

    .suggestion-item:hover {
        background: var(--bg-secondary);
        color: var(--primary-color);
    }

    .custom-tooltip {
        position: absolute;
        background: var(--text-primary);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.8rem;
        white-space: nowrap;
        z-index: 10000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .custom-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 5px solid transparent;
        border-top-color: var(--text-primary);
    }

    .btn {
        position: relative;
        overflow: hidden;
    }

    .step-item {
        transition: transform 0.15s ease;
    }

    .form-group.has-animation {
        animation: slideInUp 0.5s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .experience-item,
    .education-item,
    .language-item {
        animation: slideInUp 0.5s ease-out;
        animation-fill-mode: both;
    }

    .experience-item:nth-child(1) { animation-delay: 0.1s; }
    .experience-item:nth-child(2) { animation-delay: 0.2s; }
    .experience-item:nth-child(3) { animation-delay: 0.3s; }

    .education-item:nth-child(1) { animation-delay: 0.1s; }
    .education-item:nth-child(2) { animation-delay: 0.2s; }
    .education-item:nth-child(3) { animation-delay: 0.3s; }

    .skill-tag {
        animation: slideInUp 0.3s ease-out;
        animation-fill-mode: both;
    }

    .template-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .template-card:hover {
        transform: translateY(-8px) scale(1.02);
    }

    .color-scheme {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .color-scheme:hover {
        transform: translateY(-4px);
    }

    .example-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .example-card:hover {
        transform: translateY(-6px) scale(1.02);
    }

    /* تحسينات للحركة المخفضة */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }

        .template-card:hover,
        .color-scheme:hover,
        .example-card:hover {
            transform: none;
        }
    }

    /* تحسينات للشاشات عالية الدقة */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .skills-suggestions {
            border-width: 0.5px;
        }

        .suggestion-item {
            border-bottom-width: 0.5px;
        }
    }
`;

// إضافة الأنماط إلى الصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = animationStyles;
document.head.appendChild(styleSheet);

console.log('🚀 تم تحميل منشئ السيرة الذاتية المتقدم بنجاح');
