{"name": "Elashrafy CV - منشئ السيرة الذاتية الذكي", "short_name": "Elashrafy CV", "description": "منشئ السيرة الذاتية الذكي الأول في العالم العربي مع الذكاء الاصطناعي وقوالب احترافية", "version": "1.0.0", "start_url": "/", "display": "standalone", "orientation": "portrait-primary", "theme_color": "#1e40af", "background_color": "#ffffff", "lang": "ar", "dir": "rtl", "scope": "/", "icons": [{"src": "assets/favicon.svg", "sizes": "any", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "assets/icon-192.png", "sizes": "192x192", "type": "image/png", "purpose": "any"}, {"src": "assets/icon-512.png", "sizes": "512x512", "type": "image/png", "purpose": "any"}, {"src": "assets/icon-maskable-192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable"}, {"src": "assets/icon-maskable-512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}], "screenshots": [{"src": "assets/screenshot-desktop.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "الصفحة الرئيسية لـ Elashrafy CV"}, {"src": "assets/screenshot-mobile.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "منشئ السيرة الذاتية على الهاتف"}], "categories": ["productivity", "business", "utilities"], "shortcuts": [{"name": "إنشاء سيرة ذاتية جديدة", "short_name": "إنشاء جديد", "description": "ابدأ في إنشاء سيرة ذاتية جديدة", "url": "/cv-builder.html", "icons": [{"src": "assets/shortcut-new.png", "sizes": "96x96"}]}, {"name": "تص<PERSON><PERSON> القوالب", "short_name": "القوالب", "description": "تصفح القوالب المتاحة", "url": "/templates.html", "icons": [{"src": "assets/shortcut-templates.png", "sizes": "96x96"}]}], "related_applications": [{"platform": "webapp", "url": "https://elashrafycv.com/manifest.json"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate", "protocol_handlers": [{"protocol": "web+elashrafycv", "url": "/?action=%s"}], "file_handlers": [{"action": "/cv-builder.html", "accept": {"application/json": [".json"], "text/plain": [".txt"]}, "launch_type": "single-client"}], "share_target": {"action": "/cv-builder.html", "method": "POST", "enctype": "multipart/form-data", "params": {"title": "title", "text": "text", "url": "url", "files": [{"name": "cv_data", "accept": ["application/json", "text/plain"]}]}}}