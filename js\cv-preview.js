// ===== CV Preview and Export Functions =====

class CVPreview {
    constructor() {
        this.cvData = null;
        this.currentTemplate = 1;
        this.exportOptions = {
            format: 'pdf',
            quality: 'high',
            includeQR: true,
            includeNFC: false
        };
        
        this.init();
    }
    
    init() {
        this.loadCVData();
        this.setupRealTimePreview();
        this.setupExportOptions();
    }
    
    loadCVData() {
        // Load CV data from localStorage or API
        const savedData = localStorage.getItem('elashrafy_cv_data');
        if (savedData) {
            this.cvData = JSON.parse(savedData);
            this.renderFullPreview();
        }
    }
    
    setupRealTimePreview() {
        // Listen for changes in CV builder
        document.addEventListener('cvDataUpdated', (e) => {
            this.cvData = e.detail;
            this.renderFullPreview();
        });
    }
    
    renderFullPreview() {
        if (!this.cvData) return;
        
        const previewContainer = document.querySelector('.cv-page');
        if (!previewContainer) return;
        
        // Clear existing content
        previewContainer.innerHTML = '';
        
        // Render based on selected template
        switch (this.currentTemplate) {
            case 1:
                this.renderModernTemplate(previewContainer);
                break;
            case 2:
                this.renderClassicTemplate(previewContainer);
                break;
            case 3:
                this.renderCreativeTemplate(previewContainer);
                break;
            default:
                this.renderModernTemplate(previewContainer);
        }
    }
    
    renderModernTemplate(container) {
        const data = this.cvData;
        
        container.innerHTML = `
            <div class="cv-header modern-header">
                <div class="cv-photo">
                    ${data.personal.photo ? 
                        `<img src="${data.personal.photo}" alt="Profile Photo">` : 
                        '<i class="fas fa-user"></i>'
                    }
                </div>
                <div class="cv-basic-info">
                    <h1 class="cv-name">${data.personal.fullName || 'اسمك هنا'}</h1>
                    <h2 class="cv-title">${data.personal.jobTitle || 'المسمى الوظيفي'}</h2>
                    <div class="cv-contact">
                        ${this.renderContactInfo(data.personal)}
                    </div>
                </div>
            </div>
            
            <div class="cv-content modern-content">
                ${this.renderSummarySection(data.summary)}
                ${this.renderExperienceSection(data.experience)}
                ${this.renderEducationSection(data.education)}
                ${this.renderSkillsSection(data.skills)}
                ${this.renderLanguagesSection(data.languages)}
            </div>
        `;
    }
    
    renderClassicTemplate(container) {
        const data = this.cvData;
        
        container.innerHTML = `
            <div class="cv-header classic-header">
                <div class="header-content">
                    <h1 class="cv-name">${data.personal.fullName || 'اسمك هنا'}</h1>
                    <h2 class="cv-title">${data.personal.jobTitle || 'المسمى الوظيفي'}</h2>
                    <div class="cv-contact classic-contact">
                        ${this.renderContactInfo(data.personal)}
                    </div>
                </div>
                ${data.personal.photo ? 
                    `<div class="cv-photo classic-photo">
                        <img src="${data.personal.photo}" alt="Profile Photo">
                    </div>` : ''
                }
            </div>
            
            <div class="cv-content classic-content">
                ${this.renderSummarySection(data.summary)}
                ${this.renderExperienceSection(data.experience)}
                ${this.renderEducationSection(data.education)}
                ${this.renderSkillsSection(data.skills)}
                ${this.renderLanguagesSection(data.languages)}
            </div>
        `;
    }
    
    renderCreativeTemplate(container) {
        const data = this.cvData;
        
        container.innerHTML = `
            <div class="cv-layout creative-layout">
                <div class="cv-sidebar creative-sidebar">
                    <div class="cv-photo creative-photo">
                        ${data.personal.photo ? 
                            `<img src="${data.personal.photo}" alt="Profile Photo">` : 
                            '<i class="fas fa-user"></i>'
                        }
                    </div>
                    <div class="cv-basic-info">
                        <h1 class="cv-name">${data.personal.fullName || 'اسمك هنا'}</h1>
                        <h2 class="cv-title">${data.personal.jobTitle || 'المسمى الوظيفي'}</h2>
                    </div>
                    <div class="cv-contact creative-contact">
                        ${this.renderContactInfo(data.personal)}
                    </div>
                    ${this.renderSkillsSection(data.skills, true)}
                    ${this.renderLanguagesSection(data.languages, true)}
                </div>
                <div class="cv-main creative-main">
                    ${this.renderSummarySection(data.summary)}
                    ${this.renderExperienceSection(data.experience)}
                    ${this.renderEducationSection(data.education)}
                </div>
            </div>
        `;
    }
    
    renderContactInfo(personal) {
        let contactHTML = '';
        
        if (personal.email) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span>${personal.email}</span>
                </div>
            `;
        }
        
        if (personal.phone) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>${personal.phone}</span>
                </div>
            `;
        }
        
        if (personal.city) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>${personal.city}</span>
                </div>
            `;
        }
        
        if (personal.website) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-globe"></i>
                    <span>${personal.website}</span>
                </div>
            `;
        }
        
        if (personal.linkedin) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fab fa-linkedin"></i>
                    <span>LinkedIn</span>
                </div>
            `;
        }
        
        return contactHTML;
    }
    
    renderSummarySection(summary) {
        if (!summary || summary.trim() === '') return '';
        
        return `
            <div class="cv-section summary-section">
                <h3 class="section-title">الملخص المهني</h3>
                <p class="summary-text">${summary}</p>
            </div>
        `;
    }
    
    renderExperienceSection(experiences) {
        if (!experiences || experiences.length === 0) return '';
        
        const experienceHTML = experiences.map(exp => `
            <div class="cv-experience-item">
                <h4 class="job-title">${exp.jobTitle}</h4>
                <div class="company">${exp.company}</div>
                <div class="duration">${exp.startDate} - ${exp.endDate || 'حتى الآن'}</div>
                ${exp.description ? `<p class="description">${exp.description}</p>` : ''}
            </div>
        `).join('');
        
        return `
            <div class="cv-section experience-section">
                <h3 class="section-title">الخبرات العملية</h3>
                <div class="experience-list">
                    ${experienceHTML}
                </div>
            </div>
        `;
    }
    
    renderEducationSection(education) {
        if (!education || education.length === 0) return '';
        
        const educationHTML = education.map(edu => `
            <div class="cv-education-item">
                <h4 class="degree">${edu.degree}</h4>
                <div class="institution">${edu.institution}</div>
                <div class="duration">${edu.year}</div>
                ${edu.gpa ? `<div class="gpa">المعدل: ${edu.gpa}</div>` : ''}
                ${edu.details ? `<p class="description">${edu.details}</p>` : ''}
            </div>
        `).join('');
        
        return `
            <div class="cv-section education-section">
                <h3 class="section-title">التعليم</h3>
                <div class="education-list">
                    ${educationHTML}
                </div>
            </div>
        `;
    }
    
    renderSkillsSection(skills, isSidebar = false) {
        if (!skills || (skills.technical?.length === 0 && skills.soft?.length === 0)) return '';
        
        let skillsHTML = '';
        
        if (skills.technical && skills.technical.length > 0) {
            skillsHTML += `
                <div class="skills-category">
                    <h4>المهارات التقنية</h4>
                    <div class="cv-skills-grid">
                        ${skills.technical.map(skill => `<div class="cv-skill-item">${skill}</div>`).join('')}
                    </div>
                </div>
            `;
        }
        
        if (skills.soft && skills.soft.length > 0) {
            skillsHTML += `
                <div class="skills-category">
                    <h4>المهارات الشخصية</h4>
                    <div class="cv-skills-grid">
                        ${skills.soft.map(skill => `<div class="cv-skill-item soft-skill">${skill}</div>`).join('')}
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="cv-section skills-section ${isSidebar ? 'sidebar-section' : ''}">
                <h3 class="section-title">المهارات</h3>
                ${skillsHTML}
            </div>
        `;
    }
    
    renderLanguagesSection(languages, isSidebar = false) {
        if (!languages || languages.length === 0) return '';
        
        const languagesHTML = languages.map(lang => `
            <div class="cv-language-item">
                <span class="language-name">${lang.name}</span>
                <span class="language-level">${lang.level}</span>
            </div>
        `).join('');
        
        return `
            <div class="cv-section languages-section ${isSidebar ? 'sidebar-section' : ''}">
                <h3 class="section-title">اللغات</h3>
                <div class="languages-list">
                    ${languagesHTML}
                </div>
            </div>
        `;
    }
    
    setupExportOptions() {
        // Setup export functionality
        this.setupPDFExport();
        this.setupImageExport();
        this.setupQRGeneration();
    }
    
    setupPDFExport() {
        // PDF export using jsPDF
        window.exportToPDF = () => {
            const element = document.querySelector('.cv-page') || document.querySelector('.cv-preview-document');
            if (!element) {
                this.showExportError('لم يتم العثور على محتوى السيرة الذاتية للتصدير');
                return;
            }

            // Show loading
            this.showExportLoading('جاري إنشاء ملف PDF...');

            // Ensure all images are loaded
            const images = element.querySelectorAll('img');
            const imagePromises = Array.from(images).map(img => {
                return new Promise((resolve) => {
                    if (img.complete) {
                        resolve();
                    } else {
                        img.onload = resolve;
                        img.onerror = resolve;
                    }
                });
            });

            Promise.all(imagePromises).then(() => {
                // Use html2canvas with optimized settings
                html2canvas(element, {
                    scale: 3, // Higher scale for better quality
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: false,
                    width: element.scrollWidth,
                    height: element.scrollHeight,
                    scrollX: 0,
                    scrollY: 0
                }).then(canvas => {
                    try {
                        const imgData = canvas.toDataURL('image/png', 1.0);
                        const pdf = new jsPDF('p', 'mm', 'a4');

                        const pdfWidth = pdf.internal.pageSize.getWidth();
                        const pdfHeight = pdf.internal.pageSize.getHeight();
                        const imgWidth = canvas.width;
                        const imgHeight = canvas.height;

                        // Calculate dimensions to fit page
                        const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
                        const finalWidth = imgWidth * ratio;
                        const finalHeight = imgHeight * ratio;

                        // Center the image
                        const x = (pdfWidth - finalWidth) / 2;
                        const y = (pdfHeight - finalHeight) / 2;

                        pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);

                        // Add QR code if enabled
                        if (this.exportOptions.includeQR) {
                            this.addQRToPDF(pdf);
                        }

                        const fileName = `${this.cvData?.personal?.fullName || 'السيرة_الذاتية'}.pdf`;
                        pdf.save(fileName);

                        this.hideExportLoading();
                        this.showExportSuccess('تم تصدير ملف PDF بنجاح');

                    } catch (error) {
                        console.error('Error creating PDF:', error);
                        this.hideExportLoading();
                        this.showExportError('حدث خطأ أثناء إنشاء ملف PDF');
                    }
                }).catch(error => {
                    console.error('Error generating canvas:', error);
                    this.hideExportLoading();
                    this.showExportError('حدث خطأ أثناء معالجة محتوى السيرة الذاتية');
                });
            });
        };
    }
    
    setupImageExport() {
        window.exportToImage = (format = 'png') => {
            const element = document.querySelector('.cv-page') || document.querySelector('.cv-preview-document');
            if (!element) {
                this.showExportError('لم يتم العثور على محتوى السيرة الذاتية للتصدير');
                return;
            }

            this.showExportLoading(`جاري إنشاء صورة ${format.toUpperCase()}...`);

            // Ensure all images are loaded
            const images = element.querySelectorAll('img');
            const imagePromises = Array.from(images).map(img => {
                return new Promise((resolve) => {
                    if (img.complete) {
                        resolve();
                    } else {
                        img.onload = resolve;
                        img.onerror = resolve;
                    }
                });
            });

            Promise.all(imagePromises).then(() => {
                html2canvas(element, {
                    scale: 4, // Higher scale for better quality
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: false,
                    width: element.scrollWidth,
                    height: element.scrollHeight,
                    scrollX: 0,
                    scrollY: 0
                }).then(canvas => {
                    try {
                        const quality = format === 'jpg' ? 0.95 : 1.0;
                        const mimeType = format === 'jpg' ? 'image/jpeg' : 'image/png';

                        canvas.toBlob((blob) => {
                            const link = document.createElement('a');
                            const fileName = `${this.cvData?.personal?.fullName || 'السيرة_الذاتية'}.${format}`;

                            link.download = fileName;
                            link.href = URL.createObjectURL(blob);

                            // Trigger download
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            // Clean up
                            URL.revokeObjectURL(link.href);

                            this.hideExportLoading();
                            this.showExportSuccess(`تم تصدير صورة ${format.toUpperCase()} بنجاح`);

                        }, mimeType, quality);

                    } catch (error) {
                        console.error('Error creating image:', error);
                        this.hideExportLoading();
                        this.showExportError('حدث خطأ أثناء إنشاء الصورة');
                    }
                }).catch(error => {
                    console.error('Error generating canvas:', error);
                    this.hideExportLoading();
                    this.showExportError('حدث خطأ أثناء معالجة محتوى السيرة الذاتية');
                });
            });
        };
    }
    
    setupQRGeneration() {
        window.generateQRCode = () => {
            if (!this.cvData || !this.cvData.personal) {
                this.showExportError('لا توجد بيانات شخصية لإنشاء رمز QR');
                return;
            }

            const qrData = this.createQRData();
            if (!qrData) {
                this.showExportError('لا توجد معلومات كافية لإنشاء رمز QR');
                return;
            }

            this.showExportLoading('جاري إنشاء رمز QR...');

            try {
                const qrContainer = document.createElement('div');

                if (typeof QRCode !== 'undefined') {
                    QRCode.toCanvas(qrContainer, qrData, {
                        width: 300,
                        margin: 3,
                        color: {
                            dark: '#1e40af',
                            light: '#ffffff'
                        },
                        errorCorrectionLevel: 'M'
                    }, (error) => {
                        this.hideExportLoading();

                        if (error) {
                            console.error('Error generating QR code:', error);
                            this.showExportError('حدث خطأ أثناء إنشاء رمز QR');
                            return;
                        }

                        try {
                            // Download QR code
                            const canvas = qrContainer.querySelector('canvas');
                            const link = document.createElement('a');
                            const fileName = `${this.cvData.personal.fullName || 'معلومات_الاتصال'}_QR.png`;

                            link.download = fileName;
                            link.href = canvas.toDataURL('image/png');

                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            this.showExportSuccess('تم إنشاء رمز QR بنجاح');

                        } catch (downloadError) {
                            console.error('Error downloading QR code:', downloadError);
                            this.showExportError('حدث خطأ أثناء تحميل رمز QR');
                        }
                    });
                } else {
                    this.hideExportLoading();
                    this.showExportError('مكتبة QR Code غير متاحة');
                }

            } catch (error) {
                this.hideExportLoading();
                console.error('Error in QR generation:', error);
                this.showExportError('حدث خطأ أثناء إنشاء رمز QR');
            }
        };
    }
    
    createQRData() {
        if (!this.cvData || !this.cvData.personal) {
            return null;
        }

        const data = this.cvData.personal;

        // Check if we have at least some contact information
        if (!data.fullName && !data.email && !data.phone) {
            return null;
        }

        let vcard = 'BEGIN:VCARD\nVERSION:3.0\n';

        if (data.fullName) {
            vcard += `FN:${data.fullName}\n`;
            vcard += `N:${data.fullName};;;;\n`;
        }

        if (data.jobTitle) {
            vcard += `TITLE:${data.jobTitle}\n`;
        }

        if (data.email) {
            vcard += `EMAIL:${data.email}\n`;
        }

        if (data.phone) {
            vcard += `TEL:${data.phone}\n`;
        }

        if (data.website) {
            vcard += `URL:${data.website}\n`;
        }

        if (data.city) {
            vcard += `ADR:;;${data.city};;;;\n`;
        }

        if (data.linkedin) {
            vcard += `URL;TYPE=LinkedIn:${data.linkedin}\n`;
        }

        if (data.github) {
            vcard += `URL;TYPE=GitHub:${data.github}\n`;
        }

        vcard += 'END:VCARD';

        return vcard;
    }
    
    addQRToPDF(pdf) {
        // Add QR code to PDF
        const qrData = this.createQRData();
        // Implementation would require QR code generation library
    }
    
    showExportLoading(message) {
        const loading = document.createElement('div');
        loading.id = 'exportLoading';
        loading.className = 'export-loading';
        loading.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        `;
        document.body.appendChild(loading);
    }
    
    hideExportLoading() {
        const loading = document.getElementById('exportLoading');
        if (loading) {
            loading.remove();
        }
    }
    
    showExportError(message) {
        this.hideExportLoading();
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, 'error');
        } else {
            this.showCustomNotification(message, 'error');
        }
    }

    showExportSuccess(message) {
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, 'success');
        } else {
            this.showCustomNotification(message, 'success');
        }
    }

    showCustomNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `export-notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            max-width: 400px;
            text-align: center;
            animation: slideInDown 0.3s ease-out;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOutUp 0.3s ease-in';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 4000);
    }
}

// Initialize CV Preview when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.cvPreview = new CVPreview();
});
