// ===== Enhanced CV Preview and Export Functions =====

class CVPreview {
    constructor() {
        this.cvData = null;
        this.currentTemplate = 1;
        this.zoomLevel = 1;
        this.isFullscreen = false;
        this.exportOptions = {
            format: 'pdf',
            quality: 'high',
            includeQR: true,
            includeNFC: false,
            pageSize: 'A4',
            orientation: 'portrait'
        };

        this.init();
    }

    init() {
        try {
            console.log('🎨 تهيئة معاين السيرة الذاتية...');
            this.loadCVData();
            this.setupRealTimePreview();
            this.setupExportOptions();
            this.setupPreviewControls();
            this.setupKeyboardShortcuts();
            console.log('✅ تم تهيئة معاين السيرة الذاتية بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تهيئة معاين السيرة الذاتية:', error);
            this.showError('حدث خطأ في تحميل معاين السيرة الذاتية');
        }
    }

    loadCVData() {
        try {
            // Load CV data from localStorage or API
            const savedData = localStorage.getItem('elashrafy_cv_data');
            if (savedData) {
                this.cvData = JSON.parse(savedData);
                this.renderFullPreview();
            } else {
                // Initialize with default data structure
                this.cvData = this.getDefaultCVData();
                this.renderFullPreview();
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل بيانات السيرة الذاتية:', error);
            this.cvData = this.getDefaultCVData();
            this.renderFullPreview();
        }
    }

    setupRealTimePreview() {
        // Listen for changes in CV builder with debouncing
        let updateTimeout;
        document.addEventListener('cvDataUpdated', (e) => {
            clearTimeout(updateTimeout);
            updateTimeout = setTimeout(() => {
                this.cvData = e.detail;
                this.renderFullPreview();
            }, 300); // Debounce updates
        });

        // Also listen for input changes directly
        document.addEventListener('input', (e) => {
            if (e.target.closest('.cv-editor')) {
                clearTimeout(updateTimeout);
                updateTimeout = setTimeout(() => {
                    this.updateFromForm();
                }, 500);
            }
        });
    }

    getDefaultCVData() {
        return {
            personal: {
                fullName: '',
                jobTitle: '',
                email: '',
                phone: '',
                location: '',
                photo: null
            },
            summary: '',
            experience: [],
            education: [],
            skills: [],
            languages: []
        };
    }

    updateFromForm() {
        try {
            if (!window.cvBuilder || !window.cvBuilder.cvData) return;

            this.cvData = { ...window.cvBuilder.cvData };
            this.renderFullPreview();
        } catch (error) {
            console.error('❌ خطأ في تحديث المعاينة من النموذج:', error);
        }
    }

    renderFullPreview() {
        try {
            if (!this.cvData) {
                this.renderEmptyState();
                return;
            }

            const previewContainer = document.querySelector('.cv-page');
            if (!previewContainer) {
                console.warn('⚠️ لم يتم العثور على حاوي المعاينة');
                return;
            }

            // Clear existing content
            previewContainer.innerHTML = '';

            // Add loading state
            previewContainer.classList.add('loading');

            // Render based on selected template
            setTimeout(() => {
                switch (this.currentTemplate) {
                    case 1:
                        this.renderModernTemplate(previewContainer);
                        break;
                    case 2:
                        this.renderClassicTemplate(previewContainer);
                        break;
                    case 3:
                        this.renderCreativeTemplate(previewContainer);
                        break;
                    default:
                        this.renderModernTemplate(previewContainer);
                }

                previewContainer.classList.remove('loading');
                this.updateZoom();
            }, 100);

        } catch (error) {
            console.error('❌ خطأ في عرض المعاينة:', error);
            this.renderErrorState();
        }
    }

    renderEmptyState() {
        const previewContainer = document.querySelector('.cv-page');
        if (!previewContainer) return;

        previewContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3>ابدأ في إنشاء سيرتك الذاتية</h3>
                <p>أدخل معلوماتك الشخصية لرؤية المعاينة</p>
            </div>
        `;
    }

    renderErrorState() {
        const previewContainer = document.querySelector('.cv-page');
        if (!previewContainer) return;

        previewContainer.innerHTML = `
            <div class="error-state">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3>حدث خطأ في المعاينة</h3>
                <p>يرجى إعادة تحميل الصفحة أو المحاولة مرة أخرى</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-refresh"></i>
                    إعادة تحميل
                </button>
            </div>
        `;
    }

    setupPreviewControls() {
        // Setup zoom controls
        const zoomInBtn = document.querySelector('[data-action="zoom-in"]');
        const zoomOutBtn = document.querySelector('[data-action="zoom-out"]');
        const zoomResetBtn = document.querySelector('[data-action="zoom-reset"]');

        if (zoomInBtn) zoomInBtn.addEventListener('click', () => this.zoomIn());
        if (zoomOutBtn) zoomOutBtn.addEventListener('click', () => this.zoomOut());
        if (zoomResetBtn) zoomResetBtn.addEventListener('click', () => this.resetZoom());

        // Setup template switching
        const templateBtns = document.querySelectorAll('[data-template]');
        templateBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const templateId = parseInt(e.target.dataset.template);
                this.switchTemplate(templateId);
            });
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '=':
                    case '+':
                        e.preventDefault();
                        this.zoomIn();
                        break;
                    case '-':
                        e.preventDefault();
                        this.zoomOut();
                        break;
                    case '0':
                        e.preventDefault();
                        this.resetZoom();
                        break;
                }
            }
        });
    }

    zoomIn() {
        if (this.zoomLevel < 2) {
            this.zoomLevel += 0.1;
            this.updateZoom();
        }
    }

    zoomOut() {
        if (this.zoomLevel > 0.5) {
            this.zoomLevel -= 0.1;
            this.updateZoom();
        }
    }

    resetZoom() {
        this.zoomLevel = 1;
        this.updateZoom();
    }

    updateZoom() {
        const previewDocument = document.querySelector('.cv-preview-document');
        const zoomDisplay = document.querySelector('.zoom-level');

        if (previewDocument) {
            previewDocument.style.transform = `scale(${this.zoomLevel})`;
        }

        if (zoomDisplay) {
            zoomDisplay.textContent = `${Math.round(this.zoomLevel * 100)}%`;
        }
    }

    switchTemplate(templateId) {
        this.currentTemplate = templateId;
        this.renderFullPreview();

        // Update active template button
        document.querySelectorAll('[data-template]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-template="${templateId}"]`)?.classList.add('active');
    }

    renderModernTemplate(container) {
        const data = this.cvData;

        container.innerHTML = `
            <div class="cv-header modern-header">
                <div class="cv-photo">
                    ${data.personal.photo ?
                        `<img src="${data.personal.photo}" alt="Profile Photo" loading="lazy">` :
                        '<div class="photo-placeholder"><i class="fas fa-user"></i></div>'
                    }
                </div>
                <div class="cv-basic-info">
                    <h1 class="cv-name">${this.escapeHtml(data.personal.fullName) || 'اسمك هنا'}</h1>
                    <h2 class="cv-title">${this.escapeHtml(data.personal.jobTitle) || 'المسمى الوظيفي'}</h2>
                    <div class="cv-contact">
                        ${this.renderContactInfo(data.personal)}
                    </div>
                </div>
            </div>

            <div class="cv-content modern-content">
                ${this.renderSummarySection(data.summary)}
                ${this.renderExperienceSection(data.experience)}
                ${this.renderEducationSection(data.education)}
                ${this.renderSkillsSection(data.skills)}
                ${this.renderLanguagesSection(data.languages)}
            </div>
        `;
    }

    renderClassicTemplate(container) {
        const data = this.cvData;

        container.innerHTML = `
            <div class="cv-header classic-header">
                <div class="header-content">
                    <h1 class="cv-name">${data.personal.fullName || 'اسمك هنا'}</h1>
                    <h2 class="cv-title">${data.personal.jobTitle || 'المسمى الوظيفي'}</h2>
                    <div class="cv-contact classic-contact">
                        ${this.renderContactInfo(data.personal)}
                    </div>
                </div>
                ${data.personal.photo ?
                    `<div class="cv-photo classic-photo">
                        <img src="${data.personal.photo}" alt="Profile Photo">
                    </div>` : ''
                }
            </div>

            <div class="cv-content classic-content">
                ${this.renderSummarySection(data.summary)}
                ${this.renderExperienceSection(data.experience)}
                ${this.renderEducationSection(data.education)}
                ${this.renderSkillsSection(data.skills)}
                ${this.renderLanguagesSection(data.languages)}
            </div>
        `;
    }

    renderCreativeTemplate(container) {
        const data = this.cvData;

        container.innerHTML = `
            <div class="cv-layout creative-layout">
                <div class="cv-sidebar creative-sidebar">
                    <div class="cv-photo creative-photo">
                        ${data.personal.photo ?
                            `<img src="${data.personal.photo}" alt="Profile Photo">` :
                            '<i class="fas fa-user"></i>'
                        }
                    </div>
                    <div class="cv-basic-info">
                        <h1 class="cv-name">${data.personal.fullName || 'اسمك هنا'}</h1>
                        <h2 class="cv-title">${data.personal.jobTitle || 'المسمى الوظيفي'}</h2>
                    </div>
                    <div class="cv-contact creative-contact">
                        ${this.renderContactInfo(data.personal)}
                    </div>
                    ${this.renderSkillsSection(data.skills, true)}
                    ${this.renderLanguagesSection(data.languages, true)}
                </div>
                <div class="cv-main creative-main">
                    ${this.renderSummarySection(data.summary)}
                    ${this.renderExperienceSection(data.experience)}
                    ${this.renderEducationSection(data.education)}
                </div>
            </div>
        `;
    }

    renderContactInfo(personal) {
        let contactHTML = '';

        if (personal.email) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span>${personal.email}</span>
                </div>
            `;
        }

        if (personal.phone) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>${personal.phone}</span>
                </div>
            `;
        }

        if (personal.city) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>${personal.city}</span>
                </div>
            `;
        }

        if (personal.website) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-globe"></i>
                    <span>${personal.website}</span>
                </div>
            `;
        }

        if (personal.linkedin) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fab fa-linkedin"></i>
                    <span>LinkedIn</span>
                </div>
            `;
        }

        return contactHTML;
    }

    renderSummarySection(summary) {
        if (!summary || summary.trim() === '') return '';

        return `
            <div class="cv-section summary-section">
                <h3 class="section-title">الملخص المهني</h3>
                <p class="summary-text">${summary}</p>
            </div>
        `;
    }

    renderExperienceSection(experiences) {
        if (!experiences || experiences.length === 0) return '';

        const experienceHTML = experiences.map(exp => `
            <div class="cv-experience-item">
                <h4 class="job-title">${exp.jobTitle}</h4>
                <div class="company">${exp.company}</div>
                <div class="duration">${exp.startDate} - ${exp.endDate || 'حتى الآن'}</div>
                ${exp.description ? `<p class="description">${exp.description}</p>` : ''}
            </div>
        `).join('');

        return `
            <div class="cv-section experience-section">
                <h3 class="section-title">الخبرات العملية</h3>
                <div class="experience-list">
                    ${experienceHTML}
                </div>
            </div>
        `;
    }

    renderEducationSection(education) {
        if (!education || education.length === 0) return '';

        const educationHTML = education.map(edu => `
            <div class="cv-education-item">
                <h4 class="degree">${edu.degree}</h4>
                <div class="institution">${edu.institution}</div>
                <div class="duration">${edu.year}</div>
                ${edu.gpa ? `<div class="gpa">المعدل: ${edu.gpa}</div>` : ''}
                ${edu.details ? `<p class="description">${edu.details}</p>` : ''}
            </div>
        `).join('');

        return `
            <div class="cv-section education-section">
                <h3 class="section-title">التعليم</h3>
                <div class="education-list">
                    ${educationHTML}
                </div>
            </div>
        `;
    }

    renderSkillsSection(skills, isSidebar = false) {
        if (!skills || (skills.technical?.length === 0 && skills.soft?.length === 0)) return '';

        let skillsHTML = '';

        if (skills.technical && skills.technical.length > 0) {
            skillsHTML += `
                <div class="skills-category">
                    <h4>المهارات التقنية</h4>
                    <div class="cv-skills-grid">
                        ${skills.technical.map(skill => `<div class="cv-skill-item">${skill}</div>`).join('')}
                    </div>
                </div>
            `;
        }

        if (skills.soft && skills.soft.length > 0) {
            skillsHTML += `
                <div class="skills-category">
                    <h4>المهارات الشخصية</h4>
                    <div class="cv-skills-grid">
                        ${skills.soft.map(skill => `<div class="cv-skill-item soft-skill">${skill}</div>`).join('')}
                    </div>
                </div>
            `;
        }

        return `
            <div class="cv-section skills-section ${isSidebar ? 'sidebar-section' : ''}">
                <h3 class="section-title">المهارات</h3>
                ${skillsHTML}
            </div>
        `;
    }

    renderLanguagesSection(languages, isSidebar = false) {
        if (!languages || languages.length === 0) return '';

        const languagesHTML = languages.map(lang => `
            <div class="cv-language-item">
                <span class="language-name">${lang.name}</span>
                <span class="language-level">${lang.level}</span>
            </div>
        `).join('');

        return `
            <div class="cv-section languages-section ${isSidebar ? 'sidebar-section' : ''}">
                <h3 class="section-title">اللغات</h3>
                <div class="languages-list">
                    ${languagesHTML}
                </div>
            </div>
        `;
    }

    setupExportOptions() {
        // Setup export functionality
        this.setupPDFExport();
        this.setupImageExport();
        this.setupQRGeneration();
    }

    setupPDFExport() {
        // Enhanced PDF export with high quality settings
        window.exportToPDF = async () => {
            try {
                const element = document.querySelector('.cv-page') || document.querySelector('.cv-preview-document');
                if (!element) {
                    this.showExportError('لم يتم العثور على محتوى السيرة الذاتية للتصدير');
                    return;
                }

                // Show loading with progress
                this.showExportLoading('جاري تحضير المحتوى للتصدير...');

                // Create a clone for PDF optimization
                const clonedElement = await this.prepareElementForPDF(element);

                // Update loading message
                this.updateExportLoading('جاري إنشاء ملف PDF عالي الجودة...');

                // Ensure all images are loaded
                await this.waitForImages(clonedElement);

                // Use html2canvas with optimized settings for PDF
                const canvas = await html2canvas(clonedElement, {
                    scale: 4, // Higher scale for better quality
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: false,
                    width: clonedElement.scrollWidth,
                    height: clonedElement.scrollHeight,
                    scrollX: 0,
                    scrollY: 0,
                    removeContainer: true,
                    foreignObjectRendering: true,
                    imageTimeout: 15000
                });

                // Clean up cloned element
                if (clonedElement.parentNode) {
                    clonedElement.parentNode.removeChild(clonedElement);
                }

                this.updateExportLoading('جاري تحسين جودة PDF...');

                // Create PDF with proper dimensions
                const imgData = canvas.toDataURL('image/jpeg', 0.95);
                const pdf = new jsPDF({
                    orientation: this.exportOptions.orientation,
                    unit: 'mm',
                    format: this.exportOptions.pageSize.toLowerCase()
                });

                const pdfWidth = pdf.internal.pageSize.getWidth();
                const pdfHeight = pdf.internal.pageSize.getHeight();
                const imgWidth = canvas.width;
                const imgHeight = canvas.height;

                // Calculate dimensions with proper margins
                const margin = 10; // 10mm margin
                const availableWidth = pdfWidth - (margin * 2);
                const availableHeight = pdfHeight - (margin * 2);

                const ratio = Math.min(availableWidth / imgWidth, availableHeight / imgHeight);
                const finalWidth = imgWidth * ratio;
                const finalHeight = imgHeight * ratio;

                // Center the content
                const x = (pdfWidth - finalWidth) / 2;
                const y = (pdfHeight - finalHeight) / 2;

                pdf.addImage(imgData, 'JPEG', x, y, finalWidth, finalHeight);

                // Add metadata
                pdf.setProperties({
                    title: `${this.cvData?.personal?.fullName || 'السيرة الذاتية'} - CV`,
                    subject: 'السيرة الذاتية',
                    author: this.cvData?.personal?.fullName || 'Elashrafy CV',
                    creator: 'Elashrafy CV Builder',
                    producer: 'Elashrafy CV Builder'
                });

                // Add QR code if enabled
                if (this.exportOptions.includeQR) {
                    await this.addQRToPDF(pdf);
                }

                const fileName = `${this.sanitizeFileName(this.cvData?.personal?.fullName) || 'السيرة_الذاتية'}.pdf`;
                pdf.save(fileName);

                this.hideExportLoading();
                this.showExportSuccess('تم تصدير ملف PDF عالي الجودة بنجاح');

            } catch (error) {
                console.error('Error creating PDF:', error);
                this.hideExportLoading();
                this.showExportError('حدث خطأ أثناء إنشاء ملف PDF: ' + error.message);
            }
        };
    }

    async prepareElementForPDF(element) {
        // Create a clone optimized for PDF export
        const clone = element.cloneNode(true);

        // Apply PDF-specific styles
        clone.style.cssText = `
            width: 210mm;
            min-height: 297mm;
            padding: 20mm;
            margin: 0;
            background: white;
            font-size: 12pt;
            line-height: 1.4;
            color: #000;
            box-shadow: none;
            border: none;
            transform: none;
        `;

        // Optimize images for PDF
        const images = clone.querySelectorAll('img');
        images.forEach(img => {
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
        });

        // Add to DOM temporarily for rendering
        clone.style.position = 'absolute';
        clone.style.left = '-9999px';
        clone.style.top = '0';
        document.body.appendChild(clone);

        return clone;
    }

    async waitForImages(element) {
        const images = element.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            return new Promise((resolve) => {
                if (img.complete && img.naturalHeight !== 0) {
                    resolve();
                } else {
                    img.onload = resolve;
                    img.onerror = resolve;
                    // Timeout after 10 seconds
                    setTimeout(resolve, 10000);
                }
            });
        });

        return Promise.all(imagePromises);
    }

    sanitizeFileName(fileName) {
        if (!fileName) return '';
        return fileName.replace(/[<>:"/\\|?*]/g, '_').trim();
    }

    updateExportLoading(message) {
        const loadingElement = document.getElementById('exportLoading');
        if (loadingElement) {
            const messageElement = loadingElement.querySelector('p');
            if (messageElement) {
                messageElement.textContent = message;
            }
        }
    }

    setupImageExport() {
        window.exportToImage = (format = 'png') => {
            const element = document.querySelector('.cv-page') || document.querySelector('.cv-preview-document');
            if (!element) {
                this.showExportError('لم يتم العثور على محتوى السيرة الذاتية للتصدير');
                return;
            }

            this.showExportLoading(`جاري إنشاء صورة ${format.toUpperCase()}...`);

            // Ensure all images are loaded
            const images = element.querySelectorAll('img');
            const imagePromises = Array.from(images).map(img => {
                return new Promise((resolve) => {
                    if (img.complete) {
                        resolve();
                    } else {
                        img.onload = resolve;
                        img.onerror = resolve;
                    }
                });
            });

            Promise.all(imagePromises).then(() => {
                html2canvas(element, {
                    scale: 4, // Higher scale for better quality
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: false,
                    width: element.scrollWidth,
                    height: element.scrollHeight,
                    scrollX: 0,
                    scrollY: 0
                }).then(canvas => {
                    try {
                        const quality = format === 'jpg' ? 0.95 : 1.0;
                        const mimeType = format === 'jpg' ? 'image/jpeg' : 'image/png';

                        canvas.toBlob((blob) => {
                            const link = document.createElement('a');
                            const fileName = `${this.cvData?.personal?.fullName || 'السيرة_الذاتية'}.${format}`;

                            link.download = fileName;
                            link.href = URL.createObjectURL(blob);

                            // Trigger download
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            // Clean up
                            URL.revokeObjectURL(link.href);

                            this.hideExportLoading();
                            this.showExportSuccess(`تم تصدير صورة ${format.toUpperCase()} بنجاح`);

                        }, mimeType, quality);

                    } catch (error) {
                        console.error('Error creating image:', error);
                        this.hideExportLoading();
                        this.showExportError('حدث خطأ أثناء إنشاء الصورة');
                    }
                }).catch(error => {
                    console.error('Error generating canvas:', error);
                    this.hideExportLoading();
                    this.showExportError('حدث خطأ أثناء معالجة محتوى السيرة الذاتية');
                });
            });
        };
    }

    setupQRGeneration() {
        window.generateQRCode = () => {
            if (!this.cvData || !this.cvData.personal) {
                this.showExportError('لا توجد بيانات شخصية لإنشاء رمز QR');
                return;
            }

            const qrData = this.createQRData();
            if (!qrData) {
                this.showExportError('لا توجد معلومات كافية لإنشاء رمز QR');
                return;
            }

            this.showExportLoading('جاري إنشاء رمز QR...');

            try {
                const qrContainer = document.createElement('div');

                if (typeof QRCode !== 'undefined') {
                    QRCode.toCanvas(qrContainer, qrData, {
                        width: 300,
                        margin: 3,
                        color: {
                            dark: '#1e40af',
                            light: '#ffffff'
                        },
                        errorCorrectionLevel: 'M'
                    }, (error) => {
                        this.hideExportLoading();

                        if (error) {
                            console.error('Error generating QR code:', error);
                            this.showExportError('حدث خطأ أثناء إنشاء رمز QR');
                            return;
                        }

                        try {
                            // Download QR code
                            const canvas = qrContainer.querySelector('canvas');
                            const link = document.createElement('a');
                            const fileName = `${this.cvData.personal.fullName || 'معلومات_الاتصال'}_QR.png`;

                            link.download = fileName;
                            link.href = canvas.toDataURL('image/png');

                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            this.showExportSuccess('تم إنشاء رمز QR بنجاح');

                        } catch (downloadError) {
                            console.error('Error downloading QR code:', downloadError);
                            this.showExportError('حدث خطأ أثناء تحميل رمز QR');
                        }
                    });
                } else {
                    this.hideExportLoading();
                    this.showExportError('مكتبة QR Code غير متاحة');
                }

            } catch (error) {
                this.hideExportLoading();
                console.error('Error in QR generation:', error);
                this.showExportError('حدث خطأ أثناء إنشاء رمز QR');
            }
        };
    }

    createQRData() {
        if (!this.cvData || !this.cvData.personal) {
            return null;
        }

        const data = this.cvData.personal;

        // Check if we have at least some contact information
        if (!data.fullName && !data.email && !data.phone) {
            return null;
        }

        let vcard = 'BEGIN:VCARD\nVERSION:3.0\n';

        if (data.fullName) {
            vcard += `FN:${data.fullName}\n`;
            vcard += `N:${data.fullName};;;;\n`;
        }

        if (data.jobTitle) {
            vcard += `TITLE:${data.jobTitle}\n`;
        }

        if (data.email) {
            vcard += `EMAIL:${data.email}\n`;
        }

        if (data.phone) {
            vcard += `TEL:${data.phone}\n`;
        }

        if (data.website) {
            vcard += `URL:${data.website}\n`;
        }

        if (data.city) {
            vcard += `ADR:;;${data.city};;;;\n`;
        }

        if (data.linkedin) {
            vcard += `URL;TYPE=LinkedIn:${data.linkedin}\n`;
        }

        if (data.github) {
            vcard += `URL;TYPE=GitHub:${data.github}\n`;
        }

        vcard += 'END:VCARD';

        return vcard;
    }

    addQRToPDF(pdf) {
        // Add QR code to PDF
        const qrData = this.createQRData();
        // Implementation would require QR code generation library
    }

    showExportLoading(message) {
        const loading = document.createElement('div');
        loading.id = 'exportLoading';
        loading.className = 'export-loading';
        loading.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        `;
        document.body.appendChild(loading);
    }

    hideExportLoading() {
        const loading = document.getElementById('exportLoading');
        if (loading) {
            loading.remove();
        }
    }

    showExportError(message) {
        this.hideExportLoading();
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, 'error');
        } else {
            this.showCustomNotification(message, 'error');
        }
    }

    showExportSuccess(message) {
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, 'success');
        } else {
            this.showCustomNotification(message, 'success');
        }
    }

    showCustomNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `export-notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            max-width: 400px;
            text-align: center;
            animation: slideInDown 0.3s ease-out;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOutUp 0.3s ease-in';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 4000);
    }

    // Utility function to escape HTML
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Enhanced error handling
    showError(message) {
        console.error('CV Preview Error:', message);
        this.showCustomNotification(message, 'error');
    }

    // Preview modal functions
    openFullPreview() {
        this.createPreviewModal();
    }

    createPreviewModal() {
        // Remove existing modal if any
        const existingModal = document.querySelector('.preview-modal-overlay');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.className = 'preview-modal-overlay';
        modal.innerHTML = `
            <div class="preview-modal-content">
                <div class="preview-modal-header">
                    <div class="preview-header-content">
                        <div class="preview-icon-main">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="preview-title-section">
                            <h3>معاينة السيرة الذاتية</h3>
                            <p>معاينة كاملة للسيرة الذاتية قبل التصدير</p>
                        </div>
                    </div>
                    <div class="preview-controls">
                        <button class="preview-control-btn" data-action="zoom-out" title="تصغير">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <span class="zoom-level">100%</span>
                        <button class="preview-control-btn" data-action="zoom-in" title="تكبير">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="preview-control-btn" data-action="zoom-reset" title="إعادة تعيين">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button class="preview-close-btn" onclick="this.closest('.preview-modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="preview-modal-body">
                    <div class="preview-container">
                        <div class="cv-preview-document">
                            <div class="cv-page full-preview">
                                <!-- Content will be inserted here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Setup modal controls
        this.setupModalControls(modal);

        // Render content in modal
        const modalPreviewContainer = modal.querySelector('.cv-page');
        if (modalPreviewContainer && this.cvData) {
            this.renderTemplateInContainer(modalPreviewContainer);
        }

        // Show modal with animation
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);

        // Close on escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
    }

    setupModalControls(modal) {
        const zoomInBtn = modal.querySelector('[data-action="zoom-in"]');
        const zoomOutBtn = modal.querySelector('[data-action="zoom-out"]');
        const zoomResetBtn = modal.querySelector('[data-action="zoom-reset"]');

        if (zoomInBtn) zoomInBtn.addEventListener('click', () => this.zoomIn());
        if (zoomOutBtn) zoomOutBtn.addEventListener('click', () => this.zoomOut());
        if (zoomResetBtn) zoomResetBtn.addEventListener('click', () => this.resetZoom());
    }

    renderTemplateInContainer(container) {
        switch (this.currentTemplate) {
            case 1:
                this.renderModernTemplate(container);
                break;
            case 2:
                this.renderClassicTemplate(container);
                break;
            case 3:
                this.renderCreativeTemplate(container);
                break;
            default:
                this.renderModernTemplate(container);
        }
    }
}

// Global functions for backward compatibility
window.previewCV = function() {
    if (window.cvPreview) {
        window.cvPreview.openFullPreview();
    } else {
        console.warn('CV Preview not initialized');
    }
};

window.exportToPDF = function() {
    if (window.cvPreview && typeof window.cvPreview.setupPDFExport === 'function') {
        // The actual export function is set up by setupPDFExport method
        if (typeof window.exportToPDF === 'function') {
            window.exportToPDF();
        } else {
            console.warn('PDF export function not ready yet');
        }
    } else {
        console.warn('CV Preview not initialized or PDF export not available');
    }
};

window.exportToImage = function(format = 'png') {
    if (window.cvPreview && typeof window.exportToImage === 'function') {
        window.exportToImage(format);
    } else {
        console.warn('Image export not available');
    }
};

window.generateQRCode = function() {
    if (window.cvPreview && typeof window.generateQRCode === 'function') {
        window.generateQRCode();
    } else {
        console.warn('QR Code generation not available');
    }
};

// Initialize CV Preview when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (!window.cvPreview) {
        window.cvPreview = new CVPreview();
        console.log('✅ تم تحميل معاين السيرة الذاتية المحسن بنجاح');
    }
});

// Also initialize if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading
} else {
    // DOM is already loaded
    if (!window.cvPreview) {
        window.cvPreview = new CVPreview();
        console.log('✅ تم تحميل معاين السيرة الذاتية المحسن بنجاح (DOM ready)');
    }
}

console.log('📄 تم تحميل ملف معاين السيرة الذاتية المحسن');
