// ===== Test Functions for Elashrafy CV =====

class CVTester {
    constructor() {
        this.testResults = [];
        this.init();
    }
    
    init() {
        console.log('🧪 بدء اختبار وظائف Elashrafy CV...');
        this.runAllTests();
    }
    
    async runAllTests() {
        await this.testBasicFunctionality();
        await this.testCVBuilder();
        await this.testTemplates();
        await this.testAIAssistant();
        await this.testExportFunctions();
        await this.testLocalStorage();
        
        this.displayResults();
    }
    
    async testBasicFunctionality() {
        console.log('📋 اختبار الوظائف الأساسية...');
        
        // Test navigation
        this.test('Navigation exists', () => {
            return document.getElementById('navbar') !== null;
        });
        
        // Test main app initialization
        this.test('Main app initialized', () => {
            return window.app !== undefined || window.ElashrafyCV !== undefined;
        });
        
        // Test CSS variables
        this.test('CSS variables loaded', () => {
            const style = getComputedStyle(document.documentElement);
            return style.getPropertyValue('--primary-color').trim() !== '';
        });
        
        // Test responsive design
        this.test('Responsive design works', () => {
            const viewport = document.querySelector('meta[name="viewport"]');
            return viewport && viewport.content.includes('width=device-width');
        });
    }
    
    async testCVBuilder() {
        console.log('🏗️ اختبار منشئ السيرة الذاتية...');
        
        // Test CV Builder initialization
        this.test('CV Builder initialized', () => {
            return window.cvBuilder !== undefined;
        });
        
        // Test data structure
        this.test('CV data structure exists', () => {
            return window.cvBuilder && 
                   window.cvBuilder.cvData && 
                   window.cvBuilder.cvData.personal !== undefined;
        });
        
        // Test section switching
        this.test('Section switching works', () => {
            if (window.cvBuilder) {
                const originalSection = window.cvBuilder.currentSection;
                window.cvBuilder.switchSection('summary');
                const switched = window.cvBuilder.currentSection === 'summary';
                window.cvBuilder.switchSection(originalSection);
                return switched;
            }
            return false;
        });
        
        // Test progress calculation
        this.test('Progress calculation works', () => {
            if (window.cvBuilder) {
                const progressElement = document.querySelector('.progress-text');
                return progressElement && progressElement.textContent.includes('%');
            }
            return false;
        });
    }
    
    async testTemplates() {
        console.log('🎨 اختبار القوالب...');
        
        // Test templates manager
        this.test('Templates manager initialized', () => {
            return window.templatesManager !== undefined || 
                   (window.location.pathname.includes('templates.html') && 
                    document.querySelector('.templates-grid') !== null);
        });
        
        // Test template filtering
        this.test('Template filtering works', () => {
            const filterButtons = document.querySelectorAll('.filter-btn');
            return filterButtons.length > 0;
        });
        
        // Test template preview
        this.test('Template preview modal exists', () => {
            return document.getElementById('templatePreviewModal') !== null ||
                   typeof previewTemplate === 'function';
        });
    }
    
    async testAIAssistant() {
        console.log('🤖 اختبار المساعد الذكي...');
        
        // Test AI Assistant initialization
        this.test('AI Assistant initialized', () => {
            return window.aiAssistant !== undefined;
        });
        
        // Test AI suggestions
        this.test('AI suggestions work', () => {
            if (window.aiAssistant) {
                const suggestion = window.aiAssistant.generateAIResponse('ملخص');
                return suggestion && suggestion.length > 0;
            }
            return false;
        });
        
        // Test skill suggestions
        this.test('Skill suggestions work', () => {
            if (window.aiAssistant) {
                const skills = window.aiAssistant.suggestSkills('مطور');
                return Array.isArray(skills) && skills.length > 0;
            }
            return false;
        });
    }
    
    async testExportFunctions() {
        console.log('📤 اختبار وظائف التصدير...');
        
        // Test export functions exist
        this.test('Export functions exist', () => {
            return typeof exportCV === 'function' &&
                   typeof exportToPDF === 'function' &&
                   typeof exportToImage === 'function';
        });
        
        // Test QR code generation
        this.test('QR code generation available', () => {
            return typeof QRCode !== 'undefined' || 
                   typeof generateQRCode === 'function';
        });
        
        // Test PDF library
        this.test('PDF library loaded', () => {
            return typeof jsPDF !== 'undefined' || 
                   window.jsPDF !== undefined;
        });
        
        // Test html2canvas library
        this.test('html2canvas library loaded', () => {
            return typeof html2canvas !== 'undefined';
        });
    }
    
    async testLocalStorage() {
        console.log('💾 اختبار التخزين المحلي...');
        
        // Test localStorage availability
        this.test('localStorage available', () => {
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                return true;
            } catch (e) {
                return false;
            }
        });
        
        // Test CV data saving
        this.test('CV data saving works', () => {
            if (window.cvBuilder) {
                const testData = { test: 'data' };
                window.cvBuilder.cvData.test = testData;
                window.cvBuilder.saveToLocalStorage();
                
                const saved = localStorage.getItem('elashrafy_cv_data');
                const parsed = JSON.parse(saved);
                
                delete window.cvBuilder.cvData.test;
                return parsed && parsed.test && parsed.test.test === 'data';
            }
            return false;
        });
    }
    
    test(name, testFunction) {
        try {
            const result = testFunction();
            this.testResults.push({
                name,
                passed: result,
                error: null
            });
            
            if (result) {
                console.log(`✅ ${name}`);
            } else {
                console.log(`❌ ${name}`);
            }
        } catch (error) {
            this.testResults.push({
                name,
                passed: false,
                error: error.message
            });
            console.log(`❌ ${name} - خطأ: ${error.message}`);
        }
    }
    
    displayResults() {
        const passed = this.testResults.filter(test => test.passed).length;
        const total = this.testResults.length;
        const percentage = Math.round((passed / total) * 100);
        
        console.log('\n📊 نتائج الاختبار:');
        console.log(`✅ نجح: ${passed}/${total} (${percentage}%)`);
        console.log(`❌ فشل: ${total - passed}/${total}`);
        
        if (percentage >= 80) {
            console.log('🎉 ممتاز! التطبيق يعمل بشكل جيد');
        } else if (percentage >= 60) {
            console.log('⚠️ جيد، لكن يحتاج بعض التحسينات');
        } else {
            console.log('🚨 يحتاج إلى إصلاحات مهمة');
        }
        
        // Show failed tests
        const failedTests = this.testResults.filter(test => !test.passed);
        if (failedTests.length > 0) {
            console.log('\n❌ الاختبارات الفاشلة:');
            failedTests.forEach(test => {
                console.log(`- ${test.name}${test.error ? ` (${test.error})` : ''}`);
            });
        }
        
        // Create visual report
        this.createVisualReport(percentage, passed, total);
    }
    
    createVisualReport(percentage, passed, total) {
        // Only create visual report if we're on a page with a body
        if (!document.body) return;
        
        const report = document.createElement('div');
        report.id = 'test-report';
        report.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid ${percentage >= 80 ? '#10b981' : percentage >= 60 ? '#f59e0b' : '#ef4444'};
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 10000;
            font-family: 'Cairo', sans-serif;
            max-width: 300px;
            direction: rtl;
        `;
        
        report.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                <span style="font-size: 1.5rem; margin-left: 10px;">
                    ${percentage >= 80 ? '🎉' : percentage >= 60 ? '⚠️' : '🚨'}
                </span>
                <strong>تقرير الاختبار</strong>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="margin-right: auto; background: none; border: none; font-size: 1.2rem; cursor: pointer;">
                    ×
                </button>
            </div>
            <div style="margin-bottom: 10px;">
                <div style="background: #f3f4f6; border-radius: 4px; height: 8px; overflow: hidden;">
                    <div style="background: ${percentage >= 80 ? '#10b981' : percentage >= 60 ? '#f59e0b' : '#ef4444'}; 
                                height: 100%; width: ${percentage}%; transition: width 0.3s ease;"></div>
                </div>
            </div>
            <div style="font-size: 0.9rem; color: #6b7280;">
                نجح ${passed} من ${total} اختبار (${percentage}%)
            </div>
        `;
        
        document.body.appendChild(report);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (report.parentElement) {
                report.remove();
            }
        }, 10000);
    }
}

// Auto-run tests when page loads (only in development)
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            new CVTester();
        }, 3000); // Wait 3 seconds for everything to load
    });
}

// Manual test function
window.runTests = () => {
    new CVTester();
};

// Export for use in other modules
window.CVTester = CVTester;
