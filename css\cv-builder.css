/* ===== CV Builder Styles ===== */

.cv-builder-page {
    background: var(--bg-secondary);
    min-height: 100vh;
}

.cv-builder-nav {
    background: var(--bg-card);
    border-bottom: 1px solid var(--bg-secondary);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.cv-builder-nav .nav-logo a {
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.cv-builder-container {
    display: grid;
    grid-template-columns: 300px 1fr 350px;
    height: calc(100vh - 70px);
    overflow: hidden;
}

/* ===== Sidebar Styles ===== */
.cv-sidebar {
    background: var(--bg-card);
    border-left: 1px solid var(--bg-secondary);
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.sidebar-header h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.progress-indicator {
    margin-bottom: var(--spacing-xl);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-align: center;
    margin-top: var(--spacing-xs);
    font-weight: 500;
}

.sidebar-sections {
    margin-bottom: var(--spacing-xl);
}

.section-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    margin-bottom: var(--spacing-sm);
}

.section-item:hover {
    background: var(--bg-secondary);
}

.section-item.active {
    background: var(--primary-color);
    color: var(--text-white);
}

.section-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    color: var(--primary-color);
    font-size: 1.125rem;
}

.section-item.active .section-icon {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-white);
}

.section-info {
    flex: 1;
}

.section-info h4 {
    font-size: 1rem;
    margin-bottom: var(--spacing-xs);
}

.section-info p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.section-item.active .section-info p {
    color: rgba(255, 255, 255, 0.8);
}

.section-status {
    font-size: 1.125rem;
}

.section-indicator {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--bg-primary);
    border: 2px solid var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.section-item.completed .section-indicator {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.section-item.active .section-indicator {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.section-status .fa-check-circle {
    color: var(--accent-color);
}

.section-status .fa-circle {
    color: var(--text-light);
}

/* ===== AI Assistant ===== */
.ai-assistant {
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    color: var(--text-white);
}

.ai-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.ai-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

.ai-info h4 {
    margin-bottom: var(--spacing-xs);
    font-size: 1rem;
}

.ai-status {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
}

.ai-suggestions {
    margin-bottom: var(--spacing-md);
}

.suggestion-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    line-height: 1.4;
}

.btn-ai {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-white);
    border: 1px solid rgba(255, 255, 255, 0.3);
    width: 100%;
}

.btn-ai:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ===== Main Editor ===== */
.cv-editor {
    padding: var(--spacing-xl);
    overflow-y: auto;
    background: var(--bg-primary);
}

.editor-section {
    max-width: 800px;
    margin: 0 auto;
}

.section-header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.section-header h2 {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.section-header p {
    color: var(--text-secondary);
    font-size: 1.125rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--bg-secondary);
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-size: 1rem;
    transition: var(--transition-fast);
    background: var(--bg-card);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* ===== Photo Upload ===== */
.photo-upload-section {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.photo-upload-section h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.photo-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
}

.photo-preview {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--text-light);
    border: 3px dashed var(--bg-secondary);
    transition: var(--transition-normal);
    overflow: hidden;
}

.photo-preview:hover {
    border-color: var(--primary-color);
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.upload-controls {
    display: flex;
    gap: var(--spacing-md);
}

/* ===== Section Actions ===== */
.section-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--bg-secondary);
}

/* ===== CV Preview Panel ===== */
.cv-preview-panel {
    background: var(--bg-card);
    border-right: 1px solid var(--bg-secondary);
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--bg-secondary);
}

.preview-header h3 {
    font-size: 1.125rem;
    color: var(--text-primary);
}

.preview-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.zoom-level {
    font-size: 0.875rem;
    color: var(--text-secondary);
    min-width: 40px;
    text-align: center;
}

.preview-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    min-height: 500px;
}

.cv-preview-document {
    background: var(--bg-card);
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-lg);
    transform-origin: top center;
    transition: transform 0.3s ease;
}

.cv-page {
    padding: var(--spacing-xl);
    min-height: 600px;
}

/* ===== CV Preview Content ===== */
.cv-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--primary-color);
}

.cv-photo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--text-light);
    overflow: hidden;
}

.cv-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cv-basic-info {
    flex: 1;
}

.cv-name {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.cv-title {
    font-size: 1.25rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.cv-contact {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.contact-item i {
    color: var(--primary-color);
}

.cv-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.cv-section h3 {
    font-size: 1.25rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--bg-secondary);
}

/* ===== Experience/Education/Language Items ===== */
.experience-item,
.education-item,
.language-item {
    background: var(--bg-card);
    border: 2px solid var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    transition: var(--transition-normal);
}

.experience-item:hover,
.education-item:hover,
.language-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--bg-secondary);
}

.item-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.125rem;
}

.btn-remove {
    background: var(--danger-color);
    color: var(--text-white);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

.btn-remove:hover {
    background: #dc2626;
    transform: scale(1.1);
}

.btn-add {
    width: 100%;
    margin-bottom: var(--spacing-lg);
    border: 2px dashed var(--primary-color);
    background: transparent;
    color: var(--primary-color);
    padding: var(--spacing-lg);
    font-size: 1rem;
    transition: var(--transition-normal);
}

.btn-add:hover {
    background: var(--primary-color);
    color: var(--text-white);
    border-style: solid;
}

.full-width {
    grid-column: 1 / -1;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* ===== Skills Section ===== */
.skills-categories {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.skill-category h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    font-size: 1.125rem;
}

.skills-input {
    margin-bottom: var(--spacing-lg);
}

.skills-input input {
    width: 100%;
    margin-bottom: var(--spacing-md);
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    min-height: 60px;
    padding: var(--spacing-md);
    border: 2px dashed var(--bg-secondary);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
}

.skill-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
}

.skill-remove {
    background: none;
    border: none;
    color: var(--text-white);
    cursor: pointer;
    padding: 2px;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.skill-remove:hover {
    background: rgba(255, 255, 255, 0.2);
}

.ai-suggestions-box {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.ai-suggestions-box h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.suggestion-btn {
    background: var(--bg-card);
    border: 1px solid var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: right;
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.suggestion-btn:hover {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.suggested-skills {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.skill-suggestion {
    background: var(--bg-card);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.skill-suggestion:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

/* ===== Character Count ===== */
.character-count {
    text-align: left;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

/* ===== Completion Modal ===== */
.completion-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
}

/* ===== Enhanced CV Preview ===== */
.cv-content .cv-section {
    margin-bottom: var(--spacing-xl);
}

.cv-section .experience-list,
.cv-section .education-list,
.cv-section .skills-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.cv-experience-item,
.cv-education-item {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    border-right: 3px solid var(--primary-color);
}

.cv-experience-item h4,
.cv-education-item h4 {
    font-size: 1rem;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
    font-weight: 600;
}

.cv-experience-item .company,
.cv-education-item .institution {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.cv-experience-item .duration,
.cv-education-item .duration {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    font-style: italic;
}

.cv-experience-item .description,
.cv-education-item .description {
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-secondary);
}

.cv-skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
}

.cv-skill-item {
    background: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    text-align: center;
    font-size: 0.875rem;
    font-weight: 500;
}

.cv-skill-item.soft-skill {
    background: var(--secondary-color);
}

/* ===== Template Specific Styles ===== */
.modern-header {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.classic-header {
    border-bottom: 3px solid var(--primary-color);
    padding: var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.classic-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--primary-color);
}

.creative-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    min-height: 100%;
}

.creative-sidebar {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: var(--spacing-xl);
}

.creative-main {
    padding: var(--spacing-xl);
}

.creative-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin: 0 auto var(--spacing-lg);
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.creative-sidebar .section-title {
    color: var(--text-white);
    border-bottom-color: rgba(255, 255, 255, 0.3);
}

.summary-text {
    line-height: 1.6;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.languages-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.cv-language-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.creative-sidebar .cv-language-item {
    background: rgba(255, 255, 255, 0.1);
}

.language-name {
    font-weight: 500;
}

.language-level {
    font-size: 0.875rem;
    color: var(--text-secondary);
    background: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.creative-sidebar .language-level {
    background: rgba(255, 255, 255, 0.2);
}

/* ===== Export Loading ===== */
.export-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: var(--text-white);
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--text-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Export Modal ===== */
.export-modal {
    max-width: 600px;
}

.export-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.export-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--bg-secondary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    background: var(--bg-card);
}

.export-option:hover {
    border-color: var(--primary-color);
    background: var(--bg-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.export-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.export-info {
    flex: 1;
}

.export-info h4 {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.export-info p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.export-arrow {
    color: var(--text-secondary);
    font-size: 1.125rem;
    transition: var(--transition-fast);
}

.export-option:hover .export-arrow {
    color: var(--primary-color);
    transform: translateX(-3px);
}

/* ===== Responsive Design ===== */
@media (max-width: 1200px) {
    .cv-builder-container {
        grid-template-columns: 280px 1fr 300px;
    }
}

@media (max-width: 768px) {
    .cv-builder-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }

    .cv-sidebar {
        order: 2;
        height: auto;
        max-height: 300px;
    }

    .cv-preview-panel {
        display: none;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .skills-categories {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .completion-actions {
        flex-direction: column;
    }

    .cv-skills-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    /* Enhanced Export Modal Mobile */
    .export-modal-content {
        width: 95%;
        margin: 20px;
    }

    .export-options-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .export-option-card {
        padding: 16px;
    }

    .option-icon-wrapper {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }

    .option-content h4 {
        font-size: 1rem;
    }

    .option-content p {
        font-size: 0.85rem;
    }

    .feature-tag {
        font-size: 0.7rem;
        padding: 3px 6px;
    }
}

/* ===== Enhanced Export Modal Styles ===== */
.export-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.export-modal-overlay.modal-show {
    opacity: 1;
    visibility: visible;
}

.export-modal-overlay.modal-hide {
    opacity: 0;
    visibility: hidden;
}

.export-modal-content {
    background: var(--bg-card);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 650px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
}

.export-modal-overlay.modal-show .export-modal-content {
    transform: scale(1) translateY(0);
}

.export-modal-header {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.export-header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.export-icon-main {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.export-title-section h3 {
    margin: 0 0 4px 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.export-title-section p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.export-close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--text-white);
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all 0.2s ease;
}

.export-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.export-modal-body {
    padding: 24px;
}

.export-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.export-option-card {
    background: var(--bg-secondary);
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    overflow: hidden;
}

.export-option-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent 0%, rgba(30, 64, 175, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.export-option-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.15);
}

.export-option-card:hover::before {
    opacity: 1;
}

.option-icon-wrapper {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.pdf-icon {
    background: linear-gradient(135deg, #dc2626, #ef4444);
    color: white;
}

.png-icon {
    background: linear-gradient(135deg, #059669, #10b981);
    color: white;
}

.jpg-icon {
    background: linear-gradient(135deg, #7c3aed, #8b5cf6);
    color: white;
}

.qr-icon {
    background: linear-gradient(135deg, #ea580c, #f97316);
    color: white;
}

.export-option-card:hover .option-icon-wrapper {
    transform: scale(1.1);
}

.option-content {
    flex: 1;
}

.option-content h4 {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.option-content p {
    margin: 0 0 12px 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.option-features {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.feature-tag {
    background: var(--primary-color);
    color: var(--text-white);
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.option-action {
    color: var(--text-secondary);
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.export-option-card:hover .option-action {
    color: var(--primary-color);
    transform: translateX(-4px);
}

.export-tips-section {
    background: var(--bg-primary);
    border: 1px solid var(--primary-color);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.tip-icon {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-top: 2px;
}

.tip-content {
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-primary);
}

.tip-content strong {
    color: var(--primary-color);
}

/* ===== Preview Modal Styles ===== */
.preview-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.preview-modal-overlay.modal-show {
    opacity: 1;
    visibility: visible;
}

.preview-modal-overlay.modal-hide {
    opacity: 0;
    visibility: hidden;
}

.preview-modal-content {
    background: var(--bg-card);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
    width: 95%;
    max-width: 1000px;
    height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.preview-modal-overlay.modal-show .preview-modal-content {
    transform: scale(1) translateY(0);
}

.preview-modal-header {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: 20px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.preview-header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.preview-icon-main {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
}

.preview-title-section h3 {
    margin: 0 0 4px 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.preview-title-section p {
    margin: 0;
    font-size: 0.85rem;
    opacity: 0.9;
}

.preview-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.preview-control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--text-white);
    width: 36px;
    height: 36px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.preview-control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.zoom-level {
    font-size: 0.85rem;
    font-weight: 500;
    min-width: 45px;
    text-align: center;
}

.preview-close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--text-white);
    width: 36px;
    height: 36px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    transition: all 0.2s ease;
    margin-right: 8px;
}

.preview-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.preview-modal-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.preview-container {
    flex: 1;
    overflow: auto;
    padding: 24px;
    background: var(--bg-secondary);
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.cv-preview-document {
    background: var(--bg-card);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transform-origin: top center;
    transition: transform 0.3s ease;
    max-width: 800px;
    width: 100%;
}

.cv-page.full-preview {
    padding: 40px;
    min-height: auto;
    font-size: 14px;
    line-height: 1.5;
}

.full-preview .cv-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
}

.full-preview .cv-name {
    font-size: 2.2rem;
    margin-bottom: 8px;
}

.full-preview .cv-title {
    font-size: 1.3rem;
    margin-bottom: 16px;
}

.full-preview .cv-contact {
    gap: 16px;
}

.full-preview .contact-item {
    font-size: 0.9rem;
}

.full-preview .cv-section {
    margin-bottom: 28px;
}

.full-preview .section-title {
    font-size: 1.3rem;
    margin-bottom: 16px;
    padding-bottom: 8px;
}

.full-preview .summary-text {
    font-size: 0.95rem;
    line-height: 1.6;
}

.full-preview .cv-experience-item,
.full-preview .cv-education-item {
    margin-bottom: 20px;
    padding: 16px;
}

.full-preview .cv-experience-item h4,
.full-preview .cv-education-item h4 {
    font-size: 1.1rem;
    margin-bottom: 6px;
}

.full-preview .company,
.full-preview .institution {
    font-size: 0.95rem;
    margin-bottom: 4px;
}

.full-preview .duration {
    font-size: 0.85rem;
    margin-bottom: 8px;
}

.full-preview .description {
    font-size: 0.9rem;
    line-height: 1.5;
}

.full-preview .cv-skills-grid {
    gap: 8px;
}

.full-preview .cv-skill-item {
    font-size: 0.85rem;
    padding: 6px 12px;
}

.full-preview .cv-language-item {
    padding: 8px 12px;
    margin-bottom: 6px;
}

.preview-actions {
    padding: 20px 24px;
    background: var(--bg-primary);
    border-top: 1px solid var(--bg-secondary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    flex-shrink: 0;
}

.preview-actions .btn {
    min-width: 140px;
}

/* Mobile Preview Modal */
@media (max-width: 768px) {
    .preview-modal-content {
        width: 100%;
        height: 100vh;
        border-radius: 0;
    }

    .preview-modal-header {
        padding: 16px 20px;
    }

    .preview-header-content {
        gap: 12px;
    }

    .preview-icon-main {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .preview-title-section h3 {
        font-size: 1.2rem;
    }

    .preview-title-section p {
        font-size: 0.8rem;
    }

    .preview-controls {
        gap: 8px;
    }

    .preview-control-btn,
    .preview-close-btn {
        width: 32px;
        height: 32px;
        font-size: 0.85rem;
    }

    .zoom-level {
        font-size: 0.8rem;
        min-width: 40px;
    }

    .preview-container {
        padding: 16px;
    }

    .cv-page.full-preview {
        padding: 24px;
        font-size: 13px;
    }

    .full-preview .cv-name {
        font-size: 1.8rem;
    }

    .full-preview .cv-title {
        font-size: 1.1rem;
    }

    .preview-actions {
        padding: 16px 20px;
        flex-direction: column;
    }

    .preview-actions .btn {
        width: 100%;
        min-width: auto;
    }
}

/* ===== Enhanced Summary Section Styles ===== */
.summary-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    font-size: 0.85rem;
}

.character-count,
.word-count {
    color: var(--text-secondary);
}

.quality-indicator {
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.quality-poor {
    background: var(--danger-color);
    color: white;
}

.quality-fair {
    background: var(--warning-color);
    color: white;
}

.quality-good {
    background: var(--success-color);
    color: white;
}

.quality-excellent {
    background: var(--primary-color);
    color: white;
}

.summary-help {
    background: var(--bg-primary);
    border: 1px solid var(--primary-color);
    border-radius: 8px;
    padding: 16px;
    margin-top: 12px;
    animation: slideInDown 0.3s ease-out;
}

.help-content h4 {
    margin: 0 0 12px 0;
    color: var(--primary-color);
    font-size: 1rem;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.help-item {
    font-size: 0.85rem;
    line-height: 1.4;
}

.help-item strong {
    color: var(--primary-color);
}

.suggestions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
}

.suggestion-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    font-size: 0.85rem;
    text-align: center;
}

.suggestion-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.suggestion-btn i {
    font-size: 1.2rem;
}

.summary-examples {
    margin-top: 20px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: 8px;
}

.summary-examples h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.examples-carousel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
}

.example-item {
    background: var(--bg-card);
    border: 1px solid var(--bg-secondary);
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.example-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.15);
}

.example-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 6px;
    font-size: 0.9rem;
}

.example-text {
    font-size: 0.8rem;
    line-height: 1.4;
    color: var(--text-secondary);
}

/* ===== Analysis Modal Styles ===== */
.analysis-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.analysis-modal-overlay.modal-show {
    opacity: 1;
    visibility: visible;
}

.analysis-modal-content {
    background: var(--bg-card);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
}

.analysis-modal-overlay.modal-show .analysis-modal-content {
    transform: scale(1) translateY(0);
}

.analysis-header {
    background: var(--gradient-primary);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.analysis-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.analysis-header button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.analysis-body {
    padding: 24px;
}

.score-section {
    text-align: center;
    margin-bottom: 24px;
}

.score-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg, var(--primary-color) calc(var(--score, 0) * 3.6deg), var(--bg-secondary) calc(var(--score, 0) * 3.6deg));
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    position: relative;
}

.score-circle::before {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    background: var(--bg-card);
    border-radius: 50%;
}

.score-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    z-index: 1;
}

.score-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    z-index: 1;
}

.score-description {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
}

.feedback-section h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
}

.feedback-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feedback-section li {
    padding: 6px 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.stats-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--bg-secondary);
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .summary-stats {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .help-grid {
        grid-template-columns: 1fr;
    }

    .suggestions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .examples-carousel {
        grid-template-columns: 1fr;
    }

    .analysis-modal-content {
        width: 95%;
        margin: 20px;
    }

    .stats-section {
        grid-template-columns: 1fr;
    }
}

/* ===== Enhanced Form Styles ===== */
.items-list {
    margin-bottom: 20px;
}

.experience-item,
.education-item,
.language-item {
    background: var(--bg-secondary);
    border: 1px solid var(--bg-secondary);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
}

.experience-item:hover,
.education-item:hover,
.language-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(30, 64, 175, 0.1);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--bg-primary);
}

.item-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.btn-remove {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.btn-remove:hover {
    background: var(--danger-dark);
    transform: scale(1.05);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.checkmark {
    width: 16px;
    height: 16px;
    border: 2px solid var(--primary-color);
    border-radius: 3px;
    position: relative;
    margin-left: 8px;
}

.btn-add {
    width: 100%;
    padding: 12px;
    border: 2px dashed var(--primary-color);
    background: transparent;
    color: var(--primary-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.btn-add:hover {
    background: var(--primary-color);
    color: white;
    border-style: solid;
}

/* Skills Section Styles */
.skills-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.skill-category {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 8px;
}

.skill-category h3 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.skill-input-group {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.skill-input-group input {
    flex: 1;
}

.skill-input-group .btn {
    padding: 8px 16px;
    font-size: 0.85rem;
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.skill-tag {
    background: var(--primary-color);
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.skill-tag.soft {
    background: var(--success-color);
}

.skill-tag button {
    background: rgba(255, 255, 255, 0.3);
    border: none;
    color: white;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    transition: all 0.2s ease;
}

.skill-tag button:hover {
    background: rgba(255, 255, 255, 0.5);
}

.character-count {
    text-align: left;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 4px;
}

.field-error {
    color: var(--danger-color);
    font-size: 0.8rem;
    margin-top: 4px;
}

.form-group input.error,
.form-group textarea.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

/* Mobile Responsive for Forms */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .skills-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .skill-input-group {
        flex-direction: column;
    }

    .experience-item,
    .education-item,
    .language-item {
        padding: 16px;
    }

    .item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* ===== Modal Styles ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-card);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.modal-header button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.modal-header button:hover {
    background: rgba(255, 255, 255, 0.3);
}

.modal-body {
    padding: 0;
    max-height: 60vh;
    overflow-y: auto;
}

/* AI Chat Styles */
.ai-chat-container {
    display: flex;
    flex-direction: column;
    height: 500px;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: var(--bg-primary);
}

.ai-message,
.user-message {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.message-avatar.user {
    background: var(--success-color);
}

.message-content {
    flex: 1;
    background: var(--bg-card);
    padding: 12px 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-content.user {
    background: var(--success-color);
    color: white;
}

.message-content p {
    margin: 0 0 12px 0;
    line-height: 1.5;
    color: var(--text-primary);
}

.quick-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.quick-suggestions button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-suggestions button:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.chat-input {
    display: flex;
    padding: 16px 20px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--bg-primary);
    gap: 12px;
}

.chat-input input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--bg-primary);
    border-radius: 24px;
    background: var(--bg-card);
    color: var(--text-primary);
    font-family: 'Cairo', sans-serif;
    outline: none;
    transition: all 0.2s ease;
}

.chat-input input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(30, 64, 175, 0.1);
}

.chat-input button {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.chat-input button:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    z-index: 10001;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
    max-width: 400px;
}

.notification-success {
    background: var(--success-color);
}

.notification-error {
    background: var(--danger-color);
}

.notification-warning {
    background: var(--warning-color);
}

.notification-info {
    background: var(--info-color);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Mobile Responsive for Modals */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 20px;
        max-height: 90vh;
    }

    .ai-chat-container {
        height: 400px;
    }

    .chat-messages {
        padding: 16px;
    }

    .quick-suggestions {
        flex-direction: column;
    }

    .quick-suggestions button {
        width: 100%;
        text-align: center;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* ===== Export Modal ===== */
.export-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease-out;
}

.export-modal {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: slideUp 0.3s ease-out;
}

.export-modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.export-modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.export-modal-content {
    padding: var(--spacing-lg);
}

.export-options {
    display: grid;
    gap: var(--spacing-md);
}

.export-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
}

.export-option:hover {
    border-color: var(--primary-color);
    background: var(--primary-color-light);
    transform: translateY(-2px);
}

.export-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.export-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.export-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* ===== Preview Modal ===== */
.preview-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    z-index: 10000;
    animation: fadeIn 0.3s ease-out;
}

.preview-modal {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
}

.preview-modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-card);
}

.preview-modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.preview-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.preview-modal-content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow: auto;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.preview-modal-content .preview-container {
    background: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-xl);
    max-width: 800px;
    width: 100%;
    transform-origin: top center;
    transition: transform 0.3s ease;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}
