/* ===== CSS Variables ===== */
:root {
    /* Colors */
    --primary-color: #1e40af;
    --primary-dark: #1e3a8a;
    --primary-light: #3b82f6;
    --primary-lighter: #60a5fa;
    --secondary-color: #f59e0b;
    --accent-color: #10b981;
    --danger-color: #ef4444;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    --gradient-primary-light: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
    --gradient-secondary: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-hero: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
    
    /* Text Colors */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --text-white: #ffffff;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-dark: #111827;
    --bg-card: #ffffff;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Fonts */
    --font-primary: 'Cairo', sans-serif;
    --font-secondary: 'Amiri', serif;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

/* ===== Reset & Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
    direction: rtl;
    text-align: right;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* ===== Utility Classes ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* ===== Buttons ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    white-space: nowrap;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
}

/* ===== Loading Screen ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease-in-out;
}

.loading-content {
    text-align: center;
    color: var(--text-white);
}

.logo-animation {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    animation: pulse 2s infinite;
}

.loading-bar {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
    margin: var(--spacing-lg) auto;
    overflow: hidden;
}

.loading-progress {
    height: 100%;
    background: var(--text-white);
    border-radius: var(--radius-sm);
    animation: loading 2s ease-in-out infinite;
}

/* ===== Navigation ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: var(--z-fixed);
    transition: var(--transition-normal);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

/* ===== Hero Section ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #f8fafc 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: var(--gradient-primary);
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    bottom: 20%;
    left: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 60%;
    animation-delay: 4s;
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    padding-top: 80px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
}

.hero-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.feature-item i {
    color: var(--primary-color);
    font-size: 1.125rem;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* ===== Animations ===== */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes loading {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(200px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* ===== Templates Section ===== */
.templates-preview {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-primary);
}

.template-categories {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.category-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 2px solid var(--primary-color);
    background: transparent;
    color: var(--primary-color);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
}

.category-btn.active,
.category-btn:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.template-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.template-preview {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.template-image {
    width: 100%;
    height: 100%;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-placeholder {
    font-size: 4rem;
    color: var(--text-light);
}

.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    opacity: 0;
    transition: var(--transition-normal);
}

.template-card:hover .template-overlay {
    opacity: 1;
}

.template-info {
    padding: var(--spacing-lg);
}

.template-info h4 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-sm);
}

.template-info p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.template-tags {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.templates-actions {
    text-align: center;
}

/* ===== Pricing Section ===== */
.pricing {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-secondary);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    position: relative;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-header h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.currency {
    font-size: 1rem;
    color: var(--text-secondary);
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.period {
    font-size: 1rem;
    color: var(--text-secondary);
}

.pricing-features {
    margin: var(--spacing-xl) 0;
}

.pricing-features ul {
    list-style: none;
    text-align: right;
}

.pricing-features li {
    padding: var(--spacing-sm) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.pricing-features .fas.fa-check {
    color: var(--accent-color);
}

.pricing-features .fas.fa-times {
    color: var(--text-light);
}

/* ===== Statistics Section ===== */
.statistics {
    padding: var(--spacing-2xl) 0;
    background: var(--gradient-primary);
    color: var(--text-white);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.8;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: 1.125rem;
    opacity: 0.9;
}

/* ===== Contact Section ===== */
.contact {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-primary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: 1.5rem;
}

.contact-details h4 {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xs);
}

.contact-details p {
    color: var(--text-secondary);
    margin: 0;
}

/* ===== Forms ===== */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--bg-secondary);
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-size: 1rem;
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* ===== Footer ===== */
.footer {
    background: var(--bg-dark);
    color: var(--text-white);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.footer-section h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-white);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--text-white);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    transition: var(--transition-normal);
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
}

/* ===== Modal Styles ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    animation: fadeIn 0.3s ease-out forwards;
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    animation: modalSlideIn 0.3s ease-out forwards;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--bg-secondary);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 50%;
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

/* ===== AI Chat Modal ===== */
.ai-chat-modal .modal-content {
    max-width: 600px;
    height: 70vh;
    display: flex;
    flex-direction: column;
}

.ai-chat-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.ai-message,
.user-message {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    max-width: 80%;
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.ai-message .message-avatar {
    background: var(--gradient-primary);
    color: var(--text-white);
}

.user-message .message-avatar {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.message-content {
    background: var(--bg-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    line-height: 1.5;
}

.user-message .message-content {
    background: var(--primary-color);
    color: var(--text-white);
}

.chat-input-container {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--bg-secondary);
}

.chat-input-container input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--bg-secondary);
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
}

.quick-actions {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--bg-secondary);
    background: var(--bg-secondary);
}

.quick-actions h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.quick-action-btn {
    display: block;
    width: 100%;
    text-align: right;
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-card);
    border: 1px solid var(--bg-secondary);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.875rem;
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

/* ===== Notification Styles ===== */
.notification {
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--bg-card);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md);
    z-index: var(--z-tooltip);
    min-width: 300px;
    animation: slideInLeft 0.3s ease-out;
}

.notification-success {
    border-right: 4px solid var(--accent-color);
}

.notification-error {
    border-right: 4px solid var(--danger-color);
}

.notification-warning {
    border-right: 4px solid var(--secondary-color);
}

.notification-info {
    border-right: 4px solid var(--primary-color);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    margin-right: auto;
    padding: var(--spacing-xs);
}

/* ===== Page Transition ===== */
.page-transition-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: var(--text-white);
}

.transition-content {
    text-align: center;
}

.transition-logo {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    animation: pulse 1s infinite;
}

.transition-text {
    font-size: 1.125rem;
    font-weight: 500;
}

/* ===== Animations ===== */
@keyframes fadeIn {
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    to { transform: scale(1); }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== AI Floating Assistant ===== */
.ai-floating-assistant {
    position: fixed;
    bottom: 30px;
    left: 30px;
    z-index: var(--z-modal);
}

.ai-toggle-btn {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-xl);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.ai-toggle-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
}

.ai-pulse {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--primary-color);
    animation: aiPulse 2s infinite;
    opacity: 0.3;
}

@keyframes aiPulse {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.1;
    }
    100% {
        transform: scale(1.4);
        opacity: 0;
    }
}

.ai-chat-window {
    position: absolute;
    bottom: 80px;
    left: 0;
    width: 350px;
    height: 500px;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: slideInUp 0.3s ease-out;
}

.ai-chat-header {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.ai-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

.ai-info {
    flex: 1;
}

.ai-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 1rem;
}

.ai-status {
    margin: 0;
    font-size: 0.875rem;
    opacity: 0.9;
}

.ai-close-btn {
    background: none;
    border: none;
    color: var(--text-white);
    font-size: 1.125rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 50%;
    transition: var(--transition-fast);
}

.ai-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.ai-chat-messages {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
    background: var(--bg-primary);
}

.ai-message {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.message-avatar {
    width: 30px;
    height: 30px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: 0.875rem;
    flex-shrink: 0;
}

.message-content {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    line-height: 1.5;
    font-size: 0.875rem;
    max-width: 250px;
}

.message-content ul {
    margin: var(--spacing-sm) 0 0 0;
    padding-right: var(--spacing-lg);
}

.message-content li {
    margin-bottom: var(--spacing-xs);
}

.ai-quick-actions {
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.quick-action-btn {
    background: var(--bg-card);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

.ai-chat-input {
    padding: var(--spacing-md);
    background: var(--bg-card);
    display: flex;
    gap: var(--spacing-sm);
    border-top: 1px solid var(--bg-secondary);
}

.ai-chat-input input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--bg-secondary);
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-size: 0.875rem;
}

.ai-chat-input button {
    background: var(--primary-color);
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
}

.ai-chat-input button:hover {
    background: var(--primary-dark);
}

.user-message {
    flex-direction: row-reverse;
    margin-bottom: var(--spacing-lg);
}

.user-msg {
    background: var(--primary-color);
    color: var(--text-white);
}

.user-avatar {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.action-btn {
    background: var(--primary-color);
    color: var(--text-white);
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    margin: var(--spacing-xs) var(--spacing-xs) var(--spacing-xs) 0;
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition-fast);
}

.action-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

@keyframes fadeInUp {
    from {
        transform: translateY(10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ===== Progress Indicator ===== */
.progress-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-card);
    padding: 30px;
    border-radius: 12px;
    box-shadow: var(--shadow-xl);
    z-index: 10000;
    min-width: 300px;
    text-align: center;
    animation: fadeIn 0.3s ease-out;
}

.progress-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.progress-icon {
    font-size: 2rem;
    color: var(--primary-color);
}

.progress-text {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-percentage {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* ===== Enhanced Notifications ===== */
.notification {
    animation: slideInRight 0.3s ease-out;
}

.notification.notification-success {
    background: var(--success-color);
    border-left: 4px solid var(--success-dark);
}

.notification.notification-error {
    background: var(--danger-color);
    border-left: 4px solid var(--danger-dark);
}

.notification.notification-warning {
    background: var(--warning-color);
    border-left: 4px solid var(--warning-dark);
}

.notification.notification-info {
    background: var(--primary-color);
    border-left: 4px solid var(--primary-dark);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        right: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--bg-card);
        flex-direction: column;
        justify-content: flex-start;
        padding: var(--spacing-xl);
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        right: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-features {
        grid-template-columns: 1fr;
    }

    .hero-actions {
        justify-content: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .templates-grid {
        grid-template-columns: 1fr;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .modal-content {
        width: 95%;
        max-height: 95vh;
    }

    .ai-chat-modal .modal-content {
        height: 90vh;
    }

    .notification {
        left: 10px;
        right: 10px;
        min-width: auto;
    }

    /* AI Assistant Mobile */
    .ai-floating-assistant {
        bottom: 20px;
        left: 20px;
    }

    .ai-toggle-btn {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .ai-chat-window {
        width: calc(100vw - 40px);
        height: 400px;
        bottom: 70px;
        left: -10px;
    }

    .ai-chat-header {
        padding: var(--spacing-md);
    }

    .ai-chat-messages {
        padding: var(--spacing-md);
    }

    .message-content {
        max-width: 200px;
        font-size: 0.8rem;
    }

    .quick-action-btn {
        font-size: 0.7rem;
        padding: var(--spacing-xs);
    }
}
