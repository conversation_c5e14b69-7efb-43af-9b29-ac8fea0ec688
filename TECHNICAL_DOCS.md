# التوثيق التقني - Elashrafy CV

## نظرة عامة على البنية التقنية

### هيكل الملفات

```
elashrafy-cv/
├── index.html                 # الصفحة الرئيسية
├── cv-builder.html           # صفحة منشئ السيرة الذاتية
├── templates.html            # صفحة عرض القوالب
├── css/
│   ├── main.css             # التنسيق الرئيسي
│   ├── cv-builder.css       # تنسيق منشئ السيرة الذاتية
│   ├── templates.css        # تنسيق صفحة القوالب
│   └── animations.css       # الرسوم المتحركة
├── js/
│   ├── app.js              # التطبيق الرئيسي
│   ├── cv-builder.js       # منشئ السيرة الذاتية
│   ├── cv-preview.js       # معاينة وتصدير السيرة الذاتية
│   ├── ai-assistant.js     # المساعد الذكي
│   ├── templates.js        # إدارة القوالب
│   ├── navigation.js       # التنقل والتمرير
│   └── animations.js       # تحكم الرسوم المتحركة
├── assets/                 # الصور والأيقونات
├── README.md              # التوثيق العام
└── TECHNICAL_DOCS.md      # هذا الملف
```

## الكلاسات الرئيسية

### 1. ElashrafyCV (app.js)
الكلاس الرئيسي للتطبيق الذي يدير:
- تهيئة التطبيق
- إدارة الأحداث العامة
- التنقل بين الصفحات
- عرض الإشعارات

```javascript
class ElashrafyCV {
    constructor() {
        this.currentUser = null;
        this.currentCV = null;
        this.templates = [];
        this.isLoading = false;
    }
    
    init() {
        this.setupEventListeners();
        this.loadTemplates();
        this.handleLoading();
        this.initializeAnimations();
    }
}
```

### 2. CVBuilder (cv-builder.js)
يدير عملية إنشاء السيرة الذاتية:
- التنقل بين الأقسام
- حفظ البيانات
- المعاينة الفورية
- التحقق من صحة البيانات

```javascript
class CVBuilder {
    constructor() {
        this.currentSection = 'personal';
        this.cvData = {
            personal: {},
            summary: '',
            experience: [],
            education: [],
            skills: [],
            languages: []
        };
    }
}
```

### 3. AIAssistant (ai-assistant.js)
المساعد الذكي الذي يقدم:
- اقتراحات تلقائية
- تحليل المحتوى
- قوالب نصية
- دردشة تفاعلية

```javascript
class AIAssistant {
    constructor() {
        this.isActive = false;
        this.chatHistory = [];
        this.suggestions = {};
        this.templates = {};
    }
}
```

### 4. TemplatesManager (templates.js)
إدارة القوالب:
- عرض القوالب
- الفلترة والبحث
- المعاينة
- الاختيار

```javascript
class TemplatesManager {
    constructor() {
        this.templates = [];
        this.filteredTemplates = [];
        this.currentFilter = 'all';
        this.currentSort = 'popular';
    }
}
```

### 5. CVPreview (cv-preview.js)
معاينة وتصدير السيرة الذاتية:
- عرض القوالب المختلفة
- تصدير PDF
- تصدير صور
- إنشاء رموز QR

```javascript
class CVPreview {
    constructor() {
        this.cvData = null;
        this.currentTemplate = 1;
        this.exportOptions = {
            format: 'pdf',
            quality: 'high',
            includeQR: true
        };
    }
}
```

## نظام التصميم

### متغيرات CSS
```css
:root {
    /* الألوان */
    --primary-color: #6366f1;
    --secondary-color: #f59e0b;
    --accent-color: #10b981;
    --danger-color: #ef4444;
    
    /* التدرجات */
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* الخطوط */
    --font-primary: 'Cairo', sans-serif;
    --font-secondary: 'Amiri', serif;
}
```

### نظام الشبكة
- استخدام CSS Grid للتخطيطات المعقدة
- Flexbox للمحاذاة والتوزيع
- تصميم متجاوب مع نقاط توقف متعددة

### الرسوم المتحركة
- استخدام CSS Animations و Transitions
- Intersection Observer للرسوم المتحركة عند التمرير
- دعم `prefers-reduced-motion` لإمكانية الوصول

## إدارة البيانات

### التخزين المحلي
```javascript
// حفظ بيانات السيرة الذاتية
localStorage.setItem('elashrafy_cv_data', JSON.stringify(cvData));

// تحميل البيانات المحفوظة
const savedData = localStorage.getItem('elashrafy_cv_data');
if (savedData) {
    this.cvData = JSON.parse(savedData);
}
```

### هيكل البيانات
```javascript
const cvData = {
    personal: {
        fullName: '',
        jobTitle: '',
        email: '',
        phone: '',
        city: '',
        website: '',
        linkedin: '',
        github: '',
        photo: null
    },
    summary: '',
    experience: [
        {
            jobTitle: '',
            company: '',
            startDate: '',
            endDate: '',
            description: '',
            isCurrent: false
        }
    ],
    education: [
        {
            degree: '',
            institution: '',
            year: '',
            gpa: '',
            details: ''
        }
    ],
    skills: {
        technical: [],
        soft: []
    },
    languages: [
        {
            name: '',
            level: ''
        }
    ]
};
```

## نظام القوالب

### أنواع القوالب
1. **عصري (Modern)**: تصميم حديث بألوان متدرجة
2. **كلاسيكي (Classic)**: تصميم تقليدي أنيق
3. **إبداعي (Creative)**: تصميم ملون للمجالات الفنية
4. **بسيط (Minimal)**: تصميم نظيف ومرتب
5. **مهني (Professional)**: للمناصب الإدارية

### تخصيص القوالب
```javascript
renderModernTemplate(container) {
    container.innerHTML = `
        <div class="cv-header modern-header">
            <!-- محتوى الرأس -->
        </div>
        <div class="cv-content modern-content">
            <!-- محتوى السيرة الذاتية -->
        </div>
    `;
}
```

## نظام التصدير

### تصدير PDF
```javascript
setupPDFExport() {
    window.exportToPDF = () => {
        const element = document.querySelector('.cv-page');
        
        html2canvas(element, {
            scale: 2,
            useCORS: true,
            allowTaint: true
        }).then(canvas => {
            const imgData = canvas.toDataURL('image/png');
            const pdf = new jsPDF('p', 'mm', 'a4');
            // إضافة الصورة إلى PDF
            pdf.addImage(imgData, 'PNG', 0, 0, 210, 297);
            pdf.save('السيرة_الذاتية.pdf');
        });
    };
}
```

### تصدير الصور
```javascript
setupImageExport() {
    window.exportToImage = (format = 'png') => {
        const element = document.querySelector('.cv-page');
        
        html2canvas(element, {
            scale: 2,
            useCORS: true
        }).then(canvas => {
            const link = document.createElement('a');
            link.download = `السيرة_الذاتية.${format}`;
            link.href = canvas.toDataURL(`image/${format}`);
            link.click();
        });
    };
}
```

### إنشاء رموز QR
```javascript
generateQRCode() {
    const qrData = this.createQRData();
    
    QRCode.toCanvas(container, qrData, {
        width: 200,
        margin: 2,
        color: {
            dark: '#000000',
            light: '#FFFFFF'
        }
    });
}

createQRData() {
    const data = this.cvData.personal;
    return `BEGIN:VCARD
VERSION:3.0
FN:${data.fullName || ''}
TITLE:${data.jobTitle || ''}
EMAIL:${data.email || ''}
TEL:${data.phone || ''}
URL:${data.website || ''}
END:VCARD`;
}
```

## المساعد الذكي

### تحليل المحتوى
```javascript
analyzeInput(input) {
    const value = input.value.trim();
    const fieldType = input.id || input.dataset.field;
    
    if (value.length > 10) {
        this.provideSuggestion(fieldType, value);
    }
}

analyzeSummary(summary) {
    const words = summary.split(' ').length;
    const hasExperience = /\d+\s*(سنة|سنوات|عام|أعوام)/.test(summary);
    const hasSkills = /(خبرة|متخصص|مطور|مصمم|مدير)/.test(summary);
    
    if (words < 20) {
        return 'الملخص قصير جداً، أضف المزيد من التفاصيل';
    }
    // المزيد من التحليلات...
}
```

### الاقتراحات التلقائية
```javascript
suggestions = {
    personal: [
        'تأكد من إضافة رقم هاتف صحيح للتواصل',
        'أضف رابط LinkedIn لزيادة فرص التواصل'
    ],
    summary: [
        'اذكر سنوات خبرتك في بداية الملخص',
        'أضف أهم إنجازاتك المهنية'
    ]
};
```

## الأمان والخصوصية

### حماية البيانات
- جميع البيانات تُحفظ محلياً في المتصفح
- لا يتم إرسال البيانات إلى خوادم خارجية
- إمكانية حذف البيانات بالكامل

### التحقق من صحة البيانات
```javascript
isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

isProfessionalEmail(email) {
    const unprofessionalPatterns = [
        /\d{4,}/,  // أرقام كثيرة
        /(cool|hot|sexy|baby|love)/i
    ];
    
    return !unprofessionalPatterns.some(pattern => pattern.test(email));
}
```

## إمكانية الوصول

### دعم قارئات الشاشة
- استخدام العلامات الدلالية المناسبة
- إضافة `aria-labels` للعناصر التفاعلية
- دعم التنقل بلوحة المفاتيح

### دعم الحركة المحدودة
```css
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
```

## الأداء والتحسين

### تحسين الصور
- استخدام تنسيقات حديثة (WebP)
- ضغط الصور قبل الرفع
- تحسين أحجام الصور للعرض

### تحسين JavaScript
- تحميل الملفات بشكل غير متزامن
- تقسيم الكود إلى وحدات
- استخدام `requestAnimationFrame` للرسوم المتحركة

### تحسين CSS
- استخدام متغيرات CSS
- تجميع الخصائص المتشابهة
- تحسين الاستعلامات الإعلامية

## اختبار التطبيق

### اختبار الوظائف
1. إنشاء سيرة ذاتية جديدة
2. حفظ وتحميل البيانات
3. تصدير بصيغ مختلفة
4. اختبار المساعد الذكي

### اختبار التوافق
- المتصفحات الحديثة (Chrome, Firefox, Safari, Edge)
- الأجهزة المختلفة (Desktop, Tablet, Mobile)
- أنظمة التشغيل المختلفة

### اختبار الأداء
- سرعة التحميل
- استجابة الواجهة
- استهلاك الذاكرة

## التطوير المستقبلي

### ميزات مخططة
- تكامل مع LinkedIn API
- قوالب إضافية
- تحليلات متقدمة
- تطبيق الهاتف المحمول
- API للمطورين

### تحسينات تقنية
- استخدام Service Workers للعمل دون اتصال
- تحسين أداء الرسوم المتحركة
- دعم PWA (Progressive Web App)
- تحسين SEO
