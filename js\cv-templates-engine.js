/**
 * محرك قوالب السيرة الذاتية
 * نظام متقدم لعرض وتصدير قوالب السيرة الذاتية المختلفة
 */

class CVTemplatesEngine {
    constructor() {
        this.templates = {
            modern: this.modernTemplate,
            classic: this.classicTemplate,
            creative: this.creativeTemplate
        };
        
        this.currentZoom = 1;
        console.log('🎨 تم تهيئة محرك القوالب');
    }

    /**
     * عرض المعاينة في اللوحة الجانبية
     */
    renderPreview(cvData, templateName = 'modern') {
        const previewContainer = document.getElementById('cvPreview');
        if (!previewContainer) return;

        // إزالة العنصر النائب
        const placeholder = previewContainer.querySelector('.preview-placeholder');
        if (placeholder) {
            placeholder.remove();
        }

        // عرض القالب
        const templateFunction = this.templates[templateName];
        if (templateFunction) {
            previewContainer.innerHTML = templateFunction.call(this, cvData, true);
        } else {
            previewContainer.innerHTML = this.modernTemplate(cvData, true);
        }

        // تطبيق التكبير الحالي
        previewContainer.style.transform = `scale(${this.currentZoom})`;
    }

    /**
     * عرض المعاينة الكاملة
     */
    renderFullPreview(container, cvData, templateName = 'modern') {
        if (!container) return;

        const templateFunction = this.templates[templateName];
        if (templateFunction) {
            container.innerHTML = templateFunction.call(this, cvData, false);
        } else {
            container.innerHTML = this.modernTemplate(cvData, false);
        }
    }

    /**
     * القالب العصري
     */
    modernTemplate(cvData, isPreview = false) {
        const scaleClass = isPreview ? 'preview-scale' : 'full-scale';
        
        return `
            <div class="cv-document modern-template ${scaleClass}">
                ${this.renderModernHeader(cvData)}
                ${this.renderModernContent(cvData)}
            </div>
        `;
    }

    /**
     * رأس القالب العصري
     */
    renderModernHeader(cvData) {
        const personal = cvData.personal || {};
        
        return `
            <div class="modern-header">
                <div class="header-content">
                    ${personal.photo ? `
                        <div class="cv-photo">
                            <img src="${personal.photo}" alt="الصورة الشخصية">
                        </div>
                    ` : ''}
                    
                    <div class="header-info">
                        <h1 class="cv-name">${this.escapeHtml(personal.fullName || 'اسمك هنا')}</h1>
                        <h2 class="cv-title">${this.escapeHtml(personal.jobTitle || 'المسمى الوظيفي')}</h2>
                        
                        <div class="contact-info">
                            ${personal.email ? `
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <span>${this.escapeHtml(personal.email)}</span>
                                </div>
                            ` : ''}
                            
                            ${personal.phone ? `
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <span>${this.escapeHtml(personal.phone)}</span>
                                </div>
                            ` : ''}
                            
                            ${personal.city ? `
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>${this.escapeHtml(personal.city)}</span>
                                </div>
                            ` : ''}
                            
                            ${personal.website ? `
                                <div class="contact-item">
                                    <i class="fas fa-globe"></i>
                                    <span>${this.escapeHtml(personal.website)}</span>
                                </div>
                            ` : ''}
                            
                            ${personal.linkedin ? `
                                <div class="contact-item">
                                    <i class="fab fa-linkedin"></i>
                                    <span>LinkedIn</span>
                                </div>
                            ` : ''}
                            
                            ${personal.github ? `
                                <div class="contact-item">
                                    <i class="fab fa-github"></i>
                                    <span>GitHub</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * محتوى القالب العصري
     */
    renderModernContent(cvData) {
        return `
            <div class="modern-content">
                ${cvData.professionalSummary ? `
                    <div class="cv-section">
                        <h3 class="section-title">
                            <i class="fas fa-user"></i>
                            الملخص المهني
                        </h3>
                        <p class="summary-text">${this.escapeHtml(cvData.professionalSummary)}</p>
                    </div>
                ` : ''}
                
                ${this.renderExperiences(cvData.experiences)}
                ${this.renderEducation(cvData.education)}
                ${this.renderSkills(cvData)}
                ${this.renderLanguages(cvData.languages)}
            </div>
        `;
    }

    /**
     * القالب الكلاسيكي
     */
    classicTemplate(cvData, isPreview = false) {
        const scaleClass = isPreview ? 'preview-scale' : 'full-scale';
        
        return `
            <div class="cv-document classic-template ${scaleClass}">
                ${this.renderClassicHeader(cvData)}
                ${this.renderClassicContent(cvData)}
            </div>
        `;
    }

    /**
     * رأس القالب الكلاسيكي
     */
    renderClassicHeader(cvData) {
        const personal = cvData.personal || {};
        
        return `
            <div class="classic-header">
                <div class="header-main">
                    <h1 class="cv-name">${this.escapeHtml(personal.fullName || 'اسمك هنا')}</h1>
                    <h2 class="cv-title">${this.escapeHtml(personal.jobTitle || 'المسمى الوظيفي')}</h2>
                    
                    <div class="contact-grid">
                        ${personal.email ? `
                            <div class="contact-item">
                                <i class="fas fa-envelope"></i>
                                ${this.escapeHtml(personal.email)}
                            </div>
                        ` : ''}
                        
                        ${personal.phone ? `
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                ${this.escapeHtml(personal.phone)}
                            </div>
                        ` : ''}
                        
                        ${personal.city ? `
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                ${this.escapeHtml(personal.city)}
                            </div>
                        ` : ''}
                        
                        ${personal.website ? `
                            <div class="contact-item">
                                <i class="fas fa-globe"></i>
                                ${this.escapeHtml(personal.website)}
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                ${personal.photo ? `
                    <div class="classic-photo">
                        <img src="${personal.photo}" alt="الصورة الشخصية">
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * محتوى القالب الكلاسيكي
     */
    renderClassicContent(cvData) {
        return `
            <div class="classic-content">
                ${cvData.professionalSummary ? `
                    <div class="cv-section">
                        <h3 class="section-title">الملخص المهني</h3>
                        <p class="summary-text">${this.escapeHtml(cvData.professionalSummary)}</p>
                    </div>
                ` : ''}
                
                ${this.renderExperiences(cvData.experiences)}
                ${this.renderEducation(cvData.education)}
                ${this.renderSkills(cvData)}
                ${this.renderLanguages(cvData.languages)}
            </div>
        `;
    }

    /**
     * القالب الإبداعي
     */
    creativeTemplate(cvData, isPreview = false) {
        const scaleClass = isPreview ? 'preview-scale' : 'full-scale';
        
        return `
            <div class="cv-document creative-template ${scaleClass}">
                <div class="creative-layout">
                    ${this.renderCreativeSidebar(cvData)}
                    ${this.renderCreativeMain(cvData)}
                </div>
            </div>
        `;
    }

    /**
     * الشريط الجانبي للقالب الإبداعي
     */
    renderCreativeSidebar(cvData) {
        const personal = cvData.personal || {};
        
        return `
            <div class="creative-sidebar">
                ${personal.photo ? `
                    <div class="sidebar-photo">
                        <img src="${personal.photo}" alt="الصورة الشخصية">
                    </div>
                ` : ''}
                
                <div class="sidebar-info">
                    <h1 class="cv-name">${this.escapeHtml(personal.fullName || 'اسمك هنا')}</h1>
                    <h2 class="cv-title">${this.escapeHtml(personal.jobTitle || 'المسمى الوظيفي')}</h2>
                </div>
                
                <div class="sidebar-contact">
                    <h3>معلومات التواصل</h3>
                    ${personal.email ? `
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span>${this.escapeHtml(personal.email)}</span>
                        </div>
                    ` : ''}
                    
                    ${personal.phone ? `
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>${this.escapeHtml(personal.phone)}</span>
                        </div>
                    ` : ''}
                    
                    ${personal.city ? `
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>${this.escapeHtml(personal.city)}</span>
                        </div>
                    ` : ''}
                </div>
                
                ${this.renderSidebarSkills(cvData)}
                ${this.renderSidebarLanguages(cvData.languages)}
            </div>
        `;
    }

    /**
     * المحتوى الرئيسي للقالب الإبداعي
     */
    renderCreativeMain(cvData) {
        return `
            <div class="creative-main">
                ${cvData.professionalSummary ? `
                    <div class="cv-section">
                        <h3 class="section-title">الملخص المهني</h3>
                        <p class="summary-text">${this.escapeHtml(cvData.professionalSummary)}</p>
                    </div>
                ` : ''}
                
                ${this.renderExperiences(cvData.experiences)}
                ${this.renderEducation(cvData.education)}
            </div>
        `;
    }

    /**
     * عرض الخبرات العملية
     */
    renderExperiences(experiences) {
        if (!experiences || experiences.length === 0) return '';
        
        return `
            <div class="cv-section">
                <h3 class="section-title">
                    <i class="fas fa-briefcase"></i>
                    الخبرات العملية
                </h3>
                <div class="experiences-list">
                    ${experiences.map(exp => `
                        <div class="experience-item">
                            <div class="experience-header">
                                <h4 class="job-title">${this.escapeHtml(exp.jobTitle || '')}</h4>
                                <div class="duration">
                                    ${this.formatDate(exp.startDate)} - ${exp.current ? 'حتى الآن' : this.formatDate(exp.endDate)}
                                </div>
                            </div>
                            <div class="company-name">${this.escapeHtml(exp.company || '')}</div>
                            ${exp.description ? `
                                <p class="experience-description">${this.escapeHtml(exp.description)}</p>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * عرض التعليم
     */
    renderEducation(education) {
        if (!education || education.length === 0) return '';
        
        return `
            <div class="cv-section">
                <h3 class="section-title">
                    <i class="fas fa-graduation-cap"></i>
                    التعليم والمؤهلات
                </h3>
                <div class="education-list">
                    ${education.map(edu => `
                        <div class="education-item">
                            <div class="education-header">
                                <h4 class="degree">${this.escapeHtml(edu.degree || '')}</h4>
                                <div class="year">${this.escapeHtml(edu.year || '')}</div>
                            </div>
                            <div class="institution">${this.escapeHtml(edu.institution || '')}</div>
                            ${edu.gpa ? `
                                <div class="gpa">المعدل: ${this.escapeHtml(edu.gpa)}</div>
                            ` : ''}
                            ${edu.description ? `
                                <p class="education-description">${this.escapeHtml(edu.description)}</p>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * عرض المهارات
     */
    renderSkills(cvData) {
        const hasSkills = (cvData.technicalSkills && cvData.technicalSkills.length > 0) ||
                         (cvData.softSkills && cvData.softSkills.length > 0);
        
        if (!hasSkills) return '';
        
        return `
            <div class="cv-section">
                <h3 class="section-title">
                    <i class="fas fa-cogs"></i>
                    المهارات
                </h3>
                
                ${cvData.technicalSkills && cvData.technicalSkills.length > 0 ? `
                    <div class="skills-category">
                        <h4 class="skills-subtitle">المهارات التقنية</h4>
                        <div class="skills-list">
                            ${cvData.technicalSkills.map(skill => `
                                <span class="skill-tag technical">${this.escapeHtml(skill)}</span>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                
                ${cvData.softSkills && cvData.softSkills.length > 0 ? `
                    <div class="skills-category">
                        <h4 class="skills-subtitle">المهارات الشخصية</h4>
                        <div class="skills-list">
                            ${cvData.softSkills.map(skill => `
                                <span class="skill-tag soft">${this.escapeHtml(skill)}</span>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * عرض اللغات
     */
    renderLanguages(languages) {
        if (!languages || languages.length === 0) return '';
        
        return `
            <div class="cv-section">
                <h3 class="section-title">
                    <i class="fas fa-language"></i>
                    اللغات
                </h3>
                <div class="languages-list">
                    ${languages.map(lang => `
                        <div class="language-item">
                            <span class="language-name">${this.escapeHtml(lang.name || '')}</span>
                            <span class="language-level">${this.escapeHtml(lang.level || '')}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * عرض المهارات في الشريط الجانبي
     */
    renderSidebarSkills(cvData) {
        const hasSkills = (cvData.technicalSkills && cvData.technicalSkills.length > 0) ||
                         (cvData.softSkills && cvData.softSkills.length > 0);
        
        if (!hasSkills) return '';
        
        return `
            <div class="sidebar-section">
                <h3>المهارات</h3>
                ${cvData.technicalSkills && cvData.technicalSkills.length > 0 ? `
                    <div class="sidebar-skills">
                        ${cvData.technicalSkills.map(skill => `
                            <span class="sidebar-skill-tag">${this.escapeHtml(skill)}</span>
                        `).join('')}
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * عرض اللغات في الشريط الجانبي
     */
    renderSidebarLanguages(languages) {
        if (!languages || languages.length === 0) return '';
        
        return `
            <div class="sidebar-section">
                <h3>اللغات</h3>
                <div class="sidebar-languages">
                    ${languages.map(lang => `
                        <div class="sidebar-language">
                            <span class="lang-name">${this.escapeHtml(lang.name || '')}</span>
                            <span class="lang-level">${this.escapeHtml(lang.level || '')}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * تنسيق التاريخ
     */
    formatDate(dateString) {
        if (!dateString) return '';
        
        try {
            const date = new Date(dateString + '-01');
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            
            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            
            return `${monthNames[month - 1]} ${year}`;
        } catch (error) {
            return dateString;
        }
    }

    /**
     * تنظيف النص من HTML
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * تحديث مستوى التكبير
     */
    setZoom(zoomLevel) {
        this.currentZoom = zoomLevel;
        const previewContainer = document.getElementById('cvPreview');
        if (previewContainer) {
            previewContainer.style.transform = `scale(${zoomLevel})`;
        }
    }
}

console.log('🎨 تم تحميل محرك القوالب بنجاح');
