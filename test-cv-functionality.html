<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف السيرة الذاتية</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/cv-builder.css">
    <link rel="stylesheet" href="css/cv-templates.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow-md);
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .test-result {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .success { color: var(--success-color); }
        .error { color: var(--danger-color); }
        .info { color: var(--info-color); }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار وظائف السيرة الذاتية المحسنة</h1>
        
        <!-- Test Preview Functionality -->
        <div class="test-section">
            <h2><i class="fas fa-eye"></i> اختبار وظائف المعاينة</h2>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testPreviewInit()">
                    <i class="fas fa-play"></i> اختبار تهيئة المعاينة
                </button>
                <button class="btn btn-secondary" onclick="testRealTimeUpdate()">
                    <i class="fas fa-sync"></i> اختبار التحديث المباشر
                </button>
                <button class="btn btn-info" onclick="testTemplateSwitch()">
                    <i class="fas fa-exchange-alt"></i> اختبار تبديل القوالب
                </button>
                <button class="btn btn-success" onclick="testZoomControls()">
                    <i class="fas fa-search-plus"></i> اختبار التكبير/التصغير
                </button>
            </div>
            <div id="previewTestResult" class="test-result"></div>
        </div>

        <!-- Test PDF Export -->
        <div class="test-section">
            <h2><i class="fas fa-file-pdf"></i> اختبار تصدير PDF</h2>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testPDFExport()">
                    <i class="fas fa-download"></i> اختبار تصدير PDF
                </button>
                <button class="btn btn-secondary" onclick="testPDFQuality()">
                    <i class="fas fa-star"></i> اختبار جودة PDF
                </button>
                <button class="btn btn-info" onclick="testPDFSettings()">
                    <i class="fas fa-cog"></i> اختبار إعدادات PDF
                </button>
            </div>
            <div id="pdfTestResult" class="test-result"></div>
        </div>

        <!-- Test UI/UX Improvements -->
        <div class="test-section">
            <h2><i class="fas fa-palette"></i> اختبار تحسينات واجهة المستخدم</h2>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testModalPreview()">
                    <i class="fas fa-window-maximize"></i> اختبار المعاينة المنبثقة
                </button>
                <button class="btn btn-secondary" onclick="testResponsiveDesign()">
                    <i class="fas fa-mobile-alt"></i> اختبار التصميم المتجاوب
                </button>
                <button class="btn btn-info" onclick="testNotifications()">
                    <i class="fas fa-bell"></i> اختبار الإشعارات
                </button>
            </div>
            <div id="uiTestResult" class="test-result"></div>
        </div>

        <!-- Sample CV Data for Testing -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> بيانات تجريبية للاختبار</h2>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="loadSampleData()">
                    <i class="fas fa-upload"></i> تحميل بيانات تجريبية
                </button>
                <button class="btn btn-secondary" onclick="clearTestData()">
                    <i class="fas fa-trash"></i> مسح البيانات
                </button>
            </div>
            <div id="dataTestResult" class="test-result"></div>
        </div>

        <!-- Preview Container for Testing -->
        <div class="test-section">
            <h2><i class="fas fa-desktop"></i> معاينة مباشرة</h2>
            <div class="cv-preview-panel">
                <div class="preview-header">
                    <h3>معاينة السيرة الذاتية</h3>
                    <div class="preview-controls">
                        <button class="btn btn-sm" data-action="zoom-out" title="تصغير">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <span class="zoom-level">100%</span>
                        <button class="btn btn-sm" data-action="zoom-in" title="تكبير">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="btn btn-sm" data-action="zoom-reset" title="إعادة تعيين">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="previewCV()" title="معاينة كاملة">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>

                <!-- Template Selection -->
                <div class="template-selection">
                    <h4>اختر القالب</h4>
                    <div class="template-options">
                        <button class="template-btn active" data-template="1" title="قالب عصري">
                            <i class="fas fa-file-alt"></i>
                            <span>عصري</span>
                        </button>
                        <button class="template-btn" data-template="2" title="قالب كلاسيكي">
                            <i class="fas fa-file-text"></i>
                            <span>كلاسيكي</span>
                        </button>
                        <button class="template-btn" data-template="3" title="قالب إبداعي">
                            <i class="fas fa-file-image"></i>
                            <span>إبداعي</span>
                        </button>
                    </div>
                </div>
                
                <div class="preview-container">
                    <div class="cv-preview-document" id="cvPreview">
                        <div class="cv-page">
                            <!-- CV preview will be generated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- External Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>

    <!-- JavaScript Files -->
    <script src="js/cv-preview.js"></script>

    <script>
        // Test Functions
        function testPreviewInit() {
            const result = document.getElementById('previewTestResult');
            try {
                if (window.cvPreview) {
                    result.innerHTML = '<span class="success">✅ تم تهيئة معاين السيرة الذاتية بنجاح</span>';
                    result.innerHTML += '<br>الإصدار: ' + (window.cvPreview.constructor.name || 'CVPreview');
                } else {
                    result.innerHTML = '<span class="error">❌ فشل في تهيئة معاين السيرة الذاتية</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="error">❌ خطأ: ' + error.message + '</span>';
            }
        }

        function testRealTimeUpdate() {
            const result = document.getElementById('previewTestResult');
            try {
                // Simulate data update
                const testData = {
                    personal: {
                        fullName: 'أحمد محمد',
                        jobTitle: 'مطور ويب',
                        email: '<EMAIL>',
                        phone: '+966 50 123 4567'
                    },
                    summary: 'مطور ويب محترف مع خبرة 5 سنوات في تطوير التطبيقات'
                };

                const event = new CustomEvent('cvDataUpdated', { detail: testData });
                document.dispatchEvent(event);
                
                result.innerHTML = '<span class="success">✅ تم اختبار التحديث المباشر بنجاح</span>';
            } catch (error) {
                result.innerHTML = '<span class="error">❌ خطأ في التحديث المباشر: ' + error.message + '</span>';
            }
        }

        function testTemplateSwitch() {
            const result = document.getElementById('previewTestResult');
            try {
                if (window.cvPreview && window.cvPreview.switchTemplate) {
                    window.cvPreview.switchTemplate(2);
                    setTimeout(() => {
                        window.cvPreview.switchTemplate(1);
                        result.innerHTML = '<span class="success">✅ تم اختبار تبديل القوالب بنجاح</span>';
                    }, 1000);
                } else {
                    result.innerHTML = '<span class="error">❌ وظيفة تبديل القوالب غير متاحة</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="error">❌ خطأ في تبديل القوالب: ' + error.message + '</span>';
            }
        }

        function testZoomControls() {
            const result = document.getElementById('previewTestResult');
            try {
                if (window.cvPreview) {
                    window.cvPreview.zoomIn();
                    setTimeout(() => {
                        window.cvPreview.zoomOut();
                        setTimeout(() => {
                            window.cvPreview.resetZoom();
                            result.innerHTML = '<span class="success">✅ تم اختبار التكبير/التصغير بنجاح</span>';
                        }, 500);
                    }, 500);
                } else {
                    result.innerHTML = '<span class="error">❌ وظائف التكبير/التصغير غير متاحة</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="error">❌ خطأ في التكبير/التصغير: ' + error.message + '</span>';
            }
        }

        function testPDFExport() {
            const result = document.getElementById('pdfTestResult');
            try {
                if (typeof window.exportToPDF === 'function') {
                    result.innerHTML = '<span class="info">🔄 جاري اختبار تصدير PDF...</span>';
                    // Note: This would actually trigger a download
                    result.innerHTML = '<span class="success">✅ وظيفة تصدير PDF متاحة</span>';
                } else {
                    result.innerHTML = '<span class="error">❌ وظيفة تصدير PDF غير متاحة</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="error">❌ خطأ في تصدير PDF: ' + error.message + '</span>';
            }
        }

        function testModalPreview() {
            const result = document.getElementById('uiTestResult');
            try {
                if (window.previewCV) {
                    window.previewCV();
                    result.innerHTML = '<span class="success">✅ تم فتح المعاينة المنبثقة بنجاح</span>';
                } else {
                    result.innerHTML = '<span class="error">❌ وظيفة المعاينة المنبثقة غير متاحة</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="error">❌ خطأ في المعاينة المنبثقة: ' + error.message + '</span>';
            }
        }

        function loadSampleData() {
            const result = document.getElementById('dataTestResult');
            const sampleData = {
                personal: {
                    fullName: 'أحمد محمد علي',
                    jobTitle: 'مطور تطبيقات الويب',
                    email: '<EMAIL>',
                    phone: '+966 50 123 4567',
                    city: 'الرياض، السعودية'
                },
                summary: 'مطور ويب محترف مع أكثر من 5 سنوات من الخبرة في تطوير التطبيقات الحديثة باستخدام أحدث التقنيات.',
                experience: [
                    {
                        jobTitle: 'مطور ويب أول',
                        company: 'شركة التقنية المتقدمة',
                        startDate: '2020',
                        endDate: 'حتى الآن',
                        description: 'تطوير وصيانة تطبيقات الويب باستخدام React و Node.js'
                    }
                ],
                education: [
                    {
                        degree: 'بكالوريوس علوم الحاسب',
                        institution: 'جامعة الملك سعود',
                        year: '2019',
                        gpa: '3.8'
                    }
                ],
                skills: {
                    technical: ['JavaScript', 'React', 'Node.js', 'Python', 'SQL'],
                    soft: ['العمل الجماعي', 'القيادة', 'حل المشكلات']
                },
                languages: [
                    { name: 'العربية', level: 'اللغة الأم' },
                    { name: 'الإنجليزية', level: 'متقدم' }
                ]
            };

            localStorage.setItem('elashrafy_cv_data', JSON.stringify(sampleData));
            
            // Trigger update
            const event = new CustomEvent('cvDataUpdated', { detail: sampleData });
            document.dispatchEvent(event);
            
            result.innerHTML = '<span class="success">✅ تم تحميل البيانات التجريبية بنجاح</span>';
        }

        function clearTestData() {
            localStorage.removeItem('elashrafy_cv_data');
            document.getElementById('dataTestResult').innerHTML = '<span class="info">🗑️ تم مسح البيانات</span>';
        }

        // Initialize tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testPreviewInit();
                loadSampleData();
            }, 1000);
        });
    </script>
</body>
</html>
