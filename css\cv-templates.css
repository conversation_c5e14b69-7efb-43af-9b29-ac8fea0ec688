/* ===== Enhanced CV Templates Styles ===== */

/* ===== Modern Template ===== */
.modern-header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 30px;
    border-radius: 12px 12px 0 0;
    margin: -40px -40px 30px -40px;
}

.modern-header .cv-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.3);
    flex-shrink: 0;
}

.modern-header .cv-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.modern-header .photo-placeholder {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    border: none;
}

.modern-header .cv-name {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-header .cv-title {
    font-size: 1.3rem;
    font-weight: 400;
    margin-bottom: 20px;
    opacity: 0.9;
}

.modern-header .cv-contact {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.modern-header .contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
}

.modern-header .contact-item i {
    width: 20px;
    text-align: center;
    opacity: 0.8;
}

.modern-content .cv-section {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-card);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.modern-content .section-title {
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modern-content .section-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* ===== Classic Template ===== */
.classic-header {
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.classic-header .header-content {
    flex: 1;
}

.classic-header .cv-name {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.classic-header .cv-title {
    font-size: 1.2rem;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 16px;
}

.classic-contact {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.classic-contact .contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.classic-contact .contact-item i {
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

.classic-photo {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid var(--bg-secondary);
    flex-shrink: 0;
}

.classic-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.classic-content .cv-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--bg-secondary);
}

.classic-content .cv-section:last-child {
    border-bottom: none;
}

.classic-content .section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== Creative Template ===== */
.creative-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 0;
    min-height: 600px;
}

.creative-sidebar {
    background: linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%);
    color: white;
    padding: 30px 25px;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.creative-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.3);
    margin: 0 auto;
}

.creative-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.creative-photo .photo-placeholder {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    border: none;
    width: 100%;
    height: 100%;
}

.creative-sidebar .cv-name {
    font-size: 1.8rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 8px;
}

.creative-sidebar .cv-title {
    font-size: 1.1rem;
    text-align: center;
    opacity: 0.9;
    margin-bottom: 20px;
}

.creative-contact .contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 0.9rem;
}

.creative-contact .contact-item i {
    width: 20px;
    text-align: center;
    opacity: 0.8;
}

.creative-sidebar .sidebar-section {
    margin-bottom: 20px;
}

.creative-sidebar .section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.creative-main {
    padding: 30px;
    background: var(--bg-card);
}

.creative-main .cv-section {
    margin-bottom: 25px;
}

.creative-main .section-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 16px;
    position: relative;
    padding-left: 20px;
}

.creative-main .section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
}

/* ===== Common Section Styles ===== */
.cv-experience-item,
.cv-education-item {
    margin-bottom: 20px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.job-title,
.degree {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.company,
.institution {
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.duration {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.description {
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-secondary);
    margin: 0;
}

.cv-skills-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
}

.cv-skill-item {
    background: var(--primary-color);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.cv-skill-item.soft-skill {
    background: var(--secondary-color);
}

.creative-sidebar .cv-skill-item {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.cv-language-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--bg-secondary);
}

.cv-language-item:last-child {
    border-bottom: none;
}

.language-name {
    font-weight: 500;
    color: var(--text-primary);
}

.language-level {
    font-size: 0.85rem;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: 4px 8px;
    border-radius: 12px;
}

.creative-sidebar .cv-language-item {
    border-bottom-color: rgba(255, 255, 255, 0.2);
}

.creative-sidebar .language-name {
    color: white;
}

.creative-sidebar .language-level {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .creative-layout {
        grid-template-columns: 1fr;
    }
    
    .creative-sidebar {
        order: 2;
        padding: 20px;
    }
    
    .creative-main {
        order: 1;
        padding: 20px;
    }
    
    .modern-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .classic-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .classic-contact {
        grid-template-columns: 1fr;
        text-align: center;
    }
}
